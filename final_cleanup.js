// 最终清理脚本 - 处理剩余的变量名和格式问题
const fs = require('fs');

console.log('🔧 开始最终清理...');

let content = fs.readFileSync('main_cleaned.js', 'utf8');

// 1. 修复一些遗漏的十六进制数字
const remainingHexFixes = [
    [/3b/g, '59'],  // 修复遗漏的 3b
    [/3c/g, '60'],  // 60秒
    [/3e8/g, '1000'], // 1000毫秒
];

for (const [pattern, replacement] of remainingHexFixes) {
    content = content.replace(pattern, replacement);
}

// 2. 重命名更多有意义的变量
const moreVariableRenames = [
    // 函数参数和局部变量
    [/_59d662/g, 'apiIndex'],
    [/_57b2e7/g, 'apiIndex'],
    [/_160780/g, 'apiIndex'],
    [/_44db88/g, 'tryCount'],
    [/_44d8f6/g, 'cookieIndex'],
    [/_315c72/g, 'apiIndex'],
    [/_209282/g, 'apiIndex'],
    [/_1f66b9/g, 'cookieIndex'],
    [/_56778b/g, 'apiIndex'],
    [/_23696c/g, 'cookieIndex'],
    [/_5e747e/g, 'apiIndex'],
    
    // 常用变量
    [/_35647f/g, 'targetCookieIndex'],
    [/_513bfb/g, 'username'],
    [/_3b8112/g, 'username'],
    [/_31eecc/g, 'username'],
    [/_18ba0d/g, 'username'],
    [/_1d34e9/g, 'successMessage'],
    [/_43b1ec/g, 'currentDate'],
    [/_5a99f8/g, 'waitTime'],
    [/_2d60f1/g, 'currentMinute'],
    
    // API相关变量
    [/_118d4c/g, 'getApiConfig'],
    [/_3e42d3/g, 'getError'],
    [/_3a7eee/g, 'getResponse'],
    [/_256d53/g, 'getBody'],
    [/_109f8e8/g, 'postError'],
    [/_353712/g, 'postResponse'],
    [/_250069/g, 'postBody'],
    [/_496286/g, 'resolve'],
    [/_21ee15/g, 'resolve'],
    [/_2e7080/g, 'error'],
    [/_1522aa/g, 'response'],
    [/_1dbcae/g, 'body'],
    [/_4d74f8/g, 'decryptedUrl'],
    
    // 业务逻辑变量
    [/_2cf1c0/g, 'message'],
    [/_3d7c3d/g, 'subCodeWithPipes'],
    [/_3d720a/g, 'couponInfo'],
    [/_5c25bd/g, 'targetHour'],
    [/_2776bb/g, 'timeArray'],
    [/_27a404/g, 'shouldRemove'],
    [/_248875/g, 'removeIndex'],
    [/_3a4043/g, 'successIndex'],
    
    // 其他变量
    [/_55d971/g, 'min'],
    [/_325d4e/g, 'max'],
    [/_5d5c5b/g, 'args'],
    [/_5f49a3/g, 'args'],
    [/_49eebc/g, 'formattedMessage'],
    [/_4ecf92/g, 'formattedMessage'],
    
    // 错误变量统一
    [/_32f029/g, 'error'],
    [/_3c07a2/g, 'error'],
    [/_1210c3/g, 'error'],
    [/_5b94da/g, 'error'],
    [/_550b75/g, 'error'],
    [/_2aab18/g, 'error'],
    [/_29f0f3/g, 'error'],
    [/_279aaf/g, 'error'],
    [/_5206ce/g, 'error'],
    [/_4ea171/g, 'error']
];

for (const [pattern, replacement] of moreVariableRenames) {
    content = content.replace(pattern, replacement);
}

// 3. 修复一些格式问题
content = content.replace(/return !\[\];/g, 'return false;');
content = content.replace(/= !\[\];/g, '= false;');
content = content.replace(/= !!\[\];/g, '= true;');
content = content.replace(/!!\[\]/g, 'true');
content = content.replace(/!\[\]/g, 'false');

// 4. 修复一些遗漏的属性访问
content = content.replace(/global\['window'\]/g, 'global.window');
content = content.replace(/global\['document'\]/g, 'global.document');
content = content.replace(/global\['Element'\]/g, 'global.Element');

// 5. 添加更好的注释和格式
const betterComments = [
    ['// 解码后的 main.min.js', '// 京东优惠券自动领取脚本 - 已解码清理'],
    ['let tryNum = 4;', '// 配置参数\nlet tryNum = 4; // 重试次数'],
    ['let maxQq = 14;', 'let maxQq = 14; // 最大优惠券数量'],
    ['let maxXc = 3;', 'let maxXc = 3; // 最大并发数'],
    ['let qqjgTime = 250;', 'let qqjgTime = 250; // 请求间隔时间(ms)'],
    ['let maxAccount = 8;', 'let maxAccount = 8; // 最大账号数'],
    ['let ycTime = 64;', 'let ycTime = 64; // 延迟时间'],
    ['let cookiesArr = [],', '// 全局变量\nlet cookiesArr = [], // Cookie数组'],
    ['let canTaskFlag = [];', 'let canTaskFlag = []; // 任务标志'],
    ['let TgCkArray = [];', 'let TgCkArray = []; // 跳过的Cookie索引'],
    ['let lqSucArray = [];', 'let lqSucArray = []; // 领取成功的用户'],
    ['let AllEendCode = \'|A9|A6|A14|D2|\';', 'let AllEendCode = \'|A9|A6|A14|D2|\'; // 全部结束代码'],
    ['let PEendCode = \'|A1|A12|A13|A19|A26|\';', 'let PEendCode = \'|A1|A12|A13|A19|A26|\'; // 部分结束代码']
];

for (const [pattern, replacement] of betterComments) {
    content = content.replace(pattern, replacement);
}

// 6. 清理多余的空行和格式
content = content.replace(/\n\n\n+/g, '\n\n');
content = content.replace(/\s+\n/g, '\n');

// 保存最终清理的文件
fs.writeFileSync('main_final.js', content);

console.log('✨ 最终清理完成！');
console.log('📁 最终文件已保存为: main_final.js');
console.log('📏 最终文件大小:', content.length, '字符');

// 显示清理进度
const originalSize = fs.readFileSync('main.min.js', 'utf8').length;
const finalSize = content.length;
const reduction = ((originalSize - finalSize) / originalSize * 100).toFixed(1);

console.log('\n📊 总体优化效果:');
console.log(`   原始文件: ${originalSize} 字符`);
console.log(`   最终文件: ${finalSize} 字符`);
console.log(`   减少: ${originalSize - finalSize} 字符 (${reduction}%)`);

console.log('\n🎯 主要改进:');
console.log('   ✅ 解码了所有混淆字符串');
console.log('   ✅ 替换了方括号属性访问');
console.log('   ✅ 转换了十六进制数字');
console.log('   ✅ 重命名了有意义的变量');
console.log('   ✅ 修复了布尔值表达式');
console.log('   ✅ 添加了注释说明');
