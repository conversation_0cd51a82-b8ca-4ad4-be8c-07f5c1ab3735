{"name": "http-cookie-agent", "version": "7.0.1", "description": "Allows cookies with every Node.js HTTP clients.", "keywords": ["agent", "axios", "cookies", "fetch", "got", "http", "https", "needle", "node-fetch", "phin", "request", "superagent", "tough-cookie", "urllib", "undici"], "homepage": "https://github.com/3846masa/http-cookie-agent#readme", "bugs": {"url": "https://github.com/3846masa/http-cookie-agent/issues"}, "repository": {"type": "git", "url": "https://github.com/3846masa/http-cookie-agent.git"}, "funding": "https://github.com/sponsors/3846masa", "license": "MIT", "author": "3846masa <3846mas<PERSON><EMAIL>>", "exports": {"./http": "./http/index.js", "./undici": "./undici/index.js", "./undici/v6": "./undici/v6/index.js"}, "files": ["dist", "undici", "http", "!**/__tests__"], "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "pnpm run --parallel \"/^build:.*/\"", "build:cjs": "babel src --out-dir dist --extensions .ts --out-file-extension .js", "build:mjs": "babel src --out-dir dist --extensions .mts --out-file-extension .mjs", "format": "pnpm run --sequential \"/^format:.*/\"", "format:eslint": "eslint --fix .", "format:prettier": "prettier --write .", "lint": "pnpm run \"/^lint:.*/\"", "lint:dts": "tsc --noEmit http/*.d.ts undici/*.d.ts", "lint:eslint": "eslint --max-warnings 0 .", "lint:prettier": "prettier --check .", "lint:tsc": "tsc --noEmit", "semantic-release": "semantic-release", "test": "NODE_OPTIONS=\"--experimental-vm-modules\" jest"}, "dependencies": {"agent-base": "^7.1.3"}, "devDependencies": {"@3846masa/configs": "github:3846masa/configs#e259de36675e7ccd48a0285d325cb9be710f4370", "@babel/cli": "7.27.0", "@babel/core": "7.26.10", "@babel/plugin-proposal-explicit-resource-management": "7.25.9", "@babel/preset-env": "7.26.9", "@babel/preset-typescript": "7.27.0", "@hapi/wreck": "18.1.0", "@jest/globals": "29.7.0", "@semantic-release/changelog": "6.0.3", "@semantic-release/git": "10.0.1", "@types/babel__core": "7.20.5", "@types/needle": "3.3.0", "@types/node": "20.17.30", "@types/request": "2.48.12", "@types/semver": "7.7.0", "@types/superagent": "8.1.9", "agentkeepalive": "4.6.0", "axios": "1.8.4", "babel-jest": "29.7.0", "disposablestack": "1.1.7", "got": "14.4.7", "http-proxy-agent": "7.0.2", "jest": "29.7.0", "needle": "3.3.1", "node-fetch": "3.3.2", "phin": "3.7.1", "proxy": "2.2.0", "request": "2.88.2", "rimraf": "6.0.1", "semantic-release": "24.2.3", "semver": "7.7.1", "superagent": "10.2.0", "tough-cookie": "5.1.2", "typescript": "5.8.3", "undici": "7.8.0", "undici__v6": "npm:undici@6.21.2", "urllib": "4.6.11"}, "peerDependencies": {"tough-cookie": "^4.0.0 || ^5.0.0", "undici": "^7.0.0"}, "peerDependenciesMeta": {"undici": {"optional": true}}, "packageManager": "pnpm@10.8.0", "engines": {"node": ">=20.0.0"}, "pnpm": {"overrides": {"urllib>undici": "$undici"}, "patchedDependencies": {"@semantic-release/git@10.0.1": "patches/@<EMAIL>"}}}