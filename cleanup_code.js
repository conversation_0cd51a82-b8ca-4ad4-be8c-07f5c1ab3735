// 进一步清理解码后的代码
const fs = require('fs');

console.log('🧹 开始清理代码...');

// 读取解码后的文件
let content = fs.readFileSync('main_deobfuscated.js', 'utf8');

console.log('📝 原始文件大小:', content.length, '字符');

// 1. 替换方括号属性访问为点号访问
console.log('🔧 修复属性访问方式...');
const propertyReplacements = [
    // 常见的属性访问
    [/console\['log'\]/g, 'console.log'],
    [/console\['error'\]/g, 'console.error'],
    [/\$\['post'\]/g, '$.post'],
    [/\$\['get'\]/g, '$.get'],
    [/\$\['wait'\]/g, '$.wait'],
    [/\$\['getdata'\]/g, '$.getdata'],
    [/\$\['getjson'\]/g, '$.getjson'],
    [/\$\['setjson'\]/g, '$.setjson'],
    [/\$\['logErr'\]/g, '$.logErr'],
    [/\$\['CryptoJS'\]/g, '$.CryptoJS'],
    [/Math\['min'\]/g, 'Math.min'],
    [/Math\['max'\]/g, 'Math.max'],
    [/Math\['floor'\]/g, 'Math.floor'],
    [/Math\['random'\]/g, 'Math.random'],
    [/Math\['abs'\]/g, 'Math.abs'],
    [/JSON\['parse'\]/g, 'JSON.parse'],
    [/JSON\['stringify'\]/g, 'JSON.stringify'],
    [/fs\['readFileSync'\]/g, 'fs.readFileSync'],
    [/fs\['existsSync'\]/g, 'fs.existsSync'],
    [/fs\['mkdirSync'\]/g, 'fs.mkdirSync'],
    [/fs\['appendFileSync'\]/g, 'fs.appendFileSync'],
    [/path\['join'\]/g, 'path.join'],
    [/util\['format'\]/g, 'util.format'],
    [/cron\['schedule'\]/g, 'cron.schedule'],
    [/window\['Element'\]/g, 'window.Element'],
    [/dom\['window'\]/g, 'dom.window'],
    [/window\['document'\]/g, 'window.document'],
    
    // 数组和对象属性
    [/\['length'\]/g, '.length'],
    [/\['push'\]/g, '.push'],
    [/\['includes'\]/g, '.includes'],
    [/\['indexOf'\]/g, '.indexOf'],
    [/\['split'\]/g, '.split'],
    [/\['join'\]/g, '.join'],
    [/\['match'\]/g, '.match'],
    [/\['replace'\]/g, '.replace'],
    [/\['substr'\]/g, '.substr'],
    [/\['substring'\]/g, '.substring'],
    [/\['toLowerCase'\]/g, '.toLowerCase'],
    [/\['toString'\]/g, '.toString'],
    [/\['slice'\]/g, '.slice'],
    [/\['concat'\]/g, '.concat'],
    [/\['apply'\]/g, '.apply'],
    [/\['then'\]/g, '.then'],
    [/\['catch'\]/g, '.catch'],
    
    // 日期方法
    [/\['getTime'\]/g, '.getTime'],
    [/\['getDate'\]/g, '.getDate'],
    [/\['getHours'\]/g, '.getHours'],
    [/\['getMinutes'\]/g, '.getMinutes'],
    [/\['getSeconds'\]/g, '.getSeconds'],
    [/\['getMilliseconds'\]/g, '.getMilliseconds'],
    [/\['getFullYear'\]/g, '.getFullYear'],
    [/\['getMonth'\]/g, '.getMonth'],
    [/\['getDay'\]/g, '.getDay'],
    
    // 业务相关属性
    [/\['qName'\]/g, '.qName'],
    [/\['qApi'\]/g, '.qApi'],
    [/\['qTime'\]/g, '.qTime'],
    [/\['endDate'\]/g, '.endDate'],
    [/\['ckIndex'\]/g, '.ckIndex'],
    [/\['lqSpace'\]/g, '.lqSpace'],
    [/\['subCode'\]/g, '.subCode'],
    [/\['subCodeMsg'\]/g, '.subCodeMsg'],
    [/\['resultData'\]/g, '.resultData'],
    [/\['code'\]/g, '.code'],
    [/\['msg'\]/g, '.msg'],
    [/\['currentTime2'\]/g, '.currentTime2'],
    
    // 配置相关
    [/\['JD_COOKIE'\]/g, '.JD_COOKIE'],
    [/\['YHQ_REMOVE'\]/g, '.YHQ_REMOVE'],
    [/\['YHQ_NOWRUN'\]/g, '.YHQ_NOWRUN'],
    [/\['YHQ_API'\]/g, '.YHQ_API'],
    [/\['apiList'\]/g, '.apiList']
];

for (const [pattern, replacement] of propertyReplacements) {
    content = content.replace(pattern, replacement);
}

// 2. 替换十六进制数字为十进制
console.log('🔢 转换十六进制数字...');
const hexReplacements = [
    [/0x0/g, '0'],
    [/0x1/g, '1'],
    [/0x2/g, '2'],
    [/0x3/g, '3'],
    [/0x4/g, '4'],
    [/0x5/g, '5'],
    [/0x6/g, '6'],
    [/0xa/g, '10'],
    [/0x14/g, '20'],
    [/0x1e/g, '30'],
    [/0x3b/g, '59'],
    [/0x3c/g, '60'],
    [/0x3e8/g, '1000'],
    [/0xfa/g, '250'],
    [/0x8/g, '8']
];

for (const [pattern, replacement] of hexReplacements) {
    content = content.replace(pattern, replacement);
}

// 3. 重命名有意义的变量名
console.log('📝 重命名变量...');
const variableReplacements = [
    // 函数参数重命名
    [/_0x315c72/g, 'apiIndex'],
    [/_0x1f66b9/g, 'cookieIndex'],
    [/_0x209282/g, 'apiIndex'],
    [/_0x56778b/g, 'apiIndex'],
    [/_0x23696c/g, 'cookieIndex'],
    [/_0x5e747e/g, 'apiIndex'],
    [/_0xc808e0/g, 'cookieIndex'],
    [/_0x44db88/g, 'tryCount'],
    [/_0x44d8f6/g, 'cookieIndex'],
    [/_0x57b2e7/g, 'apiIndex'],
    [/_0x160780/g, 'apiIndex'],
    [/_0x59d662/g, 'apiIndex'],
    [/_0x3a4043/g, 'successIndex'],
    [/_0x248875/g, 'removeIndex'],
    
    // 常见变量重命名
    [/_0x35647f/g, 'targetCookieIndex'],
    [/_0x513bfb/g, 'username'],
    [/_0x3b8112/g, 'username'],
    [/_0x31eecc/g, 'username'],
    [/_0x18ba0d/g, 'username'],
    [/_0x1d34e9/g, 'successMessage'],
    [/_0x43b1ec/g, 'currentDate'],
    [/_0x5a99f8/g, 'waitTime'],
    [/_0x2d60f1/g, 'currentMinute'],
    [/_0x5b94da/g, 'error'],
    [/_0x550b75/g, 'error'],
    [/_0x2aab18/g, 'error'],
    [/_0x32f029/g, 'error'],
    [/_0x3c07a2/g, 'error'],
    [/_0x1210c3/g, 'error'],
    [/_0x29f0f3/g, 'error'],
    [/_0x761396/g, 'error'],
    [/_0x279aaf/g, 'error'],
    [/_0x5206ce/g, 'error'],
    [/_0x4ea171/g, 'error'],
    
    // API 相关变量
    [/_0x118d4c/g, 'getApiConfig'],
    [/_0xf78332/g, 'postApiConfig'],
    [/_0x3e42d3/g, 'getError'],
    [/_0x3a7eee/g, 'getResponse'],
    [/_0x256d53/g, 'getBody'],
    [/_0xa9f8e8/g, 'postError'],
    [/_0x353712/g, 'postResponse'],
    [/_0x250069/g, 'postBody'],
    [/_0x496286/g, 'resolve'],
    [/_0x21ee15/g, 'resolve'],
    [/_0x2e7080/g, 'error'],
    [/_0x1522aa/g, 'response'],
    [/_0x1dbcae/g, 'body'],
    [/_0xcfebdf/g, 'decryptedUrl'],
    [/_0x4d74f8/g, 'decryptedUrl'],
    
    // 其他变量
    [/_0x2cf1c0/g, 'message'],
    [/_0x3d7c3d/g, 'subCodeWithPipes'],
    [/_0x3d720a/g, 'couponInfo'],
    [/_0x5c25bd/g, 'targetHour'],
    [/_0x2776bb/g, 'timeArray'],
    [/_0x27a404/g, 'shouldRemove'],
    [/_0xee10e3/g, 'couponInfo'],
    [/_0x55d971/g, 'min'],
    [/_0x325d4e/g, 'max'],
    [/_0x5d5c5b/g, 'args'],
    [/_0x5f49a3/g, 'args'],
    [/_0x49eebc/g, 'formattedMessage'],
    [/_0x4ecf92/g, 'formattedMessage']
];

for (const [pattern, replacement] of variableReplacements) {
    content = content.replace(pattern, replacement);
}

// 4. 清理一些格式问题
console.log('🎨 优化代码格式...');
content = content.replace(/\n\n\n+/g, '\n\n'); // 移除多余的空行
content = content.replace(/\s+\n/g, '\n'); // 移除行尾空格

// 5. 保存清理后的文件
fs.writeFileSync('main_cleaned.js', content);

console.log('✨ 清理完成！');
console.log('📁 清理后的文件已保存为: main_cleaned.js');
console.log('📏 清理后文件大小:', content.length, '字符');
console.log('📊 减少了', fs.readFileSync('main_deobfuscated.js', 'utf8').length - content.length, '字符');

// 显示一些清理示例
console.log('\n📋 清理示例:');
console.log('   console[\'log\'] → console.log');
console.log('   Math[\'random\'] → Math.random');
console.log('   0x1 → 1');
console.log('   _0x315c72 → apiIndex');
console.log('   _0x1f66b9 → cookieIndex');
