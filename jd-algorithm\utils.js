/**
 * 基础工具函数
 * 从TypeScript版本转换而来的JavaScript实现
 */

/**
 * 将字符串进行 URL 安全的 Base64 解码
 */
function decodeBase64URL(encodedString) {
  return (encodedString + '===')
    .slice(0, encodedString.length + 3 - ((encodedString.length + 3) % 4))
    .replace(/-/g, '+')
    .replace(/_/g, '/');
}

/**
 * 获取随机字符串
 */
function getRandomIDPro({ size = 10, dictType = 'number', customDict } = {}) {
  let random = '';
  if (!customDict) {
    switch (dictType) {
      case 'alphabet':
        customDict = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        break;
      case 'max':
        customDict = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ_-';
        break;
      default:
        customDict = '0123456789';
    }
  }
  for (; size--; ) random += customDict[(Math.random() * customDict.length) | 0];
  return random;
}

/**
 * 从数组中随机选择一个元素
 */
function getRandomElement(arr) {
  const randomIndex = Math.floor(Math.random() * arr.length);
  return arr[randomIndex];
}

/**
 * 获取随机设备信息
 */
function getRandomDevice() {
  const DEVICE_LISTS = {
    products: [
      {
        value: 'Xiaomi',
        models: [
          { value: 'MI 6' },
          { value: 'MI 8' },
          { value: 'MI 9' },
          { value: 'MI 10' },
          { value: 'MI 11' },
          { value: 'Redmi Note 8' },
          { value: 'Redmi Note 9' }
        ]
      },
      {
        value: 'HUAWEI',
        models: [
          { value: 'P30' },
          { value: 'P40' },
          { value: 'Mate 30' },
          { value: 'Mate 40' },
          { value: 'nova 7' }
        ]
      },
      {
        value: 'OPPO',
        models: [
          { value: 'Find X2' },
          { value: 'Find X3' },
          { value: 'Reno4' },
          { value: 'Reno5' }
        ]
      },
      {
        value: 'vivo',
        models: [
          { value: 'X60' },
          { value: 'X70' },
          { value: 'S9' },
          { value: 'S10' }
        ]
      }
    ]
  };

  const product_index = Math.floor(Math.random() * DEVICE_LISTS.products.length);
  const model_index = Math.floor(Math.random() * DEVICE_LISTS.products[product_index].models.length);

  const brand = DEVICE_LISTS.products[product_index].value;
  const model = DEVICE_LISTS.products[product_index].models[model_index].value;

  return { brand, model };
}

/**
 * 将一个标准的 Base64 编码的字符串转换成 URL 安全的 Base64 编码
 */
function fromBase64(str) {
  return str.replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
}

/**
 * 是否为一个普通的 JavaScript 对象
 */
function isPlainObject(params) {
  return '[object Object]' === Object.prototype.toString.call(params);
}

/**
 * 判断一个对象是否为空
 */
function isEmpty(params) {
  return isPlainObject(params) && !Object.keys(params).length;
}

/**
 * 判断参数是否包含保留参数名
 */
function containsReservedParamName(params) {
  const reservedParams = ['h5st', '_stk', '_ste'];
  const paramKeys = Object.keys(params);

  for (const key of paramKeys) {
    if (reservedParams.includes(key)) {
      return true;
    }
  }
  return false;
}

/**
 * 判断是否为null或undefined
 */
function isNullOrUndefined(value) {
  return value === null || value === undefined;
}

/**
 * 判断参数值是否安全
 */
function isSafeParamValue(value) {
  if (typeof value === 'string') {
    return value.length <= 1000; // 限制字符串长度
  }
  return true;
}

/**
 * 格式化日期
 */
function formatDate(timestamp = Date.now(), pattern = 'yyyy-MM-dd') {
  const date = new Date(timestamp);
  const map = {
    'M+': date.getMonth() + 1, // 月份
    'd+': date.getDate(), // 日
    'D+': date.getDate(), // 日
    'h+': date.getHours(), // 小时
    'H+': date.getHours(), // 小时
    'm+': date.getMinutes(), // 分
    's+': date.getSeconds(), // 秒
    'w+': date.getDay(), // 星期
    'q+': Math.floor((date.getMonth() + 3) / 3), // 季度
    'S+': date.getMilliseconds(), // 毫秒
  };

  // 年份处理
  if (/(y+)/i.test(pattern)) {
    pattern = pattern.replace(/(y+)/i, (match) => {
      return (date.getFullYear() + '').slice(4 - match.length);
    });
  }

  // 其他部分处理
  for (const key in map) {
    if (new RegExp(`(${key})`).test(pattern)) {
      pattern = pattern.replace(new RegExp(`(${key})`), (match) => {
        const value = map[key];
        return match.length === 1 ? value.toString() : ('000' + value).slice(-match.length);
      });
    }
  }

  return pattern;
}

/**
 * 从源数组中随机选择指定数量的元素
 */
function selectRandomElements(sourceArray, requiredCount) {
  requiredCount = Math.min(requiredCount, sourceArray.length);

  let remainingElements = sourceArray.length;
  const selectedElements = [];
  const iterator = sourceArray.split('');
  
  for (const element of iterator) {
    if (Math.random() * remainingElements < requiredCount) {
      selectedElements.push(element);
      if (--requiredCount === 0) {
        break;
      }
    }
    remainingElements--;
  }
  
  let result = '';
  for (let index = 0; index < selectedElements.length; index++) {
    const P = (Math.random() * (selectedElements.length - index)) | 0;
    result += selectedElements[P];
    selectedElements[P] = selectedElements[selectedElements.length - index - 1];
  }
  return result;
}

/**
 * 生成一个0到9之间的随机整数
 */
function getRandomInt10() {
  return (10 * Math.random()) | 0;
}

/**
 * 过滤字符串中的指定字符
 */
function filterCharactersFromString(sourceString, charactersToFilter) {
  return sourceString.split('').filter(char => !charactersToFilter.includes(char)).join('');
}

/**
 * Uint8Array 转换为一个十六进制字符串表示
 */
function toHexString(byteArray) {
  return Array.from(byteArray)
    .map((byte) => {
      const hex = '00' + (255 & byte).toString(16);
      return hex.slice(-2);
    })
    .join('');
}

/**
 * Base64编码
 */
function base64Encode(str) {
  if (typeof btoa !== 'undefined') {
    return btoa(unescape(encodeURIComponent(str)));
  }
  // Node.js环境
  return Buffer.from(str, 'utf8').toString('base64');
}

module.exports = {
  decodeBase64URL,
  getRandomIDPro,
  getRandomElement,
  getRandomDevice,
  fromBase64,
  isPlainObject,
  isEmpty,
  containsReservedParamName,
  isNullOrUndefined,
  isSafeParamValue,
  formatDate,
  selectRandomElements,
  getRandomInt10,
  filterCharactersFromString,
  toHexString,
  base64Encode
};
