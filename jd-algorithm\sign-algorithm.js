/**
 * Sign算法实现
 */

const { CryptoJS } = require('./crypto-utils');
const { getRandomElement, getRandomIDPro, getRandomDevice, base64Encode } = require('./utils');

/**
 * 生成Sign签名
 */
function generateSign(functionId, body, uuid, client = 'android', clientVersion = '13.6.3') {
  const st = Date.now();
  const version = [
    [0, 2],
    [1, 1],
    [2, 0],
  ];
  const r1r2 = getRandomElement(version);
  const r1 = r1r2[0];
  const r2 = r1r2[1];
  const sv = `1${r1}${r2}`;
  const input = `functionId=${functionId}&body=${body}&uuid=${uuid}&client=${client}&clientVersion=${clientVersion}&st=${st}&sv=${sv}`;
  const ret_bytes = sub_126AC(Buffer.from(input), r1, r2);
  const sign = CryptoJS.MD5(Buffer.from(ret_bytes).toString('base64')).toString();
  
  return {
    st,
    sv,
    sign,
  };
}

/**
 * 生成EP参数
 */
function generateEp(uuid) {
  const randomDevice = getRandomDevice();
  return JSON.stringify({
    hdid: 'JM9F1ywUPwflvMIpYPok0tt5k9kW4ArJEU3lfLhxBqw=',
    ts: Date.now(),
    ridx: -1,
    cipher: {
      d_model: base64Encode(randomDevice.model),
      wifiBssid: 'dW5hbw93bq==', // unknown
      osVersion: base64Encode(getRandomElement(['10', '11', '12', '13'])),
      d_brand: base64Encode(randomDevice.brand),
      screen: base64Encode(getRandomElement(['640x1136', '750x1334', '1080x1920', '2297*1080'])),
      uuid: base64Encode(uuid),
      aid: base64Encode(uuid),
      openudid: base64Encode(uuid),
      area: base64Encode(getRandomIDPro({ size: 2 }) + '_' + getRandomIDPro({ size: 4 }) + '_' + getRandomIDPro({ size: 5 }) + '_' + getRandomIDPro({ size: 5 })),
    },
  });
}

/**
 * 算法辅助函数 - sub_10D70
 */
function sub_10D70(input) {
  const arr = [0x37, 0x92, 0x44, 0x68, 0xa5, 0x3d, 0xcc, 0x7f, 0xbb, 0x0f, 0xd9, 0x88, 0xee, 0x9a, 0xe9, 0x5a];
  const key2 = Buffer.from('80306f4370b39fd5630ad0529f77adb6');
  const arr1 = Buffer.alloc(input.length);

  for (let i = 0; i < input.length; i++) {
    let r0 = input[i];
    const r2 = arr[i & 0xf];
    const r4 = key2[i & 7];
    r0 ^= r2;
    r0 ^= r4;
    r0 += r2;
    let r2_new = r2 ^ r0;
    r2_new ^= key2[i & 7];
    arr1[i] = r2_new & 0xff;
  }

  return arr1;
}

/**
 * 算法辅助函数 - sub_10EA4
 */
function sub_10EA4(input) {
  const arr = [0x37, 0x92, 0x44, 0x68, 0xa5, 0x3d, 0xcc, 0x7f, 0xbb, 0x0f, 0xd9, 0x88, 0xee, 0x9a, 0xe9, 0x5a];
  const key2 = Buffer.from('80306f4370b39fd5630ad0529f77adb6');
  const arr1 = Buffer.alloc(input.length);

  for (let i = 0; i < input.length; i++) {
    let r0 = input[i];
    const r2 = arr[i & 0xf];
    const r4 = key2[i & 7];
    r0 ^= r2;
    r0 ^= r4;
    r0 += r2;
    let r2_new = r2 ^ r0;
    r2_new ^= key2[i & 7];
    arr1[i] = r2_new & 0xff;
  }

  return arr1;
}

/**
 * 算法辅助函数 - sub_12ECC
 */
function sub_12ECC(input) {
  const arr = [0x37, 0x92, 0x44, 0x68, 0xa5, 0x3d, 0xcc, 0x7f, 0xbb, 0x0f, 0xd9, 0x88, 0xee, 0x9a, 0xe9, 0x5a];
  const key2 = Buffer.from('80306f4370b39fd5630ad0529f77adb6');
  const arr1 = Buffer.alloc(input.length);

  for (let i = 0; i < input.length; i++) {
    let r0 = input[i];
    const r2 = arr[i & 0xf];
    const r4 = key2[i & 7];
    r0 ^= r2;
    r0 ^= r4;
    r0 += r2;
    let r2_new = r2 ^ r0;
    r2_new ^= key2[i & 7];
    arr1[i] = r2_new & 0xff;
  }

  return arr1;
}

/**
 * 算法辅助函数 - sub_12510
 */
function sub_12510(input) {
  let output = Buffer.from('');
  const length = Math.floor(input.length / 8);

  for (let i = 0; i < length; i++) {
    const start = i * 8;
    const end = (i + 1) * 8;
    output = Buffer.concat([output, sub_10EA4(input.subarray(start, end))]);
  }

  if (input.length % 8 !== 0) {
    output = Buffer.concat([output, sub_10D70(input.subarray(-(input.length % 8)))]);
  }

  return output;
}

/**
 * 主要算法函数 - sub_126AC
 */
function sub_126AC(input, random1, random2) {
  let arr = [0, 1, 2];

  if (random2 === 1) {
    arr = [1, 2, 0];
  } else if (random2 === 2) {
    arr = [2, 0, 1];
  }

  const version = arr[random1];

  switch (version) {
    case 0:
      return sub_12510(input);
    case 2:
      return sub_12ECC(input);
    case 1:
      throw new Error('无法确定Sign的代码逻辑');
    default:
      throw new Error('未知的版本号');
  }
}

/**
 * 生成完整的Sign请求参数
 */
function generateSignParams(functionId, body, uuid, client = 'android', clientVersion = '13.6.3') {
  // 如果没有提供uuid，生成一个
  if (!uuid) {
    uuid = getRandomIDPro({ size: 16, customDict: '0123456789abcdef' });
  }

  const signResult = generateSign(functionId, body, uuid, client, clientVersion);
  
  return {
    client: client,
    clientVersion: clientVersion,
    functionId: functionId,
    body: body,
    ef: '1',
    ep: generateEp(uuid),
    uuid: uuid,
    st: signResult.st,
    sv: signResult.sv,
    sign: signResult.sign,
  };
}

module.exports = {
  generateSign,
  generateEp,
  generateSignParams
};
