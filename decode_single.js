const fs = require('fs');
const vm = require('vm');

// 读取混淆文件
const content = fs.readFileSync('main.min.js', 'utf8');

// 提取必要的组件
const arrayMatch = content.match(/const\s+(_0x[a-f0-9]+)\s*=\s*\[([\s\S]*?)\];/);
const shuffleMatch = content.match(/\(function\((_0x[a-f0-9]+),\s*(_0x[a-f0-9]+)\)\s*\{[\s\S]*?\}\((_0x[a-f0-9]+),\s*(0x[a-f0-9]+)\)\);/);
const decodeFuncMatch = content.match(/const\s+(_0x[a-f0-9]+)\s*=\s*function\s*\([^)]*\)\s*\{[\s\S]*?\};/);

if (!arrayMatch || !decodeFuncMatch) {
    console.log('无法找到必要的混淆组件');
    process.exit(1);
}

// 创建执行环境
const sandbox = {
    console: { log: () => {} },
    atob: (str) => Buffer.from(str, 'base64').toString('binary'),
    String: String,
    Math: Math,
    parseInt: parseInt,
    decodeURIComponent: decodeURIComponent,
    Buffer: Buffer,
    window: {}
};

try {
    // 执行字符串数组定义
    vm.runInNewContext(arrayMatch[0], sandbox);
    
    // 执行数组重排（如果存在）
    if (shuffleMatch) {
        vm.runInNewContext(shuffleMatch[0], sandbox);
    }
    
    // 执行解码函数定义
    vm.runInNewContext(decodeFuncMatch[0], sandbox);
    
    const decodeFuncName = decodeFuncMatch[1];
    const decodeFunc = sandbox[decodeFuncName];
    
    if (typeof decodeFunc !== 'function') {
        console.log('解码函数创建失败');
        process.exit(1);
    }
    
    // 解码特定的调用
    const targetIndex = '0x1b0';
    const targetKey = 'NY7O';
    
    console.log(`正在解码: ${decodeFuncName}('${targetIndex}', '${targetKey}')`);
    
    const decoded = decodeFunc(targetIndex, targetKey);
    console.log(`解码结果: "${decoded}"`);
    
    // 同时解码一些常见的调用，帮助理解代码
    const commonCalls = [
        ['0x1b0', 'NY7O'],
        ['0x49', 'RmO]'],
        ['0x14', 'v1Rp'],
        ['0x87', '^jVw'],
        ['0x10', '^jVw'],
        ['0x102', 'gdIQ'],
        ['0x2d', 'pyqW'],
        ['0x15d', 'frUB'],
        ['0x28', 'i*IW'],
        ['0x33', '$uwW']
    ];
    
    console.log('\n其他常见调用的解码结果:');
    for (const [index, key] of commonCalls) {
        try {
            const result = decodeFunc(index, key);
            console.log(`${decodeFuncName}('${index}', '${key}') = "${result}"`);
        } catch (e) {
            console.log(`${decodeFuncName}('${index}', '${key}') = 解码失败: ${e.message}`);
        }
    }
    
} catch (e) {
    console.log('解码失败:', e.message);
    console.log(e.stack);
}
