/**
 * H5st算法实现
 */

const CryptoJS = require('crypto-js');
const { createCustomAlgorithm } = require('./crypto-utils');
const { TokenFactory } = require('./token-generator');
const {
  formatDate,
  getRandomIDPro,
  selectRandomElements,
  getRandomInt10,
  filterCharactersFromString,
  decodeBase64URL,
  isNullOrUndefined,
  isEmpty,
  containsReservedParamName,
  isSafeParamValue
} = require('./utils');
const {
  H5stAlgoConfigCollection,
  SignAlgorithmType,
  CANVAS_FP,
  WEBGL_FP
} = require('./config');

/**
 * H5st算法基础类
 */
class BaseH5st {
  constructor(version, config) {
    this.version = version;
    this.h5stAlgoConfig = config;
    this.algos = createCustomAlgorithm();
    this.tokenFactory = new TokenFactory();
    this.localToken = this.tokenFactory.getInstance(config.tokenVersion, config.genLocalTK);
    this.context = {
      _debug: false,
      _isNormal: false,
      _version: version,
      customAlgorithm: config.customAlgorithm || {}
    };

    // 设置算法上下文
    this.algos.setContext(this.context);
  }

  /**
   * 初始化配置
   */
  __iniConfig(h5stInitConfig) {
    this.context._debug = h5stInitConfig.debug || false;
    this.context._appId = h5stInitConfig.appId;
    this.context._fingerprint = this.generateVisitKey();
    this.context._isNormal = false;
    this.context._token = '';
    this.context._defaultToken = '';

    // 设置自定义算法参数
    if (this.h5stAlgoConfig.customAlgorithm) {
      this.context.customAlgorithm = this.h5stAlgoConfig.customAlgorithm;
      this.algos.setContext(this.context);
    }
  }

  /**
   * 生成默认的签名key
   */
  __genDefaultKey(token, fingerprint, time, appId) {
    const input = `${token}${fingerprint}${time}${appId}${this.h5stAlgoConfig.defaultKey.extend}`;

    try {
      const tokenPart = this.__parseToken(token, 16, 28);
      const decodedUrl = decodeBase64URL(tokenPart);
      const express = this.algos.enc.Utf8.stringify(this.algos.enc.Base64.parse(decodedUrl));
      const expressMatch = /^[123]([x+][123])+/.exec(express);

      let key = '';
      if (expressMatch) {
        const expressHit = expressMatch[0].split('');
        let keyRouter = '';

        expressHit.forEach((router) => {
          if (isNaN(Number(router))) {
            keyRouter += router === 'x' ? '*' : router;
          } else {
            keyRouter += router;
          }
        });

        try {
          const result = eval(keyRouter);
          key = this.algos.MD5(input + result).toString(this.algos.enc.Hex);
        } catch (e) {
          key = this.algos.MD5(input).toString(this.algos.enc.Hex);
        }
      } else {
        key = this.algos.MD5(input).toString(this.algos.enc.Hex);
      }

      return key;
    } catch (e) {
      // 如果解析失败，使用简单的MD5
      return this.algos.MD5(input).toString(this.algos.enc.Hex);
    }
  }

  /**
   * 解析Token
   */
  __parseToken(token, start, end) {
    try {
      const parts = token.split(';');
      if (parts.length >= 7) {
        const cipherStr = parts[6];
        return cipherStr.substring(start, end);
      }
    } catch (e) {
      // 解析失败，返回默认值
    }
    return 'default_token_part';
  }

  /**
   * 生成签名
   */
  __genSign(key, body) {
    const signAlgorithmType = this.h5stAlgoConfig.signAlgorithmType;
    const paramsStr = body
      .map((item) => {
        return item.key + ':' + item.value;
      })
      .join('&');

    let signedStr;
    if (signAlgorithmType === SignAlgorithmType.MD5_WRAP) {
      signedStr = this.algos.MD5(`${key}${paramsStr}${key}`).toString(this.algos.enc.Hex);
    } else if (signAlgorithmType === SignAlgorithmType.SHA256_WRAP) {
      signedStr = this.algos.SHA256(`${key}${paramsStr}${key}`).toString(this.algos.enc.Hex);
    } else if (signAlgorithmType === SignAlgorithmType.HMAC_SHA256_WRAP) {
      signedStr = this.algos.HmacSHA256(paramsStr, key).toString(this.algos.enc.Hex);
    } else {
      throw new Error('未指定加密模式或者不兼容的加密模式');
    }

    return signedStr;
  }

  /**
   * 生成默认签名（4.7.4新增）
   */
  __genSignDefault(key, body) {
    const signAlgorithmType = this.h5stAlgoConfig.signAlgorithmType;
    const paramsStr = body
      .map((item) => {
        return item.key + ':' + item.value;
      })
      .join('&');

    let signedStr;
    if (signAlgorithmType === SignAlgorithmType.MD5_WRAP) {
      signedStr = this.algos.MD5(`${key}${paramsStr}${key}`).toString(this.algos.enc.Hex);
    } else if (signAlgorithmType === SignAlgorithmType.SHA256_WRAP) {
      signedStr = this.algos.SHA256(`${key}${paramsStr}${key}`).toString(this.algos.enc.Hex);
    } else if (signAlgorithmType === SignAlgorithmType.HMAC_SHA256_WRAP) {
      signedStr = this.algos.HmacSHA256(paramsStr, key).toString(this.algos.enc.Hex);
    } else {
      throw new Error('未指定加密模式或者不兼容的加密模式');
    }

    return signedStr;
  }

  /**
   * 组装H5ST签名参数
   */
  __genSignParams(bodySign, timestamp, timeStr, envSign, signStrDefault) {
    const { _fingerprint, _appId, _isNormal, _token, _defaultToken, _version } = this.context;
    signStrDefault = signStrDefault ? `;${signStrDefault}` : '';
    return `${timeStr};${_fingerprint};${_appId};${_isNormal ? _token : _defaultToken};${bodySign};${_version};${timestamp};${envSign}${signStrDefault}`;
  }

  /**
   * 检查参数
   */
  __checkParams(filterParams) {
    if (isEmpty(filterParams)) {
      return null;
    }

    if (containsReservedParamName(filterParams)) {
      throw new Error('参数包含保留字段');
    }

    const checkParams = [];
    for (const [key, value] of Object.entries(filterParams)) {
      if (value !== undefined && isSafeParamValue(value)) {
        checkParams.push({ key, value: String(value) });
      }
    }

    return checkParams;
  }

  /**
   * 收集环境信息
   */
  async __collect() {
    const envExtend = this.context.envExtend;
    const randomLength = this.h5stAlgoConfig.env.randomLength;

    const envData = {
      random: getRandomIDPro({ size: randomLength }),
      v: this.h5stAlgoConfig.env.fv || '',
      fp: this.context._fingerprint,
      canvas: CANVAS_FP,
      webglFp: WEBGL_FP,
      ...envExtend
    };

    return this.envSign(JSON.stringify(envData));
  }

  /**
   * 环境信息加密
   */
  envSign(message) {
    try {
      // 4.8.1开始不再使用AES算法
      if (!isNullOrUndefined(this.h5stAlgoConfig.customAlgorithm?.convertIndex?.hex)) {
        return CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(message));
      }

      const secret = this.h5stAlgoConfig.env.secret;
      if (!secret) {
        return CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(message));
      }

      const temp = this.algos.AES.encrypt(message, secret, {
        iv: CryptoJS.enc.Utf8.parse('0102030405060708'),
      });

      // 4.7开始会将AES加密结果通过自定义的Base64.encode编码
      if (this.h5stAlgoConfig.customAlgorithm?.map) {
        return this.algos.enc.Base64.encode(temp.ciphertext);
      }

      return temp.ciphertext.toString();
    } catch (e) {
      // 如果加密失败，返回简单的Base64编码
      return CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(message));
    }
  }

  /**
   * 生成访问密钥（指纹）
   */
  generateVisitKey() {
    const { seed, selectLength, randomLength } = this.h5stAlgoConfig.visitKey;

    const selectedChars = selectRandomElements(seed, selectLength);
    const random = getRandomInt10();
    const filteredChars = filterCharactersFromString(seed, selectedChars);
    const combinedString =
      getRandomIDPro({
        size: random,
        customDict: filteredChars,
      }) +
      selectedChars +
      getRandomIDPro({
        size: randomLength - random,
        customDict: filteredChars,
      }) +
      random;

    return this.convertVisitKey(combinedString);
  }

  /**
   * 转换访问密钥
   */
  convertVisitKey(combinedString) {
    const { convertLength } = this.h5stAlgoConfig.visitKey;
    if (convertLength && combinedString.length > convertLength) {
      return combinedString.substring(0, convertLength);
    }
    return combinedString;
  }

  /**
   * H5st加签
   */
  __makeSign(params, envSign) {
    const appId = this.context._appId;
    const fingerprint = this.context._fingerprint;
    const extendDateStr = this.h5stAlgoConfig.makeSign.extendDateStr;

    const now = Date.now();
    const dateStr = formatDate(now, 'yyyyMMddhhmmssSSS');
    const dateStrExtend = dateStr + extendDateStr;

    const defaultToken = this.localToken.genLocalTK(fingerprint);
    const key = this.__genDefaultKey(defaultToken, fingerprint, dateStrExtend, appId);
    this.context._defaultToken = defaultToken;

    if (!key) {
      return {};
    }

    const signStr = this.__genSign(key, params);
    const stk = params.map((item) => item.key).join(',');

    // 4.7.4新增
    let signStrDefault = '';
    if (this.h5stAlgoConfig.genSignDefault) {
      signStrDefault = this.__genSignDefault(key, params);
    }

    const h5st = this.__genSignParams(signStr, now, dateStr, envSign, signStrDefault);

    return {
      _stk: stk,
      _ste: 1,
      h5st: h5st,
    };
  }

  /**
   * 环境信息解密
   */
  envDecrypt(envSignStr) {
    try {
      // 简单的解密逻辑，实际可能更复杂
      const decrypted = this.algos.enc.Base64.parse(envSignStr);
      const envData = JSON.parse(this.algos.enc.Utf8.stringify(decrypted));
      this.context.envExtend = envData;
    } catch (e) {
      // 解密失败，使用默认值
      this.context.envExtend = {};
    }
  }

  /**
   * H5st加签入口
   */
  async sign(params, h5stInitConfig, envSignStr) {
    const start = Date.now();

    // 初始化配置
    this.__iniConfig(Object.assign({}, {
      debug: false,
      appId: ''
    }, h5stInitConfig));

    // 如果有环境签名字符串，进行解密
    if (envSignStr) {
      this.envDecrypt(envSignStr);
    }

    // 设置默认的stk参数
    const defaultStk = ['functionId', 'appid', 'client', 'body', 'clientVersion', 'sign', 't', 'jsonp'];
    this.context.stk = h5stInitConfig.stk || defaultStk;

    // 过滤参数
    const filterParams = {};
    this.context.stk.forEach((key) => {
      let value = params[key];
      if (value !== undefined) {
        if (key === 'body') {
          value = CryptoJS.SHA256(value).toString();
        }
        filterParams[key] = value;
      }
    });

    // 检查参数
    const checkParams = this.__checkParams(filterParams);
    if (checkParams == null) {
      return filterParams;
    }

    // 收集环境信息
    const envSign = await this.__collect();

    // 生成签名
    const makeSign = this.__makeSign(checkParams, envSign);

    console.log(`H5st sign elapsed time: ${Date.now() - start}ms`);

    return Object.assign({}, filterParams, makeSign);
  }

  /**
   * 收集环境信息
   */
  async envCollect() {
    const envExtend = this.context.envExtend;
    const randomLength = this.h5stAlgoConfig.env.randomLength;

    return this.coverEnv(envExtend, envExtend?.random?.length ?? randomLength);
  }

  /**
   * 覆盖环境信息
   */
  coverEnv(envExtend, randomLength) {
    return {
      pp: {},
      extend: envExtend || {},
      random: getRandomIDPro({ size: randomLength }),
      sua: '',
      v: this.h5stAlgoConfig.env.fv || '',
      fp: this.context._fingerprint,
      wc: 0,
      wd: 0,
      l: 'zh-CN',
      ls: 'zh-CN',
      ml: 0,
      pl: 0,
      av: '',
      ua: '',
      w: 1920,
      h: 1080,
      ow: 1920,
      oh: 1080,
      url: '',
      og: '',
      pf: 'Linux x86_64',
      bu2: '',
      canvas: CANVAS_FP,
      canvas1: '',
      webglFp: WEBGL_FP,
      webglFp1: '',
      ccn: 0,
      ai: '',
      pr: 1,
      re: '',
      referer: '',
      pp1: ''
    };
  }
}

/**
 * H5st工厂类
 */
class H5stFactory {
  constructor() {
    this.instances = new Map();

    // 初始化所有版本的实例
    for (const [version, config] of Object.entries(H5stAlgoConfigCollection)) {
      this.instances.set(version, new BaseH5st(version, config));
    }
  }

  getInstance(key) {
    return this.instances.get(key);
  }
}

module.exports = {
  BaseH5st,
  H5stFactory,
  H5stAlgoConfigCollection
};
