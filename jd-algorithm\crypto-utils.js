/**
 * 加密工具函数
 * 需要安装 crypto-js: npm install crypto-js
 */

const CryptoJS = require('crypto-js');
const { isNullOrUndefined } = require('./utils');

/**
 * 自定义算法类
 */
class CustomAlgorithm {
  constructor() {
    this.context = {};
  }

  setContext(context) {
    this.context = context;
  }

  /**
   * 添加盐值
   */
  addSalt(message) {
    const salt = this.context.customAlgorithm?.salt;
    if (!salt) {
      return message;
    }

    if (typeof message === 'string') {
      return message + salt;
    }
    return message;
  }

  /**
   * 处理密钥
   */
  eKey(key) {
    const convertIndex = this.context.customAlgorithm?.convertIndex?.hmac;

    if (isNullOrUndefined(convertIndex)) {
      return key;
    }

    if (typeof key === 'string') {
      key = CryptoJS.enc.Utf8.parse(key);
    }

    const array = Array.from(this.enc.Utils.fromWordArray(key));
    if (convertIndex > array.length) {
      key = this.enc.Utils.toWordArray(array.reverse());
    } else {
      const reversedPart = array.slice(0, convertIndex).reverse();
      const remainingPart = array.slice(convertIndex);
      key = this.enc.Utils.toWordArray(reversedPart.concat(remainingPart));
    }

    return key;
  }

  /**
   * AES加密
   */
  AES = {
    encrypt: (message, key, cfg) => {
      if (typeof key === 'string') {
        if (this.context.customAlgorithm?.keyReverse) {
          key = key.split('').reverse().join('');
        }
        key = CryptoJS.enc.Utf8.parse(key);
      }
      return CryptoJS.AES.encrypt(this.addSalt(message), key, cfg);
    }
  };

  /**
   * 编码工具
   */
  enc = {
    ...CryptoJS.enc,
    Utils: {
      toWordArray: (array) => {
        const words = [];
        for (let i = 0; i < array.length; i++) {
          words[i >>> 2] |= array[i] << (24 - (i % 4) * 8);
        }
        return CryptoJS.lib.WordArray.create(words, array.length);
      },
      fromWordArray: (wordArray) => {
        const u8array = new Uint8Array(wordArray.sigBytes);
        for (let i = 0; i < wordArray.sigBytes; i++) {
          u8array[i] = (wordArray.words[i >>> 2] >>> (24 - (i % 4) * 8)) & 0xff;
        }
        return u8array;
      },
    },
    Hex: {
      ...CryptoJS.enc.Hex,
      stringify: (wordArray) => {
        const convertIndex = this.context.customAlgorithm?.convertIndex?.hex;

        if (isNullOrUndefined(convertIndex)) {
          return CryptoJS.enc.Hex.stringify(wordArray);
        }

        const array = Array.from(this.enc.Utils.fromWordArray(wordArray));
        if (convertIndex > array.length) {
          wordArray = this.enc.Utils.toWordArray(array.reverse());
        } else {
          const reversedPart = array.slice(0, convertIndex).reverse();
          const remainingPart = array.slice(convertIndex);
          wordArray = this.enc.Utils.toWordArray(reversedPart.concat(remainingPart));
        }

        return CryptoJS.enc.Hex.stringify(wordArray);
      },
    },
    Base64: {
      ...CryptoJS.enc.Base64,
      encode: (wordArray) => {
        const map = this.context.customAlgorithm?.map;
        if (!map) {
          // 如果没有自定义map，使用标准Base64编码
          return CryptoJS.enc.Base64.stringify(wordArray);
        }

        try {
          const typedArray = this.enc.Utils.fromWordArray(wordArray);
          const normalArray = Array.from(typedArray);
          const number = normalArray.length % 3;
          for (let j = 0; j < 3 - number; j++) {
            normalArray.push(3 - number);
          }
          let sigBytes = normalArray.length;
          let words = [];
          for (let j = sigBytes; j > 0; j -= 3) {
            words.push(...normalArray.slice(Math.max(j - 3, 0), j));
          }

          let result = '';
          for (let i = 0; i < words.length; i += 3) {
            const byte1 = words[i];
            const byte2 = words[i + 1] || 0;
            const byte3 = words[i + 2] || 0;

            const bitmap = (byte1 << 16) | (byte2 << 8) | byte3;

            result += map.charAt((bitmap >> 18) & 63);
            result += map.charAt((bitmap >> 12) & 63);
            result += map.charAt((bitmap >> 6) & 63);
            result += map.charAt(bitmap & 63);
          }

          return result;
        } catch (e) {
          // 如果自定义编码失败，回退到标准Base64
          return CryptoJS.enc.Base64.stringify(wordArray);
        }
      },
    },
  };

  lib = CryptoJS.lib;

  /**
   * MD5哈希
   */
  MD5(message) {
    return CryptoJS.MD5(this.addSalt(message));
  }

  /**
   * SHA256哈希
   */
  SHA256(message) {
    return CryptoJS.SHA256(this.addSalt(message));
  }

  /**
   * HMAC-SHA256
   */
  HmacSHA256(message, key) {
    return CryptoJS.HmacSHA256(this.addSalt(message), this.eKey(key));
  }
}

/**
 * 创建自定义算法实例
 */
function createCustomAlgorithm() {
  return new CustomAlgorithm();
}

module.exports = {
  CryptoJS,
  CustomAlgorithm,
  createCustomAlgorithm
};
