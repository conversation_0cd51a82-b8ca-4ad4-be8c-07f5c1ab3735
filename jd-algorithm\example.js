/**
 * 使用示例
 * 演示如何使用H5st和Sign算法
 */

const { generateH5st, generateSign, generateUUID } = require('./index');

/**
 * H5st算法使用示例
 */
async function h5stExample() {
  console.log('=== H5st算法示例 ===');
  
  try {
    // 示例1: 使用appId生成H5st
    const h5stParams1 = {
      version: '5.0.6',
      appId: '27004',
      pin: 'test_user',
      ua: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      body: {
        functionId: 'queryMaterialProducts',
        appid: 'activities_platform',
        body: JSON.stringify({
          pageSize: 10,
          pageNo: 1
        })
      },
      debug: true
    };

    const result1 = await generateH5st(h5stParams1);
    console.log('H5st结果1:', result1);
    console.log('查询字符串1:', result1.qs);

    // 示例2: 使用已有h5st字符串
    const h5stParams2 = {
      version: '3.1.0',
      h5st: '20231201120000123;fp123456;27004;token123;bodyhash;3.1;1701408000000;envsign',
      body: {
        functionId: 'getUserInfo',
        appid: 'jd_app',
        body: JSON.stringify({
          userId: '12345'
        })
      }
    };

    const result2 = await generateH5st(h5stParams2);
    console.log('H5st结果2:', result2);

    // 示例3: 自定义stk参数
    const h5stParams3 = {
      version: '5.0.1',
      appId: '27004',
      body: {
        functionId: 'getProductList',
        appid: 'shop_app',
        body: JSON.stringify({
          categoryId: '1001'
        }),
        client: 'android',
        clientVersion: '13.6.3'
      },
      stk: ['functionId', 'appid', 'body', 'client', 'clientVersion']
    };

    const result3 = await generateH5st(h5stParams3);
    console.log('H5st结果3:', result3);

  } catch (error) {
    console.error('H5st生成错误:', error.message);
  }
}

/**
 * Sign算法使用示例
 */
function signExample() {
  console.log('\n=== Sign算法示例 ===');
  
  try {
    // 示例1: 基本Sign生成
    const signParams1 = {
      functionId: 'queryMaterialProducts',
      body: JSON.stringify({
        pageSize: 10,
        pageNo: 1,
        categoryId: '1001'
      })
    };

    const result1 = generateSign(signParams1);
    console.log('Sign结果1:', result1);
    console.log('查询字符串1:', result1.qs);

    // 示例2: 自定义客户端信息
    const signParams2 = {
      functionId: 'getUserInfo',
      body: JSON.stringify({
        userId: '12345'
      }),
      uuid: generateUUID(),
      client: 'ios',
      clientVersion: '12.5.0'
    };

    const result2 = generateSign(signParams2);
    console.log('Sign结果2:', result2);

    // 示例3: 使用指定UUID
    const customUuid = generateUUID(32, '0123456789abcdef');
    const signParams3 = {
      functionId: 'getOrderList',
      body: JSON.stringify({
        status: 'completed',
        pageSize: 20
      }),
      uuid: customUuid
    };

    const result3 = generateSign(signParams3);
    console.log('Sign结果3:', result3);

  } catch (error) {
    console.error('Sign生成错误:', error.message);
  }
}

/**
 * 工具函数示例
 */
function utilsExample() {
  console.log('\n=== 工具函数示例 ===');
  
  // 生成不同长度的UUID
  console.log('16位UUID:', generateUUID());
  console.log('32位UUID:', generateUUID(32));
  console.log('8位数字UUID:', generateUUID(8, '0123456789'));
  console.log('10位字母UUID:', generateUUID(10, 'abcdefghijklmnopqrstuvwxyz'));
}

/**
 * 实际应用示例 - 模拟京东API调用
 */
async function practicalExample() {
  console.log('\n=== 实际应用示例 ===');
  
  try {
    // 模拟获取商品列表的API调用
    const apiParams = {
      version: '5.0.6',
      appId: '27004',
      pin: 'jd_user_123',
      ua: 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15',
      body: {
        functionId: 'queryMaterialProducts',
        appid: 'activities_platform',
        body: JSON.stringify({
          pageSize: 20,
          pageNo: 1,
          categoryId: '1001',
          sortType: 'price_asc'
        }),
        client: 'ios',
        clientVersion: '12.5.0',
        t: Date.now().toString()
      }
    };

    const h5stResult = await generateH5st(apiParams);
    
    // 构建完整的API URL
    const baseUrl = 'https://api.m.jd.com/client.action';
    const fullUrl = `${baseUrl}?${h5stResult.qs}`;
    
    console.log('完整API URL:', fullUrl);
    console.log('请求参数:', h5stResult.body);
    
    // 模拟发送请求（这里只是示例，实际使用时需要用fetch或axios等）
    console.log('可以使用以下方式发送请求:');
    console.log(`
    fetch('${baseUrl}', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'User-Agent': '${apiParams.ua}'
      },
      body: '${h5stResult.qs}'
    })
    .then(response => response.json())
    .then(data => console.log(data));
    `);

  } catch (error) {
    console.error('实际应用示例错误:', error.message);
  }
}

/**
 * 运行所有示例
 */
async function runAllExamples() {
  await h5stExample();
  signExample();
  utilsExample();
  await practicalExample();
}

// 如果直接运行此文件，执行示例
if (require.main === module) {
  runAllExamples().catch(console.error);
}

module.exports = {
  h5stExample,
  signExample,
  utilsExample,
  practicalExample,
  runAllExamples
};
