/**
 * H5st和Sign算法配置文件
 */

// 签名算法类型枚举
const SignAlgorithmType = {
  MD5_WRAP: 'MD5_WRAP', // MD5(`${key}${paramsStr}${key}`)
  SHA256_WRAP: 'SHA256_WRAP', // SHA256(`${key}${paramsStr}${key}`)
  HMAC_SHA256_WRAP: 'HMAC_SHA256_WRAP', // HmacSHA256(paramsStr, key)
};

// Token版本枚举
const LocalTokenVersion = {
  '03': '03',
  '04': '04',
  '05': '05'
};

// H5st常量
const CANVAS_FP = 'canvas_d41d8cd98f00b204e9800998ecf8427e';
const WEBGL_FP = 'webgl_d41d8cd98f00b204e9800998ecf8427e';
const STORAGE_FP_KEY = 'jd_h5st_fp';
const STORAGE_TOKEN_KEY = 'jd_h5st_token';

// 访问密钥配置
const h5st31VisitKey = {
  seed: '0123456789abcdefghijklmnopqrstuvwxyz',
  selectLength: 3,
  randomLength: 10,
  convertLength: 10
};

const h5st50VisitKey = {
  seed: '0123456789abcdefghijklmnopqrstuvwxyz',
  selectLength: 3,
  randomLength: 10,
  convertLength: 10
};

// Token基础信息配置
const normal02TokenBaseInfo = {
  magic: 'tk',
  version: '02',
  platform: 'h5',
  expires: 1800000,
  producer: 'l',
  expr: 'Math.random()',
  cipher: {
    area: 'CJVa',
    d_brand: 'bnVsbA==',
    d_model: 'bnVsbA==',
    wifiBssid: 'dW5rbm93bg==',
    osVersion: 'bnVsbA==',
    screen: 'MzQwKjc2MA==',
    uuid: 'ZGZhMWY5ODEtNGY0ZC00ZDJkLWJhNzMtMzBkNGQzZjU4ZGE2',
    aid: 'ZGZhMWY5ODEtNGY0ZC00ZDJkLWJhNzMtMzBkNGQzZjU4ZGE2',
    openudid: 'ZGZhMWY5ODEtNGY0ZC00ZDJkLWJhNzMtMzBkNGQzZjU4ZGE2'
  }
};

const normal04TokenBaseInfo = {
  magic: 'tk',
  version: '04',
  platform: 'h5',
  expires: 1800000,
  producer: 'l',
  expr: 'Math.random()',
  cipher: {
    area: 'CJVa',
    d_brand: 'bnVsbA==',
    d_model: 'bnVsbA==',
    wifiBssid: 'dW5rbm93bg==',
    osVersion: 'bnVsbA==',
    screen: 'MzQwKjc2MA==',
    uuid: 'ZGZhMWY5ODEtNGY0ZC00ZDJkLWJhNzMtMzBkNGQzZjU4ZGE2',
    aid: 'ZGZhMWY5ODEtNGY0ZC00ZDJkLWJhNzMtMzBkNGQzZjU4ZGE2',
    openudid: 'ZGZhMWY5ODEtNGY0ZC00ZDJkLWJhNzMtMzBkNGQzZjU4ZGE2'
  }
};

// H5st 3.1.0 算法配置
const H5st310AlgoConfig = {
  version: '3.1',
  tokenVersion: LocalTokenVersion['03'],
  signAlgorithmType: SignAlgorithmType.HMAC_SHA256_WRAP,
  env: {
    secret: 'wm0!@w_s#ll1flo(',
    randomLength: 10,
  },
  visitKey: h5st31VisitKey,
  defaultKey: {
    extend: '',
  },
  makeSign: {
    extendDateStr: '',
  },
  genLocalTK: {
    baseInfo: normal02TokenBaseInfo,
    cipher: {
      prefix: 'xx',
      secret2: 'dp0!@w_s#ll0flo(',
    },
  },
};

// H5st 5.0.1 算法配置
const H5st501AlgoConfig = {
  genSignDefault: true,
  tokenVersion: LocalTokenVersion['04'],
  signAlgorithmType: SignAlgorithmType.HMAC_SHA256_WRAP,
  version: '5.0',
  env: {
    fv: 'h5_file_v5.0.1',
    randomLength: 13,
  },
  visitKey: h5st50VisitKey,
  defaultKey: {
    extend: '7wcba&',
  },
  makeSign: {
    extendDateStr: '36',
  },
  genLocalTK: {
    baseInfo: normal04TokenBaseInfo,
    cipher: {
      secret1: '=9pStUH7B64/',
      prefix: 'm2',
    },
  },
  customAlgorithm: {
    salt: 'rxn&50',
    map: 'ZYXWVUTSRQPONMLKJIHGFEDCBA-_9876543210zyxwvutsrqponmlkjihgfedcba',
    convertIndex: {
      hex: 4,
      hmac: 4,
    },
    transformMessageOptions: {
      map: 'baZYXWVUTSRQPONMLKJIHGFEDCBA-_9876543210zyxwvutsrqponmlkjihgfedc',
      segments: 4,
      multiplier: 18,
    },
  },
};

// H5st 5.0.6 算法配置
const H5st506AlgoConfig = {
  genSignDefault: true,
  tokenVersion: LocalTokenVersion['04'],
  signAlgorithmType: SignAlgorithmType.SHA256_WRAP,
  version: '5.0',
  env: {
    fv: 'h5_file_v5.0.6',
    randomLength: 12,
  },
  visitKey: h5st50VisitKey,
  defaultKey: {
    extend: 'crDf.u',
  },
  makeSign: {
    extendDateStr: '22',
  },
  genLocalTK: {
    baseInfo: normal04TokenBaseInfo,
    cipher: {
      secret1: 'DbIAgz71j04v',
      prefix: 'nJ',
    },
  },
  customAlgorithm: {
    salt: 'R]dev/',
    map: 'ZYXWVUTSRQPONMLKJIHGFEDCBA-_9876543210zyxwvutsrqponmlkjihgfedcba',
    convertIndex: {
      hex: 5,
      hmac: 7,
    },
    transformMessageOptions: {
      map: 'ZYXWVUTSRQPONMLKJIHGFEDCBA-_9876543210zyxwvutsrqponmlkjihgfedcba',
      segments: 7,
      multiplier: 6,
    },
  },
};

// H5st算法配置集合
const H5stAlgoConfigCollection = {
  '3.1.0': H5st310AlgoConfig,
  '5.0.1': H5st501AlgoConfig,
  '5.0.6': H5st506AlgoConfig,
};

module.exports = {
  SignAlgorithmType,
  LocalTokenVersion,
  CANVAS_FP,
  WEBGL_FP,
  STORAGE_FP_KEY,
  STORAGE_TOKEN_KEY,
  h5st31VisitKey,
  h5st50VisitKey,
  normal02TokenBaseInfo,
  normal04TokenBaseInfo,
  H5st310AlgoConfig,
  H5st501AlgoConfig,
  H5st506AlgoConfig,
  H5stAlgoConfigCollection
};
