# HTTP Cookie Agent

![HTTP Cookie Agent](./docs/assets/ogp.jpg)

[![github sponsors](https://flat.badgen.net/badge/GitHub%20Sponsors/Support%20me%20%E2%9D%A4/ff69b4?icon=github)](https://github.com/sponsors/3846masa)
[![npm](https://flat.badgen.net/npm/v/http-cookie-agent)](https://www.npmjs.com/package/http-cookie-agent)
[![license](https://flat.badgen.net/badge/license/MIT/blue)](LICENSE)
[![standard-readme compliant](https://flat.badgen.net/badge/readme%20style/standard/green)](https://github.com/RichardLitt/standard-readme)

Allows cookies with every Node.js HTTP clients (e.g. Node.js global fetch, undici, axios, node-fetch).

## Table of Contents

- [Install](#install)
- [Usage](#usage)
  - [Supported libraries](#supported-libraries)
  - [Using with another Agent library](#using-with-another-agent-library)
- [Contributing](#contributing)
- [License](#license)

## Install

```bash
npm install http-cookie-agent tough-cookie
```

## Usage

See also [examples](./examples) for more details.

### Supported libraries

| Library              | Supported?        |
| -------------------- | ----------------- |
| Node.js global fetch | ✅                |
| `undici`             | ✅                |
| `node:http`          | ✅                |
| `node:https`         | ✅                |
| `axios`              | ✅                |
| `node-fetch`         | ✅                |
| `got`                | ✅ <sup>\*1</sup> |
| `superagent`         | ✅ <sup>\*1</sup> |
| `request`            | ✅ <sup>\*1</sup> |
| `needle`             | ✅                |
| `phin`               | ✅                |
| `@hapi/wrech`        | ✅                |
| `urllib`             | ✅                |
| Bun global fetch     | ❌ <sup>\*2</sup> |
| Deno global fetch    | ❌ <sup>\*2</sup> |

\*1: This library supports cookies by default. You may not need `http-cookie-agent`.

\*2: There have proprietary fetch implementation and is [not currently supported](https://github.com/3846masa/http-cookie-agent/issues/692).

#### Node.js global fetch

When you want to use Node.js global fetch, you should install `undici` additionally.

| Node.js version | undici version         |
| --------------- | ---------------------- |
| v23             | `npm install undici@6` |
| v22             | `npm install undici@6` |
| v20             | `npm install undici@6` |

```js
import { CookieJar } from 'tough-cookie';
import { CookieAgent } from 'http-cookie-agent/undici/v6';

const jar = new CookieJar();
const agent = new CookieAgent({ cookies: { jar } });

await fetch('https://example.com', { dispatcher: agent });
```

#### `undici`

```js
import { fetch } from 'undici';
import { CookieJar } from 'tough-cookie';
import { CookieAgent } from 'http-cookie-agent/undici';

const jar = new CookieJar();
const agent = new CookieAgent({ cookies: { jar } });

await fetch('https://example.com', { dispatcher: agent });
```

Alternatively, `http-cookie-agent` can be used as [interceptors](https://github.com/nodejs/undici/blob/v7.0.0/docs/docs/api/Dispatcher.md#dispatchercomposeinterceptors-interceptor).
In this case, the `cookie()` must be placed at the beginning of the interceptors.

```js
import { fetch, interceptors } from 'undici';
import { CookieJar } from 'tough-cookie';
import { cookie } from 'http-cookie-agent/undici';

const jar = new CookieJar();
const agent = new Agent()
  .compose(cookie({ jar }))
  .compose(interceptors.retry())
  .compose(interceptors.redirect({ maxRedirections: 3 }));

await fetch('https://example.com', { dispatcher: agent });
```

#### `node:http` / `node:https`

```js
import https from 'node:https';

import { CookieJar } from 'tough-cookie';
import { HttpsCookieAgent } from 'http-cookie-agent/http';

const jar = new CookieJar();
const agent = new HttpsCookieAgent({ cookies: { jar } });

https.get('https://example.com', { agent }, (res) => {
  // ...
});
```

#### `axios`

```js
import axios from 'axios';
import { CookieJar } from 'tough-cookie';
import { HttpCookieAgent, HttpsCookieAgent } from 'http-cookie-agent/http';

const jar = new CookieJar();

const client = axios.create({
  httpAgent: new HttpCookieAgent({ cookies: { jar } }),
  httpsAgent: new HttpsCookieAgent({ cookies: { jar } }),
});

await client.get('https://example.com');
```

#### `node-fetch`

```js
import fetch from 'node-fetch';
import { CookieJar } from 'tough-cookie';
import { HttpCookieAgent, HttpsCookieAgent } from 'http-cookie-agent/http';

const jar = new CookieJar();

const httpAgent = new HttpCookieAgent({ cookies: { jar } });
const httpsAgent = new HttpsCookieAgent({ cookies: { jar } });

await fetch('https://example.com', {
  agent: ({ protocol }) => {
    return protocol === 'https:' ? httpsAgent : httpAgent;
  },
});
```

#### `got`

:warning: `got` supports cookies by default. You may not need `http-cookie-agent`.

See https://github.com/sindresorhus/got/tree/v11.8.2#cookies.

```js
import got from 'got';
import { CookieJar } from 'tough-cookie';
import { HttpCookieAgent, HttpsCookieAgent } from 'http-cookie-agent/http';

const jar = new CookieJar();

const client = got.extend({
  agent: {
    http: new HttpCookieAgent({ cookies: { jar } }),
    https: new HttpsCookieAgent({ cookies: { jar } }),
  },
});

await client('https://example.com');
```

#### `superagent`

:warning: `superagent` supports cookies by default. You may not need `http-cookie-agent`.

See https://github.com/visionmedia/superagent/blob/v6.1.0/docs/index.md#saving-cookies.

```js
import superagent from 'superagent';
import { CookieJar } from 'tough-cookie';
import { MixedCookieAgent } from 'http-cookie-agent/http';

const jar = new CookieJar();
const mixedAgent = new MixedCookieAgent({ cookies: { jar } });

const client = superagent.agent().use((req) => req.agent(mixedAgent));

await client.get('https://example.com');
```

#### `request`

:warning: `request` supports cookies by default. You may not need `http-cookie-agent`.

See https://github.com/request/request/tree/v2.88.1#examples.

```js
import request from 'request';
import { CookieJar } from 'tough-cookie';
import { MixedCookieAgent } from 'http-cookie-agent/http';

const jar = new CookieJar();

const client = request.defaults({
  agent: new MixedCookieAgent({ cookies: { jar } }),
});

client.get('https://example.com', (_err, _res) => {
  // ...
});
```

#### `needle`

```js
import needle from 'needle';
import { CookieJar } from 'tough-cookie';
import { MixedCookieAgent } from 'http-cookie-agent/http';

const jar = new CookieJar();

await needle('get', 'https://example.com', {
  agent: new MixedCookieAgent({ cookies: { jar } }),
});
```

#### `phin`

```js
import phin from 'phin';
import { CookieJar } from 'tough-cookie';
import { MixedCookieAgent } from 'http-cookie-agent/http';

const jar = new CookieJar();

await phin({
  url: 'https://example.com',
  core: {
    agent: new MixedCookieAgent({ cookies: { jar } }),
  },
});
```

#### `@hapi/wreck`

```js
import Wreck from '@hapi/wreck';
import { CookieJar } from 'tough-cookie';
import { HttpCookieAgent, HttpsCookieAgent } from 'http-cookie-agent/http';

const jar = new CookieJar();

const client = Wreck.defaults({
  agents: {
    http: new HttpCookieAgent({ cookies: { jar } }),
    https: new HttpsCookieAgent({ cookies: { jar } }),
    httpsAllowUnauthorized: new HttpsCookieAgent({ cookies: { jar } }),
  },
});

await client.get('https://example.com');
```

#### `urllib`

```js
import { request, setGlobalDispatcher } from 'urllib';
import { CookieJar } from 'tough-cookie';
import { CookieAgent } from 'http-cookie-agent/undici';

const jar = new CookieJar();
const agent = new CookieAgent({ cookies: { jar } });
setGlobalDispatcher(agent);

await request('https://example.com');
```

### Using with another Agent library

If you want to use another Agent library, wrap the agent in `createCookieAgent`.

```js
import https from 'node:https';

import { HttpsAgent as KeepAliveAgent } from 'agentkeepalive';
import { CookieJar } from 'tough-cookie';
import { createCookieAgent } from 'http-cookie-agent/http';

const Agent = createCookieAgent(KeepAliveAgent);

const jar = new CookieJar();
const agent = new Agent({ cookies: { jar } });

https.get('https://example.com', { agent }, (res) => {
  // ...
});
```

#### `undici`

If you want to use another undici Agent library, use `cookie` with the compose method.

```js
import { fetch, ProxyAgent } from 'undici';
import { CookieJar } from 'tough-cookie';
import { cookie } from 'http-cookie-agent/undici';

const jar = new CookieJar();
const agent = new ProxyAgent({
  /* ... */
}).compose(cookie({ jar }));

await fetch('https://example.com', { dispatcher: agent });
```

## Contributing

PRs accepted.

## License

[MIT (c) 3846masa](../LICENSE)
