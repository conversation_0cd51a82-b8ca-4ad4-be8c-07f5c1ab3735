/**
 * 京东H5st和Sign算法 - 主入口文件
 * 提供统一的API接口，可以直接在其他JS文件中调用
 */

const { H5stFactory } = require('./h5st-algorithm');
const { generateSignParams } = require('./sign-algorithm');
const { getRandomIDPro } = require('./utils');

/**
 * 京东算法类
 */
class JDAlgorithm {
  constructor() {
    this.h5stFactory = new H5stFactory();
  }

  /**
   * 生成H5st签名
   * @param {Object} params - 请求参数
   * @param {string} params.version - H5st版本 (如: '3.1.0', '5.0.1', '5.0.6')
   * @param {string} params.appId - 应用ID
   * @param {string} params.pin - 用户pin (可选)
   * @param {string} params.ua - 用户代理 (可选)
   * @param {Object} params.body - 业务参数
   * @param {string} params.body.functionId - 功能ID
   * @param {string} params.body.appid - 应用ID
   * @param {string} params.body.body - 请求体JSON字符串
   * @param {boolean} params.debug - 调试模式 (可选)
   * @param {Array} params.stk - 参与签名的参数名数组 (可选)
   * @param {string} params.h5st - 已有的h5st字符串 (可选，用于解析appId)
   * @param {string} params.envSignStr - 环境签名字符串 (可选)
   * @returns {Promise<Object>} 包含h5st签名的完整参数对象
   */
  async generateH5st(params) {
    try {
      const {
        version = '5.0.6',
        appId,
        pin,
        ua,
        body,
        debug = false,
        stk,
        h5st,
        envSignStr
      } = params;

      // 获取H5st实例
      const h5stInstance = this.h5stFactory.getInstance(version);
      if (!h5stInstance) {
        throw new Error(`不支持的H5st版本: ${version}`);
      }

      // 准备配置
      let config = {
        debug: debug
      };

      // 如果提供了h5st字符串，从中解析appId
      if (h5st) {
        const h5stParts = h5st.split(';');
        if (h5stParts.length >= 3) {
          config.appId = h5stParts[2];
        }
      } else if (appId) {
        config.appId = appId;
      } else {
        throw new Error('appId和h5st不能同时为空');
      }

      // 设置stk参数
      if (stk && Array.isArray(stk)) {
        config.stk = stk;
      }

      // 设置上下文信息
      if (pin) {
        h5stInstance.context.pt_pin = pin;
      }
      if (ua) {
        h5stInstance.context.userAgent = ua;
      }

      // 执行签名
      const result = await h5stInstance.sign(body, config, envSignStr);

      // 构建查询字符串
      const queryParams = new URLSearchParams();
      Object.keys(result).forEach(key => {
        if (result[key] !== undefined) {
          queryParams.append(key, result[key]);
        }
      });

      return {
        h5st: result,
        body: Object.assign({}, body, { h5st: result.h5st }),
        qs: queryParams.toString()
      };

    } catch (error) {
      throw new Error(`H5st生成失败: ${error.message}`);
    }
  }

  /**
   * 生成Sign签名
   * @param {Object} params - 请求参数
   * @param {string} params.functionId - 功能ID
   * @param {string} params.body - 请求体JSON字符串
   * @param {string} params.uuid - UUID (可选，不提供会自动生成)
   * @param {string} params.client - 客户端类型 (默认: 'android')
   * @param {string} params.clientVersion - 客户端版本 (默认: '13.6.3')
   * @returns {Object} 包含sign签名的完整参数对象
   */
  generateSign(params) {
    try {
      const {
        functionId,
        body,
        uuid,
        client = 'android',
        clientVersion = '13.6.3'
      } = params;

      if (!functionId) {
        throw new Error('functionId不能为空');
      }

      if (!body) {
        throw new Error('body不能为空');
      }

      // 生成UUID（如果没有提供）
      const finalUuid = uuid || getRandomIDPro({ size: 16, customDict: '0123456789abcdef' });

      // 生成签名参数
      const result = generateSignParams(functionId, body, finalUuid, client, clientVersion);

      // 构建查询字符串
      const queryParams = new URLSearchParams();
      Object.keys(result).forEach(key => {
        if (result[key] !== undefined) {
          queryParams.append(key, result[key]);
        }
      });

      return {
        body: result,
        qs: queryParams.toString()
      };

    } catch (error) {
      throw new Error(`Sign生成失败: ${error.message}`);
    }
  }

  /**
   * 获取支持的H5st版本列表
   * @returns {Array<string>} 支持的版本列表
   */
  getSupportedH5stVersions() {
    return ['3.1.0', '5.0.1', '5.0.6'];
  }

  /**
   * 生成随机UUID
   * @param {number} size - 长度 (默认: 16)
   * @param {string} customDict - 自定义字符集 (默认: 16进制字符)
   * @returns {string} 随机UUID
   */
  generateUUID(size = 16, customDict = '0123456789abcdef') {
    return getRandomIDPro({ size, customDict });
  }
}

// 创建单例实例
const jdAlgorithm = new JDAlgorithm();

/**
 * 便捷函数 - 生成H5st签名
 */
async function generateH5st(params) {
  return await jdAlgorithm.generateH5st(params);
}

/**
 * 便捷函数 - 生成Sign签名
 */
function generateSign(params) {
  return jdAlgorithm.generateSign(params);
}

/**
 * 便捷函数 - 生成UUID
 */
function generateUUID(size, customDict) {
  return jdAlgorithm.generateUUID(size, customDict);
}

// 导出
module.exports = {
  JDAlgorithm,
  generateH5st,
  generateSign,
  generateUUID,
  // 导出单例实例
  default: jdAlgorithm
};

// 如果在浏览器环境中，也可以通过全局变量访问
if (typeof window !== 'undefined') {
  window.JDAlgorithm = JDAlgorithm;
  window.generateH5st = generateH5st;
  window.generateSign = generateSign;
  window.generateUUID = generateUUID;
}
