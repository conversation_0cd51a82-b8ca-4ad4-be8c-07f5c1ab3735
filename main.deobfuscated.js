
_0xod2 = 'jsjiami.com.v6';
const $ = new Env('领取优惠券');
const nowVersion = "20250526";
console.log("当前版本" + nowVersion);

// 字符串数组已移除
// 数组重排函数已移除
// 解码函数已移除
            const _0x1cda3f = _0x14d149();
            const _0x1b75f7 = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';
            _0x1cda3f['atob'] || (_0x1cda3f['atob'] = function(_0x15ae66) {
                const _0x4da71c = String(_0x15ae66)['replace'](/=+$/, '');
                let _0x1d677a = '';
                for (let _0x26e037 = 0x0, _0x2d789e, _0x56403a, _0x3c7ebe = 0x0; _0x56403a = _0x4da71c['charAt'](_0x3c7ebe++); ~_0x56403a && (_0x2d789e = _0x26e037 % 0x4 ? _0x2d789e * 0x40 + _0x56403a : _0x56403a, _0x26e037++ % 0x4) ? _0x1d677a += String['fromCharCode'](0xff & _0x2d789e >> (-0x2 * _0x26e037 & 0x6)) : 0x0) {
                    _0x56403a = _0x1b75f7['indexOf'](_0x56403a);
                }
                return _0x1d677a;
            });
        }());
        const _0x5127f4 = function(_0x2f93b4, _0x41574a) {
            let _0x2d2a80 = [],
                _0x3f1182 = 0x0,
                _0x253dd8, _0x4329a7 = '',
                _0x516d6d = '';
            _0x2f93b4 = atob(_0x2f93b4);
            for (let _0x46b299 = 0x0, _0x16e6fc = _0x2f93b4['length']; _0x46b299 < _0x16e6fc; _0x46b299++) {
                _0x516d6d += '%' + ('00' + _0x2f93b4['charCodeAt'](_0x46b299)['toString'](0x10))['slice'](-0x2);
            }
            _0x2f93b4 = decodeURIComponent(_0x516d6d);
            let _0x2bbbf0;
            for (_0x2bbbf0 = 0x0; _0x2bbbf0 < 0x100; _0x2bbbf0++) {
                _0x2d2a80[_0x2bbbf0] = _0x2bbbf0;
            }
            for (_0x2bbbf0 = 0x0; _0x2bbbf0 < 0x100; _0x2bbbf0++) {
                _0x3f1182 = (_0x3f1182 + _0x2d2a80[_0x2bbbf0] + _0x41574a['charCodeAt'](_0x2bbbf0 % _0x41574a['length'])) % 0x100;
                _0x253dd8 = _0x2d2a80[_0x2bbbf0];
                _0x2d2a80[_0x2bbbf0] = _0x2d2a80[_0x3f1182];
                _0x2d2a80[_0x3f1182] = _0x253dd8;
            }
            _0x2bbbf0 = 0x0;
            _0x3f1182 = 0x0;
            for (let _0x4eba3c = 0x0; _0x4eba3c < _0x2f93b4['length']; _0x4eba3c++) {
                _0x2bbbf0 = (_0x2bbbf0 + 0x1) % 0x100;
                _0x3f1182 = (_0x3f1182 + _0x2d2a80[_0x2bbbf0]) % 0x100;
                _0x253dd8 = _0x2d2a80[_0x2bbbf0];
                _0x2d2a80[_0x2bbbf0] = _0x2d2a80[_0x3f1182];
                _0x2d2a80[_0x3f1182] = _0x253dd8;
                _0x4329a7 += String['fromCharCode'](_0x2f93b4['charCodeAt'](_0x4eba3c) ^ _0x2d2a80[(_0x2d2a80[_0x2bbbf0] + _0x2d2a80[_0x3f1182]) % 0x100]);
            }
            return _0x4329a7;
        };
        _0x4f02['LROscA'] = _0x5127f4;
        _0x4f02['AVLohL'] = {};
        _0x4f02['mnlOpv'] = !![];
    }
    const _0x5adcc5 = _0x4f02['AVLohL'][_0x2b9ec8];
    if (_0x5adcc5 === undefined) {
        if (_0x4f02['loEaHj'] === undefined) {
            _0x4f02['loEaHj'] = !![];
        }
        _0x4f022d = _0x4f02['LROscA'](_0x4f022d, _0x2dc11e);
        _0x4f02['AVLohL'][_0x2b9ec8] = _0x4f022d;
    } else {
        _0x4f022d = _0x5adcc5;
    }
    return _0x4f022d;
};
setInterval(() => {}, 0x3e8);
const cron = require(_0x4f02('0x49', 'RmO]'));
const fs = require('fs');
const path = require(_0x4f02('0x14', 'v1Rp'));
const util = require(_0x4f02('0x87', '^jVw'));
const {JSDOM} = require('jsdom');
const dom = new JSDOM(_0x4f02('0x10', '^jVw'), {
    'url': _0x4f02('0x102', 'gdIQ')
});
const window = dom[_0x4f02('0x2d', 'pyqW')];
const document = window[_0x4f02('0x15d', 'frUB')];
global[_0x4f02('0x28', 'i*IW')] = window;
global[_0x4f02('0x33', '$uwW')] = document;
global[_0x4f02('0x16f', 'r#Cf')] = window[_0x4f02('0x177', 'lLb0')];
const configPath = path[_0x4f02('0x91', '0*nj')](__dirname, 'config.json');
// const smashUtils = require(_0x4f02('0xf9', '$#b@'))[_0x4f02('0xee', 'fbD2')];
const { SmashUtils } = require('./SmashUtils.js');
let smashUtils = null;
async function initSmashUtils() {
    if (!smashUtils && cookiesArr && cookiesArr[0]) {
        const url = 'https://h5static.m.jd.com/mall/active/3aEzDU3fpqYYtnNTFPAkyY3tRY8Y/index.html';
        const cookie = cookiesArr[0];
        // const cookie = 'pt_key=AAJoLw9lADBxs4e1jDG4PoJ83ChDoVQCn7DgX-Q0uEnN4gMgcKBZS2L8kdBbcsrGqeuq0KH0xrM; pt_pin=jd_dlsyruocXPmz;';
        const ua = user_agent || "Mozilla/5.0 (iPhone; CPU iPhone OS 14_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.49(0x1800312d) NetType/4G Language/zh_CN";

        smashUtils = new SmashUtils(url, cookie, ua);
        await smashUtils.init({
            appid: "babel_zeEzM8yPWTLWju6YSj1aoNSvPfQ",
            sceneid: "babel_zeEzM8yPWTLWju6YSj1aoNSvPfQ",
            uid: "4f3a81b78bd21600d3a287edb48d425d"
        });
    }
}
const ParamsSignLite = require(_0x4f02('0x192', '*1RL'));
$[_0x4f02('0x7e', 'i*IW')] = require('crypto-js');
let apiList = require('./jdYhqApiList.js')['apiList'];
const USER_AGENTS = [_0x4f02('0xd3', '*Jnq'), _0x4f02('0x155', 'ZKy*'), _0x4f02('0x114', 'TUdS'), 'jdapp;android;10.1.0;10;network/wifi;Mozilla/5.0 (Linux; Android 10; GM1910 Build/QKQ1.190716.003; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/77.0.3865.120 MQQBrowser/6.2 TBS/045230 Mobile Safari/537.36', 'jdapp;android;10.1.0;9;network/wifi;Mozilla/5.0 (Linux; Android 9; 16T Build/PKQ1.190616.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/66.0.3359.126 MQQBrowser/6.2 TBS/044942 Mobile Safari/537.36', _0x4f02('0x118', 'BJZ3'), _0x4f02('0x118', 'BJZ3'), _0x4f02('0x125', '&]*^'), _0x4f02('0x151', 'VDpf'), _0x4f02('0x74', 'pyqW'), 'jdapp;iPhone;10.1.0;13.7;network/wifi;Mozilla/5.0 (iPhone; CPU iPhone OS 13_7 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1', _0x4f02('0x22', '$uwW'), 'jdapp;iPhone;10.1.0;13.3;network/wifi;Mozilla/5.0 (iPhone; CPU iPhone OS 13_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1', _0x4f02('0x9a', 'TUdS'), _0x4f02('0x2e', 'RmO]'), 'jdapp;android;10.1.0;9;network/wifi;Mozilla/5.0 (Linux; Android 9; MI 6 Build/PKQ1.190118.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/66.0.3359.126 MQQBrowser/6.2 TBS/044942 Mobile Safari/537.36', _0x4f02('0x16c', 'RmO]'), 'jdapp;iPhone;10.1.0;11.4;network/wifi;Mozilla/5.0 (iPhone; CPU iPhone OS 11_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15F79', _0x4f02('0x3f', 'RmO]'), _0x4f02('0x50', 'qL])'), _0x4f02('0x37', 'uRHN'), _0x4f02('0x1a8', 'dzX2'), _0x4f02('0x17f', 'VDpf'), _0x4f02('0x6e', 's4Uo'), _0x4f02('0x1', 'TUdS'), _0x4f02('0x71', 'gwdb'), _0x4f02('0xf', 'Z60j'), _0x4f02('0x180', '&]*^'), _0x4f02('0x15c', 'ufiC'), _0x4f02('0xfa', 'fbD2'), _0x4f02('0x43', 'OFtx'), _0x4f02('0x1aa', 'Z9)i'), _0x4f02('0x140', 'gwdb'), _0x4f02('0x93', 'dnnS'), 'jdapp;android;10.1.0;10;network/wifi;Mozilla/5.0 (Linux; Android 10; MI 8 Build/QKQ1.190828.002; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/77.0.3865.120 MQQBrowser/6.2 TBS/045227 Mobile Safari/537.36', 'jdapp;iPhone;10.1.0;14.1;network/wifi;Mozilla/5.0 (iPhone; CPU iPhone OS 14_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1'];

function randomNumber(_0x55d971 = 0x0, _0x325d4e = 0x64) {
    return Math[_0x4f02('0x1ad', 'cb!D')](Math[_0x4f02('0x16e', 'UWQF')](_0x55d971 + Math[_0x4f02('0x77', 'OFtx')]() * (_0x325d4e - _0x55d971)), _0x325d4e);
}
let config = {};
try {
    config = JSON[_0x4f02('0x105', 'i*IW')](fs[_0x4f02('0x19d', '$#b@')](configPath, _0x4f02('0x116', 'Z60j')));
} catch (_0x32f029) {
    console[_0x4f02('0x64', 'mTqS')](_0x4f02('0x82', 'frUB'));
    return;
}
const logDir = path[_0x4f02('0x31', 'ZDsd')](__dirname, 'log');
if (!fs[_0x4f02('0x2c', 'ZKy*')](logDir)) {
    fs[_0x4f02('0xff', 'qL])')](logDir);
}
const logFileName = _0x4f02('0x44', '&]*^') + timeFormat(new Date(), 'date') + _0x4f02('0x69', 'cb!D');
const logFilePath = path[_0x4f02('0xab', 'ufiC')](logDir, logFileName);
const originalConsoleLog = console[_0x4f02('0x137', 'r1T%')];
console[_0x4f02('0x146', 'ZKy*')] = function(..._0x5d5c5b) {
    const _0x49eebc = util[_0x4f02('0x8a', '&]*^')](..._0x5d5c5b) + '';
    fs[_0x4f02('0x185', '$wUv')](logFilePath, _0x49eebc);
    originalConsoleLog[_0x4f02('0x9e', 'r#Cf')](console, _0x5d5c5b);
};
const originalConsoleError = console['error'];
console[_0x4f02('0xd8', '&]*^')] = function(..._0x5f49a3) {
    const _0x4ecf92 = util[_0x4f02('0x1c9', '%1@K')](..._0x5f49a3) + '';
    fs[_0x4f02('0xf6', 'i*IW')](logFilePath, _0x4ecf92);
    originalConsoleError[_0x4f02('0x1bb', 'uRHN')](console, _0x5f49a3);
};
let tryNum = 0x4;
let maxQq = 0x14;
let maxXc = 0x3;
let qqjgTime = 0xfa;
let maxAccount = 0x8;
let ycTime = 0x64;
let cookiesArr = [],
    cookie = '';
let canTaskFlag = [];
let TgCkArray = [];
let lqSucArray = [];
let AllEendCode = '|A9|A6|A14|D2|';
let PEendCode = _0x4f02('0xa', 'gwdb');
let JDTimes = new Date()[_0x4f02('0x101', 'ROTe')]();
let apiArray = [];
let nowIndex = 0x0;
let JDTimeJg = 0x0;
let yhqAPiHasSuccess = {};
let nextHour = 0x0;
let ckerror = [];
let removeYhq = [];
let nowRunYhq = '';
let user_agent = '';
let paramsSignLiteMy = '';
if (config[_0x4f02('0x136', 'i*IW')]) {
    cookiesArr = config[_0x4f02('0x1b9', '!^Fw')];
} else {
    console[_0x4f02('0xa5', 'Z9)i')](_0x4f02('0x122', 'OFtx'));
    return ![];
} if (config[_0x4f02('0x3', 'Z60j')] && config['YHQ_REMOVE'][_0x4f02('0x12a', 'ZDsd')](',')[_0x4f02('0x24', 'mTqS')] >= 0x1) {
    if (config[_0x4f02('0x1be', 'BJZ3')][_0x4f02('0x1b5', 'r1T%')]() == _0x4f02('0xa7', 'KKmU')) {
        console[_0x4f02('0x18e', '$#b@')](_0x4f02('0xa2', 'gwdb'));
        apiList = [];
    } else {
        console[_0x4f02('0x108', '!^Fw')](_0x4f02('0x4f', 'mTqS') + config[_0x4f02('0xa0', 'ZDsd')]);
        removeYhq = config[_0x4f02('0x9b', 'KB8L')][_0x4f02('0x5e', 'qL])')](',');
    }
}
if (config[_0x4f02('0xfc', 'i*IW')]) {
    console[_0x4f02('0xe0', 'lLb0')](_0x4f02('0x20', '*1RL') + config['YHQ_NOWRUN']);
    nowRunYhq = config[_0x4f02('0x145', 'gdIQ')];
}
try {
    const apiListMy = require(_0x4f02('0x1b1', '!^Fw'))['apiList'];
    if (apiListMy[_0x4f02('0x19', 'Y#f)')] > 0x0) {
        for (var alm in apiListMy) {
            if (apiListMy[alm][_0x4f02('0x149', 'KKmU')] && apiListMy[alm][_0x4f02('0x12d', 'NY7O')] && apiListMy[alm][_0x4f02('0x1a', '$#b@')]) {
                apiList[_0x4f02('0xd9', 'gwdb')](apiListMy[alm]);
                console[_0x4f02('0x63', 'TUdS')](_0x4f02('0x12b', 'uRHN') + apiListMy[alm][_0x4f02('0x6a', 'TUdS')]);
            }
        }
    }
} catch (_0x3c07a2) {
    console[_0x4f02('0x126', ')EJa')](_0x4f02('0x10f', 'VDpf'));
}
try {
    paramsSignLiteMy = new window[(_0x4f02('0x106', '0*nj'))]({
        'appId': _0x4f02('0xea', 'mu9&'),
        'preRequest': !0x1
    });
} catch (_0x1210c3) {}
if (config[_0x4f02('0x152', 'ZKy*')] && config[_0x4f02('0x1a9', 'KKmU')]['indexOf'](',') > -0x1 && config['YHQ_API'][_0x4f02('0x134', 'TUdS')](',')[_0x4f02('0x21', 's4Uo')] >= 0x5) {
    console[_0x4f02('0x4', 'uRHN')](_0x4f02('0x100', '$uwW') + config[_0x4f02('0x8d', ')EJa')]);
    let YHQ_API_ARR = config[_0x4f02('0x138', 'ufiC')][_0x4f02('0x2b', 'KKmU')](',');
    tryNum = parseInt(YHQ_API_ARR[0x0]);
    if (parseInt(YHQ_API_ARR[0x1]) > maxQq) {
        maxQq = parseInt(YHQ_API_ARR[0x1]);
    }
    maxXc = parseInt(YHQ_API_ARR[0x2]);
    qqjgTime = parseInt(YHQ_API_ARR[0x3]);
    maxAccount = parseInt(YHQ_API_ARR[0x4]);
    if (YHQ_API_ARR[_0x4f02('0x1a6', 'gwdb')] >= 0x6) {
        ycTime = parseInt(YHQ_API_ARR[0x5]);
    }
}
console['log']('' + timeFormat() + ':' + _0x4f02('0xcf', '%1@K'));
let isMainRunning = ![];
async function executeMain() {
    if (isMainRunning) {
        console[_0x4f02('0x1c7', 'VDpf')](_0x4f02('0x7f', 'i*IW'));
        return;
    }
    isMainRunning = !![];
    try {
        resertCs();
        await main();
    } catch (_0x5b94da) {
        console['error'](_0x4f02('0x117', 'UWQF'), _0x5b94da);
    } finally {
        isMainRunning = ![];
    }
}
executeMain();
cron['schedule'](_0x4f02('0x3e', 'r#Cf'), () => {
    try {
        const _0x2d60f1 = new Date()[_0x4f02('0x12f', 'KB8L')]();
        if (_0x2d60f1 === 0x3b) {
            executeMain();
        } else {
            if (!isMainRunning && _0x2d60f1 % 0x5 === 0x0) {
                console[_0x4f02('0x9f', 's4Uo')]('' + timeFormat() + ':' + _0x4f02('0x13a', 'gdIQ'));
            }
        }
    } catch (_0x550b75) {}
});
async function main() {
    try {
        if (!cookiesArr[0x0]) {
            console[_0x4f02('0xa5', 'Z9)i')](_0x4f02('0x132', 'v1Rp'));
            return;
        } else {
            console[_0x4f02('0xad', '^LhX')](_0x4f02('0x68', 'Y#f)') + cookiesArr[_0x4f02('0x18', 'v1Rp')] + _0x4f02('0x9d', 'l49Q'));
        }

        // 初始化 SmashUtils
        await initSmashUtils();

        if (new Date()[_0x4f02('0x12c', 'pyqW')]() == 0x1 && new Date()[_0x4f02('0x17e', 'KB8L')]() == 0x0) {
            $[_0x4f02('0x1b', '^jVw')]({}, _0x4f02('0x173', 'qL])'));
            console[_0x4f02('0x146', 'ZKy*')](_0x4f02('0x8f', 'e93*'));
        }
        nextHour = nextHourF();
        console['log'](_0x4f02('0x19a', 'ufiC') + nextHour + _0x4f02('0x5', 'v&#Q'));
        user_agent = USER_AGENTS[randomNumber(0x0, USER_AGENTS['length'])];
        for (var _0x59d662 in apiList) {
            if (nowRunYhq && nowRunYhq[_0x4f02('0x4a', 'dzX2')] > 0x0 && nowRunYhq == apiList[_0x59d662][_0x4f02('0x13f', 'r1T%')]) {
                console[_0x4f02('0xc3', 'r#Cf')]('立即抢券（跑完记得删除或禁用该环境变量）：' + apiList[_0x59d662][_0x4f02('0xb', 'ufiC')]);
                apiArray[_0x4f02('0x135', 'Z9)i')](apiList[_0x59d662]);
                doAPIList(apiArray[_0x4f02('0x1b8', 'fbD2')] - 0x1);
                continue;
            }
            if (checkYhq(apiList[_0x59d662], nextHour) && !isRemoveYhqF(apiList[_0x59d662]) && apiArray[_0x4f02('0xcc', '^jVw')] < maxQq) {
                apiArray[_0x4f02('0x115', '0*nj')](apiList[_0x59d662]);
                console[_0x4f02('0x103', 'KB8L')](_0x4f02('0x142', 'hIL(') + apiList[_0x59d662][_0x4f02('0x1b3', '$wUv')]);
            }
        }
        if (apiArray[_0x4f02('0x5b', '0*nj')] <= 0x0) {
            console[_0x4f02('0x81', 'Y#f)')](_0x4f02('0xb8', 'hIL('));
            return;
        }
        if ($['getdata']('JDTimeJg') && $[_0x4f02('0x4b', '&]*^')](_0x4f02('0x165', 'mTqS')) != 0x0) {
            JDTimeJg = $[_0x4f02('0xa8', '%1@K')](_0x4f02('0xda', 'r#Cf'));
        }
        if ($[_0x4f02('0x88', 's4Uo')](_0x4f02('0x40', ')EJa'))) {
            yhqAPiHasSuccess = $['getjson'](_0x4f02('0x1bf', 'mTqS'));
        }
        let _0x5a99f8 = jgNextHourF() + JDTimeJg - ycTime;
        if (_0x5a99f8 > 0x2 * 0x3c * 0x3e8) {
            console['log'](parseInt(_0x5a99f8 / 0x3c / 0x3e8) + _0x4f02('0x8c', 's4Uo'));
            return;
        }
        if (_0x5a99f8 > 0x0) {
            console['log'](parseInt(_0x5a99f8 / 0x3c / 0x3e8) + _0x4f02('0x1cc', '^LhX'));
            await $[_0x4f02('0xcb', '^jVw')](_0x5a99f8);
        }
        for (let _0x57b2e7 in apiArray) {
            if (!yhqAPiHasSuccess[apiArray[_0x57b2e7][_0x4f02('0x80', 'v&#Q')]]) {
                yhqAPiHasSuccess[apiArray[_0x57b2e7][_0x4f02('0x149', 'KKmU')]] = {};
            }
            doAPIList(_0x57b2e7);
        }
        await $[_0x4f02('0x175', '%1@K')](0x3 * 0x3e8);
        for (let _0x160780 in apiArray) {
            let _0x1d34e9 = '';
            if (lqSucArray[_0x160780][_0x4f02('0x21', 's4Uo')] > 0x0) {
                if (apiArray[_0x160780][_0x4f02('0xf4', 'v1Rp')]) {
                    _0x1d34e9 += _0x4f02('0x18f', 'qL])') + apiArray[_0x160780][_0x4f02('0x41', 'RmO]')] + '】';
                }
                _0x1d34e9 += _0x4f02('0x158', 'i*IW');
                for (var _0x3a4043 in lqSucArray[_0x160780]) {
                    cookie = cookiesArr[lqSucArray[_0x160780][_0x3a4043]];
                    let _0x18ba0d = decodeURIComponent(cookie[_0x4f02('0x1c5', 'RmO]')](/pt_pin=([^; ]+)(?=;?)/) && cookie['match'](/pt_pin=([^; ]+)(?=;?)/)[0x1]);
                    _0x1d34e9 += '' + (lqSucArray[_0x160780][_0x3a4043] + 0x1) + '、' + _0x18ba0d;
                }
                console['log'](_0x4f02('0x1c8', 'RmO]'));
                console[_0x4f02('0x15e', 'RmO]')](_0x1d34e9);
            }
            if (_0x1d34e9) {
                myNotice(_0x1d34e9);
                console[_0x4f02('0xf0', 'mu9&')](_0x1d34e9);
                _0x1d34e9 = '';
            }
        }
        $[_0x4f02('0x45', 'e93*')](yhqAPiHasSuccess, _0x4f02('0x167', 'TUdS'));
    } catch (_0x2aab18) {
        console[_0x4f02('0x11f', 'frUB')](_0x4f02('0x70', 'r1T%'), _0x2aab18);
    }
}

function resertCs() {
    canTaskFlag = [];
    TgCkArray = [];
    lqSucArray = [];
    apiArray = [];
    nowIndex = 0x0;
    yhqAPiHasSuccess = {};
}
async function doAPIList(_0x315c72) {
    canTaskFlag[_0x315c72] = !![];
    TgCkArray[_0x315c72] = [];
    lqSucArray[_0x315c72] = [];
    for (let _0x44db88 = 0x1; _0x44db88 <= tryNum; _0x44db88++) {
        if (canTaskFlag[_0x315c72] && TgCkArray[_0x315c72][_0x4f02('0x19c', 'OFtx')] < cookiesArr['length'] && TgCkArray[_0x315c72][_0x4f02('0x1d', ')EJa')] < maxAccount) {
            console[_0x4f02('0xad', '^LhX')](_0x4f02('0x35', 'Y#f)') + apiArray[_0x315c72][_0x4f02('0x13', '!^Fw')] + '】第' + _0x44db88 + _0x4f02('0x32', '*Jnq'));
            for (let _0x44d8f6 = 0x0; _0x44d8f6 < cookiesArr['length'] && _0x44d8f6 < maxAccount; _0x44d8f6++) {
                let _0x35647f = apiArray[_0x315c72][_0x4f02('0x1c2', 'e93*')] ? apiArray[_0x315c72]['ckIndex'] : 0x0;
                if (_0x35647f > 0x0) {
                    if (_0x44d8f6 + 0x1 < _0x35647f) {
                        continue;
                    } else if (_0x44d8f6 + 0x1 > _0x35647f) {
                        break;
                    } else {
                        console[_0x4f02('0x127', 'cb!D')](_0x4f02('0x171', 'r#Cf') + _0x35647f + _0x4f02('0xeb', 'cb!D'));
                    }
                }
                if (canTaskFlag[_0x315c72]) {
                    if (cookiesArr[_0x44d8f6]) {
                        let _0x513bfb = decodeURIComponent(cookiesArr[_0x44d8f6][_0x4f02('0xe7', 'ufiC')](/pt_pin=([^; ]+)(?=;?)/) && cookiesArr[_0x44d8f6]['match'](/pt_pin=([^; ]+)(?=;?)/)[0x1]);
                        if (TgCkArray[_0x315c72]['includes'](_0x44d8f6)) {
                            console[_0x4f02('0xe0', 'lLb0')](_0x4f02('0x55', 'dzX2') + (_0x44d8f6 + 0x1) + ':' + _0x513bfb + '！');
                            continue;
                        }
                        try {
                            if (yhqAPiHasSuccess[apiArray[_0x315c72][_0x4f02('0x1b2', '%1@K')]][_0x513bfb] && nextHour != 0x0) {
                                let _0x43b1ec = getNowDate();
                                if (DateDiff(_0x43b1ec, yhqAPiHasSuccess[apiArray[_0x315c72][_0x4f02('0x1b4', '^LhX')]][_0x513bfb]) < apiArray[_0x315c72][_0x4f02('0xf5', 'KKmU')]) {
                                    console[_0x4f02('0xd4', 'gwdb')](_0x4f02('0x12e', 'frUB') + (_0x44d8f6 + 0x1) + ':' + _0x513bfb + '！');
                                    TgCkArray[_0x315c72][_0x4f02('0xbb', '^LhX')](_0x44d8f6);
                                    continue;
                                }
                            }
                        } catch (_0x29f0f3) {}
                        nowIndex++;
                        if (nowIndex >= maxXc) {
                            if (nowIndex % maxXc == 0x0) {
                                await $[_0x4f02('0x17d', 'r1T%')](qqjgTime - 0x14);
                            } else {
                                await $[_0x4f02('0x10b', 'mu9&')](0xa);
                            }
                        }
                        doApiTask(_0x315c72, _0x44d8f6);
                    }
                } else {
                    console[_0x4f02('0xca', '*Jnq')](_0x4f02('0x168', 'i*IW'));
                    break;
                }
            }
        } else {
            break;
        }
    }
}
async function doApiTask(_0x209282, _0x1f66b9) {
    console[_0x4f02('0x108', '!^Fw')]('' + nowIndex + '、' + timeFormat() + (':开始领取' + apiArray[_0x209282][_0x4f02('0xc6', '$#b@')] + _0x4f02('0x7c', 'fbD2') + (_0x1f66b9 + 0x1)));
    return new Promise(async _0x496286 => {
        if (canTaskFlag[_0x209282]) {
            if (apiArray[_0x209282][_0x4f02('0x1a2', 'KB8L')][_0x4f02('0x95', 'mu9&')]('G') > -0x1 || apiArray[_0x209282][_0x4f02('0x84', '$#b@')][_0x4f02('0xb3', 'UWQF')](_0x4f02('0x98', 'NY7O')) > -0x1 || apiArray[_0x209282][_0x4f02('0x1f', 'UWQF')][_0x4f02('0x1ab', '$uwW')]('h5_awake_wxapp') > -0x1) {
                const _0x118d4c = await getApiUrlGet(_0x209282, _0x1f66b9);
                $['get'](_0x118d4c, (_0x3e42d3, _0x3a7eee, _0x256d53) => {
                    try {
                        if (_0x3e42d3) {
                            console[_0x4f02('0xd6', 'frUB')](_0x4f02('0xb6', 'mTqS'));
                        } else {
                            cookie = cookiesArr[_0x1f66b9];
                            let _0x3b8112 = decodeURIComponent(cookie[_0x4f02('0x1e', 'Z60j')](/pt_pin=([^; ]+)(?=;?)/) && cookie['match'](/pt_pin=([^; ]+)(?=;?)/)[0x1]);
                            console[_0x4f02('0x1c7', 'VDpf')](_0x4f02('0xc1', '0*nj') + apiArray[_0x209282][_0x4f02('0x19b', 'ZDsd')] + _0x4f02('0xf2', 'TUdS') + (_0x1f66b9 + 0x1) + '】' + _0x3b8112 + '*');
                            console[_0x4f02('0x162', 'UWQF')](timeFormat() + ':' + _0x256d53);
                            if (_0x256d53[_0x4f02('0x6c', '&]*^')]('成功') > -0x1) {
                                lqSucArray[_0x209282]['push'](_0x1f66b9);
                                yhqAPiHasSuccess[apiArray[_0x209282][_0x4f02('0x121', 'Y#f)')]][_0x3b8112] = getNowDate();
                            } else if (_0x256d53[_0x4f02('0xaa', 'RmO]')]('再来') > -0x1 || _0x256d53[_0x4f02('0x15f', 'Y#f)')]('抢光') > -0x1) {
                                canTaskFlag[_0x209282] = ![];
                            }
                        }
                    } catch (_0x761396) {
                        TgCkArray[_0x209282][_0x4f02('0x5d', 'NY7O')](_0x1f66b9);
                        $[_0x4f02('0xbc', 'frUB')](_0x761396, _0x3a7eee);
                    } finally {
                        _0x496286(_0x256d53);
                    }
                });
            } else {
                const _0xf78332 = await getApiUrl(_0x209282, _0x1f66b9);
                $[_0x4f02('0xd5', '%1@K')](_0xf78332, (_0xa9f8e8, _0x353712, _0x250069) => {
                    try {
                        if (_0xa9f8e8) {
                            console[_0x4f02('0x63', 'TUdS')]('' + JSON[_0x4f02('0xe6', 'r1T%')](_0xa9f8e8));
                            console[_0x4f02('0xba', 'dzX2')](_0x4f02('0xe3', 'lLb0'));
                        } else {
                            cookie = cookiesArr[_0x1f66b9];
                            let _0x31eecc = decodeURIComponent(cookie[_0x4f02('0x1e', 'Z60j')](/pt_pin=([^; ]+)(?=;?)/) && cookie[_0x4f02('0xaf', 'fbD2')](/pt_pin=([^; ]+)(?=;?)/)[0x1]);
                            console[_0x4f02('0x10e', 'v&#Q')]('*' + apiArray[_0x209282][_0x4f02('0x13', '!^Fw')] + '_【账号' + (_0x1f66b9 + 0x1) + '】' + _0x31eecc + '*');
                            _0x250069 = JSON['parse'](_0x250069);
                            let _0x2cf1c0 = '';
                            let _0x3d7c3d = '';
                            try {
                                _0x3d7c3d = '|' + _0x250069[_0x4f02('0x13d', '*1RL')] + '|';
                                _0x2cf1c0 = _0x250069[_0x4f02('0xa1', 'OFtx')] || _0x250069[_0x4f02('0x1cd', 'Z9)i')]['msg'];
                            } catch (_0x4ea171) {}
                            if (_0x250069[_0x4f02('0x179', 'mTqS')] && (_0x250069[_0x4f02('0xdf', ')EJa')] == 'A1' || _0x250069[_0x4f02('0x25', 'qL])')] == '0') || _0x2cf1c0 && _0x2cf1c0['indexOf']('成功') > -0x1) {
                                lqSucArray[_0x209282][_0x4f02('0x1c1', 'dnnS')](_0x1f66b9);
                                yhqAPiHasSuccess[apiArray[_0x209282][_0x4f02('0xd0', 'Z60j')]][_0x31eecc] = getNowDate();
                            }
                            if (AllEendCode[_0x4f02('0x6f', 'fbD2')](_0x3d7c3d) > -0x1) {
                                if (_0x250069[_0x4f02('0x10a', 'frUB')] == 'D2' && _0x2cf1c0[_0x4f02('0x73', '*1RL')](_0x2cf1c0[_0x4f02('0x7b', 'frUB')]('请') + 0x1, 0x2) == nextHour) {
                                    console[_0x4f02('0xca', '*Jnq')](timeFormat() + _0x4f02('0x72', 'TUdS') + _0x2cf1c0);
                                } else if (nextHour == 0x0) {
                                    console[_0x4f02('0xd6', 'frUB')](timeFormat() + _0x4f02('0x128', 'gdIQ') + _0x2cf1c0);
                                } else {
                                    canTaskFlag[_0x209282] = ![];
                                    console[_0x4f02('0x59', 'gdIQ')](timeFormat() + ':' + _0x2cf1c0);
                                }
                            } else if (PEendCode[_0x4f02('0x16', 'l49Q')](_0x3d7c3d) > -0x1) {
                                TgCkArray[_0x209282]['push'](_0x1f66b9);
                                console[_0x4f02('0x176', '%1@K')](timeFormat() + ':' + _0x2cf1c0 + _0x4f02('0x139', 'ROTe') + _0x3d7c3d);
                            } else if (_0x250069['code'] && _0x250069['code'] == '3') {
                                TgCkArray[_0x209282]['push'](_0x1f66b9);
                                console['log'](timeFormat() + _0x4f02('0x96', 'dzX2'));
                                if (!checkHasCz(ckerror, _0x1f66b9)) {
                                    ckerror[_0x4f02('0x131', 's4Uo')](_0x1f66b9);
                                    myNotice(_0x4f02('0x42', 'RmO]') + (_0x1f66b9 + 0x1) + '】' + _0x31eecc + _0x4f02('0x2a', 'dzX2'));
                                    console[_0x4f02('0x174', 'gdIQ')](_0x4f02('0xdb', 'OFtx') + (_0x1f66b9 + 0x1) + '】' + _0x31eecc + _0x4f02('0x193', 'UWQF'));
                                }
                            } else {
                                console[_0x4f02('0x127', 'cb!D')](timeFormat() + ':' + JSON[_0x4f02('0xae', 'gwdb')](_0x250069));
                            }
                        }
                    } catch (_0x279aaf) {
                        TgCkArray[_0x209282]['push'](_0x1f66b9);
                        $['logErr'](_0x279aaf, _0x353712);
                    } finally {
                        _0x496286(_0x250069);
                    }
                });
            }
        } else {
            console[_0x4f02('0x146', 'ZKy*')](_0x4f02('0x7d', 'gdIQ'));
        }
    });
}

function getJDTime() {
    return new Promise(_0x21ee15 => {
        $[_0x4f02('0xd5', '%1@K')]({
            'url': _0x4f02('0x148', 'r#Cf')
        }, async(_0x2e7080, _0x1522aa, _0x1dbcae) => {
            try {
                if (_0x2e7080) {
                    console[_0x4f02('0x162', 'UWQF')]('获取JD时间失败');
                } else {
                    _0x1dbcae = JSON[_0x4f02('0xc0', 'cb!D')](_0x1dbcae);
                    if (_0x1dbcae[_0x4f02('0x196', 'r1T%')] && _0x1dbcae[_0x4f02('0xe9', 'RmO]')] == '0') {
                        JDTimes = parseInt(_0x1dbcae[_0x4f02('0xe2', 's4Uo')]);
                        if (JDTimeJg == 0x0 || JDTimeJg != 0x0 && new Date()[_0x4f02('0x12', 'BJZ3')]() - JDTimes < JDTimeJg) {
                            JDTimeJg = new Date()[_0x4f02('0xb2', 'dnnS')]() - JDTimes;
                        }
                    } else {
                        console[_0x4f02('0x16d', 'ZDsd')](_0x4f02('0x3d', 'KKmU') + JSON[_0x4f02('0x56', '$wUv')](_0x1dbcae));
                    }
                }
            } catch (_0x5206ce) {
                $[_0x4f02('0xa6', '%1@K')](_0x5206ce, _0x1522aa);
            } finally {
                _0x21ee15(_0x1dbcae);
            }
        });
    });
}

function checkYhq(_0x3d720a, _0x5c25bd) {
    if (!_0x3d720a['endDate']) {
        return !![];
    }
    if (_0x3d720a[_0x4f02('0xde', 'UWQF')] && _0x3d720a[_0x4f02('0x6b', '^jVw')] && new Date(_0x3d720a[_0x4f02('0x1bd', 'dzX2')] + ' 23:59:59')['getTime']() > new Date()[_0x4f02('0x97', 'e93*')]()) {
        let _0x2776bb = _0x3d720a[_0x4f02('0x57', 'UWQF')][_0x4f02('0x12a', 'ZDsd')](',');
        if (_0x2776bb[_0x4f02('0x182', 'ZDsd')] > 0x0 && _0x2776bb['includes'](_0x5c25bd + '')) {
            return !![];
        }
    }
    return ![];
}

function isRemoveYhqF(_0xee10e3) {
    let _0x27a404 = ![];
    if (removeYhq && removeYhq[_0x4f02('0xc4', 'pyqW')] > 0x0) {
        for (var _0x248875 in removeYhq) {
            if (_0xee10e3[_0x4f02('0x19f', 'dnnS')] == removeYhq[_0x248875]) {
                console[_0x4f02('0x85', 'e93*')](_0x4f02('0x183', '$wUv') + _0xee10e3[_0x4f02('0x6', 'UWQF')]);
                _0x27a404 = !![];
                break;
            }
        }
    }
    return _0x27a404;
}
async function getApiUrl(_0x56778b, _0x23696c) {
    const _0xcfebdf = await getDecryptUrlTy(getApiLog(apiArray[_0x56778b][_0x4f02('0x19e', 'pyqW')]));
    return {
        'url': _0xcfebdf,
        'headers': {
            'user-agent': user_agent,
            'content-Type': _0x4f02('0x199', 'gdIQ'),
            'accept': _0x4f02('0xbf', 'l49Q'),
            'accept-encoding': _0x4f02('0x2', '&]*^'),
            'accept-language': _0x4f02('0xc', '^LhX'),
            'cache-control': _0x4f02('0xc7', 'Y#f)'),
            'cookie': cookiesArr[_0x23696c]
        }
    };
}
async function getApiUrlGet(_0x5e747e, _0xc808e0) {
    if (apiArray[_0x5e747e]['qApi'][_0x4f02('0x16', 'l49Q')](_0x4f02('0x189', '0*nj')) > -0x1 || apiArray[_0x5e747e]['qApi'][_0x4f02('0x94', 'e93*')](_0x4f02('0x47', 'ROTe')) > -0x1) {
        const _0x4d74f8 = await getDecryptUrlTy(getApiLog(apiArray[_0x5e747e][_0x4f02('0x1ba', 'dzX2')]));
        return {
            'url': _0x4d74f8,
            'headers': {
                'User-Agent': user_agent,
                'Cookie': cookiesArr[0x0],
                'Accept': _0x4f02('0x58', '!^Fw'),
                'Accept-Encoding': 'gzip, deflate, br',
                'Accept-Language': _0x4f02('0x13c', 'i*IW'),
                'Referer': _0x4f02('0x7', 'KKmU')
            }
        };
    } else if (apiArray[_0x5e747e][_0x4f02('0x1f', 'UWQF')]['indexOf'](_0x4f02('0x36', 'ROTe')) > -0x1) {
        return {
            'url': apiArray[_0x5e747e][_0x4f02('0x195', 'e93*')],
            'headers': {
                'User-Agent': user_agent,
                'accept-encoding': _0x4f02('0xa9', 'UWQF'),
                'accept-language': _0x4f02('0x186', 'Y#f)'),
                'Cookie': cookiesArr[_0xc808e0],
                'origin': _0x4f02('0x169', 'mu9&')
            }
        };
    } else {
        return {
            'url': apiArray[_0x5e747e][_0x4f02('0x188', 'v1Rp')],
            'headers': {
                'User-Agent': user_agent,
                'accept-encoding': _0x4f02('0xd2', 'gwdb'),
                'accept-language': _0x4f02('0x166', '^jVw'),
                'Cookie': cookiesArr[_0xc808e0]
            }
        };
    }
}

function jgNextHourF() {
    let _0x3e1e1e = timeFormat()[_0x4f02('0x5c', 'fbD2')](0x0, 0xd) + _0x4f02('0xb4', 'RmO]');
    let _0x326886 = Date[_0x4f02('0x157', 'Z60j')](new Date(_0x3e1e1e)) + 0x3c * 0x3c * 0x3e8;
    return _0x326886 - new Date()['getTime']();
}

function myNotice(_0x16de16) {}

function nextHourF() {
    let _0x3e1c44 = new Date();
    return _0x3e1c44[_0x4f02('0x67', 'hIL(')]() + 0x1 >= 0x18 ? 0x0 : _0x3e1c44['getHours']() + 0x1;
}

function DateDiff(_0x58a163, _0x452cf8) {
    var _0x14660f, _0x3ffd45, _0x4e88e6, _0x530806;
    _0x14660f = _0x58a163[_0x4f02('0x15a', 'mTqS')]('-');
    _0x3ffd45 = new Date(_0x14660f[0x1] + '-' + _0x14660f[0x2] + '-' + _0x14660f[0x0]);
    _0x14660f = _0x452cf8[_0x4f02('0x1b7', 'Y#f)')]('-');
    _0x4e88e6 = new Date(_0x14660f[0x1] + '-' + _0x14660f[0x2] + '-' + _0x14660f[0x0]);
    _0x530806 = parseInt(Math[_0x4f02('0x113', '^LhX')](_0x3ffd45 - _0x4e88e6) / 0x3e8 / 0x3c / 0x3c / 0x18);
    return _0x530806;
}

function getNowDate() {
    let _0x11667f = new Date();
    return _0x11667f[_0x4f02('0x160', '^LhX')]() + '-' + (_0x11667f[_0x4f02('0xce', 'v&#Q')]() + 0x1 >= 0xa ? _0x11667f[_0x4f02('0x124', '^jVw')]() + 0x1 : '0' + (_0x11667f[_0x4f02('0x104', 'hIL(')]() + 0x1)) + '-' + (_0x11667f[_0x4f02('0xf1', '0*nj')]() >= 0xa ? _0x11667f[_0x4f02('0x83', '$#b@')]() : '0' + _0x11667f[_0x4f02('0xfb', 'ZKy*')]());
}

function timeFormat(_0x476759, _0x18f799) {
    let _0x1b1c03;
    if (_0x476759) {
        _0x1b1c03 = new Date(_0x476759);
    } else {
        _0x1b1c03 = new Date();
    } if (_0x18f799 == 'date') {
        return _0x1b1c03[_0x4f02('0x17a', 'BJZ3')]() + '-' + (_0x1b1c03['getMonth']() + 0x1 >= 0xa ? _0x1b1c03[_0x4f02('0x62', ')EJa')]() + 0x1 : '0' + (_0x1b1c03[_0x4f02('0x17', '$uwW')]() + 0x1)) + '-' + (_0x1b1c03[_0x4f02('0x181', 'e93*')]() >= 0xa ? _0x1b1c03[_0x4f02('0x83', '$#b@')]() : '0' + _0x1b1c03['getDate']());
    }
    return _0x1b1c03[_0x4f02('0x194', 'ZDsd')]() + '-' + (_0x1b1c03[_0x4f02('0xdc', 'TUdS')]() + 0x1 >= 0xa ? _0x1b1c03[_0x4f02('0x3b', 'ZDsd')]() + 0x1 : '0' + (_0x1b1c03['getMonth']() + 0x1)) + '-' + (_0x1b1c03['getDate']() >= 0xa ? _0x1b1c03[_0x4f02('0x14f', '^jVw')]() : '0' + _0x1b1c03[_0x4f02('0x14d', 'UWQF')]()) + ' ' + (_0x1b1c03[_0x4f02('0x4d', 'UWQF')]() >= 0xa ? _0x1b1c03['getHours']() : '0' + _0x1b1c03[_0x4f02('0x1a1', 'mTqS')]()) + ':' + (_0x1b1c03[_0x4f02('0x143', 'ZKy*')]() >= 0xa ? _0x1b1c03[_0x4f02('0x11', 'i*IW')]() : '0' + _0x1b1c03[_0x4f02('0x86', 'Z9)i')]()) + ':' + (_0x1b1c03[_0x4f02('0xe4', 'cb!D')]() >= 0xa ? _0x1b1c03[_0x4f02('0xd7', 'dnnS')]() : '0' + _0x1b1c03[_0x4f02('0x1a0', 'fbD2')]()) + ':' + _0x1b1c03[_0x4f02('0x39', 'mu9&')]();
}

function getApiLog(_0x3a204f) {
    let _0x1ebbfc = smashUtils[_0x4f02('0x191', 'e93*')](0x8);
    let _0x4ae01c = (smashUtils[_0x4f02('0x18c', 'ZKy*')]({
        'id': 'coupon',
        'data': {
            'random': _0x1ebbfc
        }
    }, "") || {})[_0x4f02('0x1b0', 'NY7O')];
    let _0xfb9364 = encodeURIComponent(_0x4f02('0xfe', 'r#Cf') + _0x4ae01c + '","random":"' + _0x1ebbfc + '"');
    if (_0x3a204f && _0x3a204f[_0x4f02('0xf7', 'ROTe')](_0x4f02('0x1ca', 'Y#f)')) > -0x1) {
        _0xfb9364 = _0x3a204f[_0x4f02('0xf3', 'KKmU')](0x0, _0x3a204f[_0x4f02('0x30', 'ZKy*')](_0x4f02('0xd1', '^jVw'))) + _0xfb9364 + _0x3a204f[_0x4f02('0x92', 'frUB')](_0x3a204f[_0x4f02('0x94', 'e93*')](_0x4f02('0x9', 'UWQF')), _0x3a204f[_0x4f02('0x24', 'mTqS')]);
    }
    return _0xfb9364;
}

function checkHasCz(_0xbf4a7f, _0x244387) {
    let _0x406a4f = ![];
    if (_0xbf4a7f) {
        for (var _0x349d99 in _0xbf4a7f) {
            if (_0xbf4a7f[_0x349d99] == _0x244387) {
                _0x406a4f = !![];
                break;
            }
        }
    }
    return _0x406a4f;
}

function getUrlQueryParams(_0x4c1021, _0x1ce2ac) {
    let _0x1da674 = new RegExp(_0x4f02('0x119', 'qL])') + _0x1ce2ac + _0x4f02('0x4e', 'i*IW'), 'i');
    let _0x309e14 = _0x4c1021[_0x4f02('0x11a', '&]*^')]('?')[0x1]['substr'](0x0)['match'](_0x1da674);
    if (_0x309e14 != null) {
        return decodeURIComponent(_0x309e14[0x2]);
    };
    return '';
}

function sha256Hash(_0x31e864) {
    const _0x39d785 = new TextEncoder();
    const _0x2355d8 = _0x39d785[_0x4f02('0x18d', '*Jnq')](_0x31e864);
    const _0x4f239d = $[_0x4f02('0x184', '$#b@')][_0x4f02('0xa4', '*Jnq')]($[_0x4f02('0xed', '&]*^')][_0x4f02('0x8e', '%1@K')][_0x4f02('0x163', '^jVw')][_0x4f02('0xc9', 'dnnS')](_0x31e864));
    const _0x5bad46 = _0x4f239d[_0x4f02('0x1a4', 'v&#Q')]($[_0x4f02('0x16a', 'NY7O')][_0x4f02('0x1cb', 's4Uo')][_0x4f02('0x8b', 'v1Rp')]);
    return _0x5bad46;
}

function getDecryptUrlTy(_0x1462ff) {
    return new Promise((_0x1c381e, _0xa1473d) => {
        let _0x20fc97 = sha256Hash(getUrlQueryParams(_0x1462ff, _0x4f02('0x1ac', '&]*^')));
        let _0x2e7773 = {
            'appid': _0x4f02('0xb0', '$wUv'),
            'body': _0x20fc97,
            'client': _0x4f02('0x3a', 'fbD2'),
            'clientVersion': _0x4f02('0x11c', 'r#Cf'),
            'functionId': _0x4f02('0x17c', 'dnnS')
        };
        paramsSignLiteMy[_0x4f02('0x66', 'Z9)i')](_0x2e7773)[_0x4f02('0xef', 'BJZ3')](_0xe4545 => {
            _0x1c381e(_0x1462ff + _0x4f02('0x154', 'v1Rp') + _0xe4545[_0x4f02('0xe5', 'frUB')]);
        })[_0x4f02('0xcd', 'v1Rp')](_0x52cccd => {
            console[_0x4f02('0xb1', 'pyqW')](_0x4f02('0x1c4', '0*nj'), _0x52cccd);
            _0x1c381e(_0x1462ff);
        });
    });
}

function getDecryptUrl(_0x1a1eaf) {
    _0x1a1eaf = _0x1a1eaf + _0x4f02('0xac', '%1@K') + Date[_0x4f02('0x2f', 'l49Q')]();
    stk = getUrlQueryParams(_0x1a1eaf, _0x4f02('0x178', 'fbD2'));
    if (stk) {
        const _0x660091 = format(_0x4f02('0x123', '$wUv'), Date[_0x4f02('0x1a3', 'dzX2')]());
        const _0x3b96a4 = $[_0x4f02('0x170', 'gdIQ')]($[_0x4f02('0x51', '!^Fw')], $['fp'][_0x4f02('0x27', '^jVw')](), _0x660091[_0x4f02('0x48', '*1RL')](), $[_0x4f02('0x133', 'Z60j')][_0x4f02('0x3c', 'Y#f)')](), $[_0x4f02('0x53', 'l49Q')])[_0x4f02('0xdd', 'r1T%')]($[_0x4f02('0x16a', 'NY7O')][_0x4f02('0x79', 'dzX2')]['Hex']);
        let _0x2a3caf = '';
        stk[_0x4f02('0xc5', 'dnnS')](',')[_0x4f02('0xc2', 'NY7O')]((_0x1b945f, _0x23c740) => {
            _0x2a3caf += _0x1b945f + ':' + getUrlQueryParams(_0x1a1eaf, _0x1b945f) + (_0x23c740 === stk[_0x4f02('0x1bc', 'hIL(')](',')[_0x4f02('0xe', '$uwW')] - 0x1 ? '' : '&');
        });
        const _0x35ce14 = $[_0x4f02('0x76', 'ROTe')][_0x4f02('0xb9', 'ZKy*')](_0x2a3caf, _0x3b96a4[_0x4f02('0x26', 'v1Rp')]())[_0x4f02('0x17b', 'KKmU')]($[_0x4f02('0x13b', 'hIL(')][_0x4f02('0x1a7', 'ZKy*')][_0x4f02('0x1af', '*1RL')]);
        return _0x1a1eaf + '&h5st=' + encodeURIComponent(['' [_0x4f02('0x130', 'e93*')](_0x660091[_0x4f02('0x9c', '*Jnq')]()), '' ['concat']($['fp'][_0x4f02('0xd', 'OFtx')]()), '' [_0x4f02('0x164', 'qL])')]($[_0x4f02('0x23', 'NY7O')]['toString']()), '' [_0x4f02('0x78', 'lLb0')]($[_0x4f02('0x8', 'frUB')]), '' ['concat'](_0x35ce14), _0x4f02('0x10c', '^LhX')[_0x4f02('0x18b', '!^Fw')](_0x660091)][_0x4f02('0x190', '*Jnq')](';')) + _0x4f02('0x111', 'lLb0') + Date['now']();
    }
}
async function requestAlgo() {
    $[_0x4f02('0x7a', 'v&#Q')] = _0x4f02('0x29', 'TUdS');
    $['fp'] = (getRandomIDPro({
        'size': 0xd
    }) + Date[_0x4f02('0x120', '^jVw')]())[_0x4f02('0x15', 'BJZ3')](0x0, 0x10);
    const _0xf4cd6b = {
        'url': _0x4f02('0x1c0', 'v1Rp'),
        'headers': {
            'Authority': _0x4f02('0x52', 'Y#f)'),
            'Pragma': _0x4f02('0xb7', '$#b@'),
            'Cache-Control': _0x4f02('0x1c6', 'TUdS'),
            'Accept': _0x4f02('0x54', '*1RL'),
            'Content-Type': _0x4f02('0x75', 'OFtx'),
            'Origin': _0x4f02('0x10d', 'Y#f)'),
            'Sec-Fetch-Site': _0x4f02('0xa3', '^jVw'),
            'User-Agent': $[_0x4f02('0xf8', '$uwW')],
            'Sec-Fetch-Mode': _0x4f02('0x46', 'VDpf'),
            'Sec-Fetch-Dest': 'empty',
            'Referer': _0x4f02('0xfd', '0*nj'),
            'Accept-Language': 'zh-CN,zh;q=0.9,zh-TW;q=0.8,en;q=0.7'
        },
        'body': JSON['stringify']({
            'version': _0x4f02('0x150', 'i*IW'),
            'fp': $['fp'],
            'appId': $[_0x4f02('0x161', 'dnnS')],
            'timestamp': Date[_0x4f02('0x0', 'ROTe')](),
            'platform': _0x4f02('0xbd', 'TUdS'),
            'expandParams': ''
        })
    };
    return new Promise(async _0x5058db => {
        $[_0x4f02('0x109', 'OFtx')](_0xf4cd6b, (_0xe25c05, _0x6e76dd, _0xf224a0) => {
            try {
                const {
                    ret, msg, data: {
                        result
                    } = {}
                } = JSON[_0x4f02('0x1b6', 'NY7O')](_0xf224a0);
                $['token'] = result['tk'];
                $[_0x4f02('0xec', 'RmO]')] = new Function(_0x4f02('0x1c3', '!^Fw') + result['algo'])();
            } catch (_0x38239b) {
                $['logErr'](_0x38239b, _0x6e76dd);
            } finally {
                _0x5058db();
            }
        });
    });
}

function getRandomIDPro() {
    var _0x158423, _0x35153a, _0x281d64 = void 0x0 === (_0x55a423 = (_0x35153a = 0x0 < arguments[_0x4f02('0x147', 'frUB')] && void 0x0 !== arguments[0x0] ? arguments[0x0] : {})[_0x4f02('0x4c', 'OFtx')]) ? 0xa : _0x55a423,
        _0x55a423 = void 0x0 === (_0x55a423 = _0x35153a['dictType']) ? 'number' : _0x55a423,
        _0x2bdbe7 = '';
    if ((_0x35153a = _0x35153a[_0x4f02('0x6d', 'r1T%')]) && _0x4f02('0x5a', 'fbD2') == typeof _0x35153a) _0x158423 = _0x35153a;
    else switch (_0x55a423) {
        case _0x4f02('0x197', 'ZKy*'):
            _0x158423 = _0x4f02('0x159', 'pyqW');
            break;
        case _0x4f02('0x38', '*1RL'):
            _0x158423 = _0x4f02('0x89', 'i*IW');
            break;
        case _0x4f02('0x13e', 'r#Cf'):
        default:
            _0x158423 = _0x4f02('0xe1', '^jVw');
    }
    for (; _0x281d64--;) _0x2bdbe7 += _0x158423[Math[_0x4f02('0x99', 'Z60j')]() * _0x158423['length'] | 0x0];
    return _0x2bdbe7;
}

function format(_0x4e2821, _0x1830a2) {
    if (!_0x4e2821) _0x4e2821 = _0x4f02('0x14a', '0*nj');
    var _0x1660d1;
    if (!_0x1830a2) {
        _0x1660d1 = Date[_0x4f02('0xc8', 'ZKy*')]();
    } else {
        _0x1660d1 = new Date(_0x1830a2);
    }
    var _0x5b159f, _0x2c72b9 = new Date(_0x1660d1),
        _0x557d25 = _0x4e2821,
        _0x22de01 = {
            'M+': _0x2c72b9[_0x4f02('0x3b', 'ZDsd')]() + 0x1,
            'd+': _0x2c72b9['getDate'](),
            'D+': _0x2c72b9[_0x4f02('0x14e', 'r1T%')](),
            'h+': _0x2c72b9[_0x4f02('0x144', 'BJZ3')](),
            'H+': _0x2c72b9[_0x4f02('0xb5', 'frUB')](),
            'm+': _0x2c72b9['getMinutes'](),
            's+': _0x2c72b9[_0x4f02('0x15b', 'i*IW')](),
            'w+': _0x2c72b9[_0x4f02('0x172', 'gdIQ')](),
            'q+': Math[_0x4f02('0x11b', 'TUdS')]((_0x2c72b9[_0x4f02('0x3b', 'ZDsd')]() + 0x3) / 0x3),
            'S+': _0x2c72b9[_0x4f02('0x11d', 'Z9)i')]()
        };
    /(y+)/i [_0x4f02('0x141', '&]*^')](_0x557d25) && (_0x557d25 = _0x557d25[_0x4f02('0x18a', 'gwdb')](RegExp['$1'], '' [_0x4f02('0x1a5', 'VDpf')](_0x2c72b9[_0x4f02('0x160', '^LhX')]())[_0x4f02('0x129', '^LhX')](0x4 - RegExp['$1'][_0x4f02('0x14b', '$wUv')])));
    Object[_0x4f02('0x156', 'TUdS')](_0x22de01)[_0x4f02('0x90', 'KB8L')](_0x3a57f9 => {
        if (new RegExp('(' [_0x4f02('0x1a5', 'VDpf')](_0x3a57f9, ')'))['test'](_0x557d25)) {
            var _0x4c2a9e, _0xc9b7ea = 'S+' === _0x3a57f9 ? _0x4f02('0x187', 'RmO]') : '00';
            _0x557d25 = _0x557d25[_0x4f02('0x1ae', '$#b@')](RegExp['$1'], 0x1 == RegExp['$1'][_0x4f02('0x21', 's4Uo')] ? _0x22de01[_0x3a57f9] : '' [_0x4f02('0x78', 'lLb0')](_0xc9b7ea)[_0x4f02('0x153', 'mu9&')](_0x22de01[_0x3a57f9])[_0x4f02('0x65', '!^Fw')]('' [_0x4f02('0x14c', 's4Uo')](_0x22de01[_0x3a57f9])[_0x4f02('0xe8', '!^Fw')]));
        }
    });
    return _0x557d25;
}
function Env(t, e) {
    "undefined" != typeof process && JSON.stringify(process.env).indexOf("GITHUB") > -1 && process.exit(0);
    class s {
        constructor(t) {
            this.env = t
        }
        send(t, e = "GET") {
            t = "string" == typeof t ? {
                url: t
            } : t;
            let s = this.get;
            return "POST" === e && (s = this.post), new Promise((e, i) => {
                s.call(this, t, (t, s, r) => {
                    t ? i(t) : e(s)
                })
            })
        }
        get(t) {
            return this.send.call(this.env, t)
        }
        post(t) {
            return this.send.call(this.env, t, "POST")
        }
    }
    return new class {
        constructor(t, e) {
            this.name = t, this.http = new s(this), this.data = null, this.dataFile = "box.dat", this.logs = [], this.isMute = !1, this.isNeedRewrite = !1, this.logSeparator = "\n", this.startTime = (new Date).getTime(), Object.assign(this, e), this.log("", `🔔${this.name}, 开始!`)
        }
        isNode() {
            return "undefined" != typeof module && !!module.exports
        }
        isQuanX() {
            return "undefined" != typeof $task
        }
        isSurge() {
            return "undefined" != typeof $httpClient && "undefined" == typeof $loon
        }
        isLoon() {
            return "undefined" != typeof $loon
        }
        toObj(t, e = null) {
            try {
                return JSON.parse(t)
            } catch {
                return e
            }
        }
        toStr(t, e = null) {
            try {
                return JSON.stringify(t)
            } catch {
                return e
            }
        }
        getjson(t, e) {
            let s = e;
            const i = this.getdata(t);
            if (i) try {
                s = JSON.parse(this.getdata(t))
            } catch {}
            return s
        }
        setjson(t, e) {
            try {
                return this.setdata(JSON.stringify(t), e)
            } catch {
                return !1
            }
        }
        getScript(t) {
            return new Promise(e => {
                this.get({
                    url: t
                }, (t, s, i) => e(i))
            })
        }
        runScript(t, e) {
            return new Promise(s => {
                let i = this.getdata("@chavy_boxjs_userCfgs.httpapi");
                i = i ? i.replace(/\n/g, "").trim() : i;
                let r = this.getdata("@chavy_boxjs_userCfgs.httpapi_timeout");
                r = r ? 1 * r : 20, r = e && e.timeout ? e.timeout : r;
                const [o, h] = i.split("@"), n = {
                    url: `http://${h}/v1/scripting/evaluate`,
                    body: {
                        script_text: t,
                        mock_type: "cron",
                        timeout: r
                    },
                    headers: {
                        "X-Key": o,
                        Accept: "*/*"
                    }
                };
                this.post(n, (t, e, i) => s(i))
            }).catch(t => this.logErr(t))
        }
        loaddata() {
            if (!this.isNode()) return {}; {
                this.fs = this.fs ? this.fs : require("fs"), this.path = this.path ? this.path : require("path");
                const t = this.path.resolve(this.dataFile),
                    e = this.path.resolve(process.cwd(), this.dataFile),
                    s = this.fs.existsSync(t),
                    i = !s && this.fs.existsSync(e);
                if (!s && !i) return {}; {
                    const i = s ? t : e;
                    try {
                        return JSON.parse(this.fs.readFileSync(i))
                    } catch (t) {
                        return {}
                    }
                }
            }
        }
        writedata() {
            if (this.isNode()) {
                this.fs = this.fs ? this.fs : require("fs"), this.path = this.path ? this.path : require("path");
                const t = this.path.resolve(this.dataFile),
                    e = this.path.resolve(process.cwd(), this.dataFile),
                    s = this.fs.existsSync(t),
                    i = !s && this.fs.existsSync(e),
                    r = JSON.stringify(this.data);
                s ? this.fs.writeFileSync(t, r) : i ? this.fs.writeFileSync(e, r) : this.fs.writeFileSync(t, r)
            }
        }
        lodash_get(t, e, s) {
            const i = e.replace(/\[(\d+)\]/g, ".$1").split(".");
            let r = t;
            for (const t of i)
                if (r = Object(r)[t], void 0 === r) return s;
            return r
        }
        lodash_set(t, e, s) {
            return Object(t) !== t ? t : (Array.isArray(e) || (e = e.toString().match(/[^.[\]]+/g) || []), e.slice(0, -1).reduce((t, s, i) => Object(t[s]) === t[s] ? t[s] : t[s] = Math.abs(e[i + 1]) >> 0 == +e[i + 1] ? [] : {}, t)[e[e.length - 1]] = s, t)
        }
        getdata(t) {
            let e = this.getval(t);
            if (/^@/.test(t)) {
                const [, s, i] = /^@(.*?)\.(.*?)$/.exec(t), r = s ? this.getval(s) : "";
                if (r) try {
                    const t = JSON.parse(r);
                    e = t ? this.lodash_get(t, i, "") : e
                } catch (t) {
                    e = ""
                }
            }
            return e
        }
        setdata(t, e) {
            let s = !1;
            if (/^@/.test(e)) {
                const [, i, r] = /^@(.*?)\.(.*?)$/.exec(e), o = this.getval(i), h = i ? "null" === o ? null : o || "{}" : "{}";
                try {
                    const e = JSON.parse(h);
                    this.lodash_set(e, r, t), s = this.setval(JSON.stringify(e), i)
                } catch (e) {
                    const o = {};
                    this.lodash_set(o, r, t), s = this.setval(JSON.stringify(o), i)
                }
            } else s = this.setval(t, e);
            return s
        }
        getval(t) {
            return this.isSurge() || this.isLoon() ? $persistentStore.read(t) : this.isQuanX() ? $prefs.valueForKey(t) : this.isNode() ? (this.data = this.loaddata(), this.data[t]) : this.data && this.data[t] || null
        }
        setval(t, e) {
            return this.isSurge() || this.isLoon() ? $persistentStore.write(t, e) : this.isQuanX() ? $prefs.setValueForKey(t, e) : this.isNode() ? (this.data = this.loaddata(), this.data[e] = t, this.writedata(), !0) : this.data && this.data[e] || null
        }
        initGotEnv(t) {
            this.got = this.got ? this.got : require("got"), this.cktough = this.cktough ? this.cktough : require("tough-cookie"), this.ckjar = this.ckjar ? this.ckjar : new this.cktough.CookieJar, t && (t.headers = t.headers ? t.headers : {}, void 0 === t.headers.Cookie && void 0 === t.cookieJar && (t.cookieJar = this.ckjar))
        }
        get(t, e = (() => {})) {
            t.headers && (delete t.headers["Content-Type"], delete t.headers["Content-Length"]), this.isSurge() || this.isLoon() ? (this.isSurge() && this.isNeedRewrite && (t.headers = t.headers || {}, Object.assign(t.headers, {
                "X-Surge-Skip-Scripting": !1
            })), $httpClient.get(t, (t, s, i) => {
                !t && s && (s.body = i, s.statusCode = s.status), e(t, s, i)
            })) : this.isQuanX() ? (this.isNeedRewrite && (t.opts = t.opts || {}, Object.assign(t.opts, {
                hints: !1
            })), $task.fetch(t).then(t => {
                const {
                    statusCode: s,
                    statusCode: i,
                    headers: r,
                    body: o
                } = t;
                e(null, {
                    status: s,
                    statusCode: i,
                    headers: r,
                    body: o
                }, o)
            }, t => e(t))) : this.isNode() && (this.initGotEnv(t), this.got(t).on("redirect", (t, e) => {
                try {
                    if (t.headers["set-cookie"]) {
                        const s = t.headers["set-cookie"].map(this.cktough.Cookie.parse).toString();
                        s && this.ckjar.setCookieSync(s, null), e.cookieJar = this.ckjar
                    }
                } catch (t) {
                    this.logErr(t)
                }
            }).then(t => {
                const {
                    statusCode: s,
                    statusCode: i,
                    headers: r,
                    body: o
                } = t;
                e(null, {
                    status: s,
                    statusCode: i,
                    headers: r,
                    body: o
                }, o)
            }, t => {
                const {
                    message: s,
                    response: i
                } = t;
                e(s, i, i && i.body)
            }))
        }
        post(t, e = (() => {})) {
            if (t.body && t.headers && !t.headers["Content-Type"] && (t.headers["Content-Type"] = "application/x-www-form-urlencoded"), t.headers && delete t.headers["Content-Length"], this.isSurge() || this.isLoon()) this.isSurge() && this.isNeedRewrite && (t.headers = t.headers || {}, Object.assign(t.headers, {
                "X-Surge-Skip-Scripting": !1
            })), $httpClient.post(t, (t, s, i) => {
                !t && s && (s.body = i, s.statusCode = s.status), e(t, s, i)
            });
            else if (this.isQuanX()) t.method = "POST", this.isNeedRewrite && (t.opts = t.opts || {}, Object.assign(t.opts, {
                hints: !1
            })), $task.fetch(t).then(t => {
                const {
                    statusCode: s,
                    statusCode: i,
                    headers: r,
                    body: o
                } = t;
                e(null, {
                    status: s,
                    statusCode: i,
                    headers: r,
                    body: o
                }, o)
            }, t => e(t));
            else if (this.isNode()) {
                this.initGotEnv(t);
                const {
                    url: s,
                    ...i
                } = t;
                this.got.post(s, i).then(t => {
                    const {
                        statusCode: s,
                        statusCode: i,
                        headers: r,
                        body: o
                    } = t;
                    e(null, {
                        status: s,
                        statusCode: i,
                        headers: r,
                        body: o
                    }, o)
                }, t => {
                    const {
                        message: s,
                        response: i
                    } = t;
                    e(s, i, i && i.body)
                })
            }
        }
        time(t, e = null) {
            const s = e ? new Date(e) : new Date;
            let i = {
                "M+": s.getMonth() + 1,
                "d+": s.getDate(),
                "H+": s.getHours(),
                "m+": s.getMinutes(),
                "s+": s.getSeconds(),
                "q+": Math.floor((s.getMonth() + 3) / 3),
                S: s.getMilliseconds()
            };
            /(y+)/.test(t) && (t = t.replace(RegExp.$1, (s.getFullYear() + "").substr(4 - RegExp.$1.length)));
            for (let e in i) new RegExp("(" + e + ")").test(t) && (t = t.replace(RegExp.$1, 1 == RegExp.$1.length ? i[e] : ("00" + i[e]).substr(("" + i[e]).length)));
            return t
        }
        msg(e = t, s = "", i = "", r) {
            const o = t => {
                if (!t) return t;
                if ("string" == typeof t) return this.isLoon() ? t : this.isQuanX() ? {
                    "open-url": t
                } : this.isSurge() ? {
                    url: t
                } : void 0;
                if ("object" == typeof t) {
                    if (this.isLoon()) {
                        let e = t.openUrl || t.url || t["open-url"],
                            s = t.mediaUrl || t["media-url"];
                        return {
                            openUrl: e,
                            mediaUrl: s
                        }
                    }
                    if (this.isQuanX()) {
                        let e = t["open-url"] || t.url || t.openUrl,
                            s = t["media-url"] || t.mediaUrl;
                        return {
                            "open-url": e,
                            "media-url": s
                        }
                    }
                    if (this.isSurge()) {
                        let e = t.url || t.openUrl || t["open-url"];
                        return {
                            url: e
                        }
                    }
                }
            };
            if (this.isMute || (this.isSurge() || this.isLoon() ? $notification.post(e, s, i, o(r)) : this.isQuanX() && $notify(e, s, i, o(r))), !this.isMuteLog) {
                let t = ["", "==============📣系统通知📣=============="];
                t.push(e), s && t.push(s), i && t.push(i), console.log(t.join("\n")), this.logs = this.logs.concat(t)
            }
        }
        log(...t) {
            t.length > 0 && (this.logs = [...this.logs, ...t]), console.log(t.join(this.logSeparator))
        }
        logErr(t, e) {
            const s = !this.isSurge() && !this.isQuanX() && !this.isLoon();
            s ? this.log("", `❗️${this.name}, 错误!`, t.stack) : this.log("", `❗️${this.name}, 错误!`, t)
        }
        wait(t) {
            return new Promise(e => setTimeout(e, t))
        }
        done(t = {}) {
            const e = (new Date).getTime(),
                s = (e - this.startTime) / 1e3;
            this.log("", `🔔${this.name}, 结束! 🕛 ${s} 秒`), this.log(), (this.isSurge() || this.isQuanX() || this.isLoon()) && $done(t)
        }
    }(t, e)
}