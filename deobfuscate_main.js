// 自动解码 main.min.js 中的所有混淆调用
const fs = require('fs');

console.log('🚀 开始解码 main.min.js...');

// 读取原文件
const content = fs.readFileSync('main.min.js', 'utf8');

// 从 main.min.js 中提取字符串数组
const arrayMatch = content.match(/const\s+_0x2dc1\s*=\s*\[([\s\S]*?)\];/);

if (!arrayMatch) {
    console.log('❌ 无法找到字符串数组');
    process.exit(1);
}

// 解析字符串数组
const arrayContent = arrayMatch[1];
const strings = [];

// 提取字符串
let current = '';
let inString = false;
let escapeNext = false;

for (let i = 0; i < arrayContent.length; i++) {
    const char = arrayContent[i];
    
    if (escapeNext) {
        current += char;
        escapeNext = false;
        continue;
    }
    
    if (char === '\\') {
        current += char;
        escapeNext = true;
        continue;
    }
    
    if (char === "'" && !inString) {
        inString = true;
        continue;
    }
    
    if (char === "'" && inString) {
        strings.push(current);
        current = '';
        inString = false;
        continue;
    }
    
    if (inString) {
        current += char;
    }
}

console.log(`📦 提取到 ${strings.length} 个字符串`);

// 模拟数组重排
function shuffleArray(arr, times) {
    const result = [...arr];
    for (let i = 0; i < times; i++) {
        result.push(result.shift());
    }
    return result;
}

const shuffledStrings = shuffleArray(strings, 0x11b);

// 解码函数
function atobPolyfill(str) {
    return Buffer.from(str, 'base64').toString('binary');
}

function rc4Decode(data, key) {
    let s = [];
    let j = 0;
    let result = '';
    
    for (let i = 0; i < 256; i++) {
        s[i] = i;
    }
    
    for (let i = 0; i < 256; i++) {
        j = (j + s[i] + key.charCodeAt(i % key.length)) % 256;
        [s[i], s[j]] = [s[j], s[i]];
    }
    
    let i = 0;
    j = 0;
    for (let k = 0; k < data.length; k++) {
        i = (i + 1) % 256;
        j = (j + s[i]) % 256;
        [s[i], s[j]] = [s[j], s[i]];
        result += String.fromCharCode(data.charCodeAt(k) ^ s[(s[i] + s[j]) % 256]);
    }
    
    return result;
}

function decodeString(index, key) {
    const numIndex = parseInt(index, 16);
    
    if (numIndex >= shuffledStrings.length) {
        return null;
    }
    
    const base64String = shuffledStrings[numIndex];
    
    try {
        const decoded = atobPolyfill(base64String);
        let urlDecoded = '';
        for (let i = 0; i < decoded.length; i++) {
            urlDecoded += '%' + ('00' + decoded.charCodeAt(i).toString(16)).slice(-2);
        }
        const finalDecoded = decodeURIComponent(urlDecoded);
        const result = rc4Decode(finalDecoded, key);
        return result;
    } catch (e) {
        return null;
    }
}

// 查找所有的 _0x4f02 调用
const obfuscatedCalls = content.match(/_0x4f02\('([^']+)',\s*'([^']+)'\)/g);

if (!obfuscatedCalls) {
    console.log('❌ 没有找到混淆调用');
    process.exit(1);
}

console.log(`🔍 找到 ${obfuscatedCalls.length} 个混淆调用`);

// 创建解码映射
const decodedMap = new Map();
let successCount = 0;
let failCount = 0;

console.log('🔓 开始解码...');

for (const call of obfuscatedCalls) {
    const match = call.match(/_0x4f02\('([^']+)',\s*'([^']+)'\)/);
    if (match) {
        const [fullCall, index, key] = match;
        const decoded = decodeString(index, key);
        
        if (decoded !== null) {
            decodedMap.set(fullCall, decoded);
            successCount++;
            console.log(`✅ ${fullCall} → "${decoded}"`);
        } else {
            failCount++;
            console.log(`❌ ${fullCall} → 解码失败`);
        }
    }
}

console.log(`\n📊 解码统计: 成功 ${successCount}, 失败 ${failCount}`);

// 替换混淆调用
let deobfuscatedContent = content;

// 首先移除混淆相关的代码（前89行）
const lines = content.split('\n');
const codeStartIndex = 89; // 从第90行开始是实际的业务代码

// 保留必要的导入和初始化
const header = `// 解码后的 main.min.js
_0xod2 = 'jsjiami.com.v6';
const $ = new Env('领取优惠券');
const nowVersion = "20250526";
console.log("当前版本" + nowVersion);

`;

// 获取业务代码部分
const businessCode = lines.slice(codeStartIndex).join('\n');

// 替换所有混淆调用
let finalCode = header + businessCode;

for (const [obfuscatedCall, decodedValue] of decodedMap) {
    // 对特殊字符进行转义
    const escapedCall = obfuscatedCall.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    const regex = new RegExp(escapedCall, 'g');
    
    // 根据解码结果的类型决定如何替换
    let replacement;
    if (decodedValue.includes(' ') || decodedValue.includes('/') || decodedValue.includes('.') || decodedValue.includes('=')) {
        // 包含空格、路径、URL等的字符串用引号包围
        replacement = `'${decodedValue.replace(/'/g, "\\'")}'`;
    } else {
        // 简单的标识符或属性名用引号包围
        replacement = `'${decodedValue}'`;
    }
    
    finalCode = finalCode.replace(regex, replacement);
}

// 保存解码后的文件
fs.writeFileSync('main_deobfuscated.js', finalCode);

console.log('\n🎉 解码完成！');
console.log('📁 解码后的文件已保存为: main_deobfuscated.js');
console.log(`📏 原文件大小: ${content.length} 字符`);
console.log(`📏 解码后大小: ${finalCode.length} 字符`);

// 显示一些解码示例
console.log('\n📋 解码示例:');
const examples = Array.from(decodedMap.entries()).slice(0, 10);
for (const [call, decoded] of examples) {
    console.log(`   ${call} → "${decoded}"`);
}

if (decodedMap.size > 10) {
    console.log(`   ... 还有 ${decodedMap.size - 10} 个解码结果`);
}
