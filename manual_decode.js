// 手动解码 _0x4f02('0x1b0', 'NY7O')
// 基于对混淆代码的分析

const fs = require('fs');

// 从 main.min.js 中提取字符串数组
const content = fs.readFileSync('main.min.js', 'utf8');
const arrayMatch = content.match(/const\s+_0x2dc1\s*=\s*\[([\s\S]*?)\];/);

if (!arrayMatch) {
    console.log('无法找到字符串数组');
    process.exit(1);
}

// 解析字符串数组
const arrayContent = arrayMatch[1];
const strings = [];

// 简单的字符串提取（处理引号包围的字符串）
let current = '';
let inString = false;
let escapeNext = false;

for (let i = 0; i < arrayContent.length; i++) {
    const char = arrayContent[i];
    
    if (escapeNext) {
        current += char;
        escapeNext = false;
        continue;
    }
    
    if (char === '\\') {
        current += char;
        escapeNext = true;
        continue;
    }
    
    if (char === "'" && !inString) {
        inString = true;
        continue;
    }
    
    if (char === "'" && inString) {
        strings.push(current);
        current = '';
        inString = false;
        continue;
    }
    
    if (inString) {
        current += char;
    }
}

console.log(`提取到 ${strings.length} 个字符串`);

// 模拟数组重排
// 从代码中可以看到：(function(_0x2b9ec8, _0x2dc11e) { ... }(_0x2dc1, 0x11b));
// 这意味着数组会被重排 0x11b (283) 次

function shuffleArray(arr, times) {
    const result = [...arr];
    for (let i = 0; i < times; i++) {
        result.push(result.shift());
    }
    return result;
}

const shuffledStrings = shuffleArray(strings, 0x11b);

console.log('数组重排完成');

// 模拟解码函数的核心逻辑
function atobPolyfill(str) {
    return Buffer.from(str, 'base64').toString('binary');
}

function rc4Decode(data, key) {
    let s = [];
    let j = 0;
    let result = '';
    
    // 初始化 S 盒
    for (let i = 0; i < 256; i++) {
        s[i] = i;
    }
    
    // 密钥调度
    for (let i = 0; i < 256; i++) {
        j = (j + s[i] + key.charCodeAt(i % key.length)) % 256;
        [s[i], s[j]] = [s[j], s[i]];
    }
    
    // 生成密钥流并解密
    let i = 0;
    j = 0;
    for (let k = 0; k < data.length; k++) {
        i = (i + 1) % 256;
        j = (j + s[i]) % 256;
        [s[i], s[j]] = [s[j], s[i]];
        result += String.fromCharCode(data.charCodeAt(k) ^ s[(s[i] + s[j]) % 256]);
    }
    
    return result;
}

function decodeString(index, key) {
    // 将十六进制索引转换为数字
    const numIndex = parseInt(index, 16);
    
    if (numIndex >= shuffledStrings.length) {
        return `索引超出范围: ${numIndex} >= ${shuffledStrings.length}`;
    }
    
    // 获取 base64 编码的字符串
    const base64String = shuffledStrings[numIndex];
    
    try {
        // Base64 解码
        const decoded = atobPolyfill(base64String);
        
        // URL 解码
        let urlDecoded = '';
        for (let i = 0; i < decoded.length; i++) {
            urlDecoded += '%' + ('00' + decoded.charCodeAt(i).toString(16)).slice(-2);
        }
        const finalDecoded = decodeURIComponent(urlDecoded);
        
        // RC4 解密
        const result = rc4Decode(finalDecoded, key);
        
        return result;
    } catch (e) {
        return `解码失败: ${e.message}`;
    }
}

// 测试目标调用
console.log('\n=== 解码测试 ===');

const targetIndex = '0x1b0';
const targetKey = 'NY7O';

console.log(`正在解码: _0x4f02('${targetIndex}', '${targetKey}')`);

const result = decodeString(targetIndex, targetKey);
console.log(`解码结果: "${result}"`);

// 测试一些其他的调用来验证我们的解码逻辑
const testCases = [
    ['0x49', 'RmO]'],   // 应该是 'cron'
    ['0x14', 'v1Rp'],   // 应该是 'path'
    ['0x87', '^jVw'],   // 应该是 'util'
    ['0x137', 'r1T%'],  // 可能是 'log'
    ['0x146', 'ZKy*'],  // 可能是 'log'
];

console.log('\n=== 其他测试用例 ===');
for (const [index, key] of testCases) {
    const testResult = decodeString(index, key);
    console.log(`_0x4f02('${index}', '${key}') = "${testResult}"`);
}

// 如果目标结果是 'log'，我们就成功了
if (result === 'log') {
    console.log('\n✅ 成功！_0x4f02(\'0x1b0\', \'NY7O\') 确实返回 "log"');
} else {
    console.log(`\n❓ 结果: "${result}" (期望: "log")`);
}
