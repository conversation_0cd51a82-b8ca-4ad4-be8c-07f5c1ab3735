const fs = require('fs');

// 读取混淆文件
const content = fs.readFileSync('main.min.js', 'utf8');

// 直接从文件中提取并执行解码逻辑
try {
    // 提取字符串数组定义（第7行）
    const arrayLine = content.split('\n')[6]; // 第7行，索引为6
    console.log('字符串数组定义:', arrayLine.substring(0, 100) + '...');
    
    // 提取数组重排函数（第8-15行）
    const shuffleLines = content.split('\n').slice(7, 15).join('\n');
    console.log('数组重排函数:', shuffleLines.substring(0, 100) + '...');
    
    // 提取解码函数（第16-88行）
    const decodeFuncLines = content.split('\n').slice(15, 88).join('\n');
    console.log('解码函数:', decodeFuncLines.substring(0, 100) + '...');
    
    // 创建一个安全的执行环境
    const vm = require('vm');
    const sandbox = {
        console: { log: () => {} },
        Function: Function,
        String: String,
        Math: Math,
        parseInt: parseInt,
        decodeURIComponent: decodeURIComponent,
        Buffer: Buffer,
        window: {},
        atob: (str) => Buffer.from(str, 'base64').toString('binary')
    };
    
    // 执行字符串数组定义
    vm.runInNewContext(arrayLine, sandbox);
    console.log('字符串数组执行成功');
    
    // 执行数组重排函数
    vm.runInNewContext(shuffleLines, sandbox);
    console.log('数组重排执行成功');
    
    // 执行解码函数定义
    vm.runInNewContext(decodeFuncLines, sandbox);
    console.log('解码函数执行成功');
    
    // 获取解码函数
    const decodeFunc = sandbox._0x4f02;
    
    if (typeof decodeFunc !== 'function') {
        console.log('解码函数创建失败，类型:', typeof decodeFunc);
        process.exit(1);
    }
    
    console.log('解码函数创建成功！');
    
    // 测试解码特定的调用
    const testCalls = [
        ['0x1b0', 'NY7O'],  // 这是你想要的
        ['0x49', 'RmO]'],   // require
        ['0x14', 'v1Rp'],   // path
        ['0x87', '^jVw'],   // util
        ['0x10', '^jVw'],   // 可能是HTML字符串
        ['0x102', 'gdIQ'],  // URL
        ['0x2d', 'pyqW'],   // window
        ['0x15d', 'frUB'],  // document
        ['0x28', 'i*IW'],   // window
        ['0x33', '$uwW']    // document
    ];
    
    console.log('\n=== 解码结果 ===');
    for (const [index, key] of testCalls) {
        try {
            const result = decodeFunc(index, key);
            console.log(`_0x4f02('${index}', '${key}') = "${result}"`);
        } catch (e) {
            console.log(`_0x4f02('${index}', '${key}') = 解码失败: ${e.message}`);
        }
    }
    
    // 特别关注你要的那个
    console.log('\n=== 重点关注 ===');
    try {
        const targetResult = decodeFunc('0x1b0', 'NY7O');
        console.log(`_0x4f02('0x1b0', 'NY7O') = "${targetResult}"`);
        
        if (targetResult === 'log') {
            console.log('✅ 确认：_0x4f02(\'0x1b0\', \'NY7O\') 确实返回 "log"');
        } else {
            console.log(`❌ 意外结果：期望 "log"，实际得到 "${targetResult}"`);
        }
    } catch (e) {
        console.log('❌ 解码失败:', e.message);
    }
    
} catch (e) {
    console.log('执行失败:', e.message);
    console.log(e.stack);
}
