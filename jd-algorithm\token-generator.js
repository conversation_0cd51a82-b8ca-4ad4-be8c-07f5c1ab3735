/**
 * Token生成器
 */

const { CryptoJS, createCustomAlgorithm } = require('./crypto-utils');
const { getRandomIDPro, fromBase64, decodeBase64URL } = require('./utils');
const { LocalTokenVersion } = require('./config');

/**
 * 基础Token生成器
 */
class BaseLocalToken {
  constructor(algos, tokenConfig) {
    this.algos = algos;
    this.tokenConfig = tokenConfig;
  }

  /**
   * 生成本地Token
   */
  genLocalTK(fingerprint) {
    const tokenData = this.generateTokenData(fingerprint);
    return this.formatToken(tokenData);
  }

  /**
   * 生成Token数据
   */
  generateTokenData(fingerprint) {
    const baseInfo = this.tokenConfig.baseInfo;
    const now = Date.now();
    
    return {
      magic: baseInfo.magic,
      version: baseInfo.version,
      platform: baseInfo.platform,
      expires: now + baseInfo.expires,
      producer: baseInfo.producer,
      expr: this.generateTokenExpr(),
      cipher: this.generateCipher(fingerprint),
      ts: now,
      hdid: fingerprint,
      parentId: fingerprint
    };
  }

  /**
   * 生成Token表达式
   */
  generateTokenExpr() {
    const randomChars = () => getRandomIDPro({ size: 32, dictType: 'max' });
    const numbers = ['1', '2', '3'];
    const operators = ['+', 'x'];
    const length = 2 + Math.floor(4 * Math.random());
    let expression = '';

    for (let i = 0; i < length; i++) {
      expression += numbers[Math.floor(Math.random() * 3)];
      if (i < length - 1) {
        expression += operators[Math.floor(Math.random() * 2)];
      }
    }

    if (expression.length < 9) {
      expression += randomChars().slice(0, 9 - expression.length);
    }

    const utf8Encoded = this.algos.enc.Utf8.parse(expression);
    return fromBase64(this.algos.enc.Base64.stringify(utf8Encoded));
  }

  /**
   * 生成密码信息
   */
  generateCipher(fingerprint) {
    const baseInfo = this.tokenConfig.baseInfo;
    const cipher = { ...baseInfo.cipher };
    
    // 更新一些动态字段
    const uuid = getRandomIDPro({ size: 16, customDict: '0123456789abcdef' });
    cipher.uuid = Buffer.from(uuid).toString('base64');
    cipher.aid = cipher.uuid;
    cipher.openudid = cipher.uuid;
    
    return cipher;
  }

  /**
   * 格式化Token
   */
  formatToken(tokenData) {
    const parts = [
      tokenData.magic,
      tokenData.version,
      tokenData.platform,
      tokenData.expires,
      tokenData.producer,
      tokenData.expr,
      JSON.stringify(tokenData.cipher),
      tokenData.ts,
      tokenData.hdid,
      tokenData.parentId
    ];
    
    return parts.join(';');
  }

  /**
   * 解析Token
   */
  parseToken(token) {
    const parts = token.split(';');
    if (parts.length < 10) {
      throw new Error('Invalid token format');
    }
    
    return {
      magic: parts[0],
      version: parts[1],
      platform: parts[2],
      expires: parseInt(parts[3]),
      producer: parts[4],
      expr: parts[5],
      cipher: JSON.parse(parts[6]),
      ts: parseInt(parts[7]),
      hdid: parts[8],
      parentId: parts[9]
    };
  }
}

/**
 * Token V3生成器
 */
class LocalTokenV3 extends BaseLocalToken {
  constructor(algos, tokenConfig) {
    super(algos, tokenConfig);
  }

  genLocalTK(fingerprint) {
    const tokenData = this.generateTokenData(fingerprint);
    const formatted = this.formatToken(tokenData);
    
    // V3特有的加密逻辑
    const cipher = this.tokenConfig.cipher;
    if (cipher && cipher.secret2) {
      const encrypted = this.algos.AES.encrypt(formatted, cipher.secret2, {
        iv: this.algos.enc.Utf8.parse('0102030405060708'),
      });
      return cipher.prefix + encrypted.toString();
    }
    
    return formatted;
  }
}

/**
 * Token V4生成器
 */
class LocalTokenV4 extends BaseLocalToken {
  constructor(algos, tokenConfig) {
    super(algos, tokenConfig);
  }

  genLocalTK(fingerprint) {
    const tokenData = this.generateTokenData(fingerprint);
    const formatted = this.formatToken(tokenData);
    
    // V4特有的加密逻辑
    const cipher = this.tokenConfig.cipher;
    if (cipher && cipher.secret1) {
      const key = this.algos.enc.Utf8.parse(cipher.secret1);
      const encrypted = this.algos.AES.encrypt(formatted, key, {
        iv: this.algos.enc.Utf8.parse('0102030405060708'),
      });
      return cipher.prefix + encrypted.toString();
    }
    
    return formatted;
  }
}

/**
 * Token V5生成器
 */
class LocalTokenV5 extends BaseLocalToken {
  constructor(algos, tokenConfig) {
    super(algos, tokenConfig);
  }

  genLocalTK(fingerprint) {
    const tokenData = this.generateTokenData(fingerprint);
    const formatted = this.formatToken(tokenData);
    
    // V5特有的处理逻辑
    return formatted;
  }
}

/**
 * Token工厂
 */
class TokenFactory {
  constructor() {
    this.instances = new Map();
    this.algos = createCustomAlgorithm();
  }

  getInstance(version, tokenConfig) {
    const key = `${version}_${JSON.stringify(tokenConfig)}`;
    
    if (!this.instances.has(key)) {
      let instance;
      switch (version) {
        case LocalTokenVersion['03']:
          instance = new LocalTokenV3(this.algos, tokenConfig);
          break;
        case LocalTokenVersion['04']:
          instance = new LocalTokenV4(this.algos, tokenConfig);
          break;
        case LocalTokenVersion['05']:
          instance = new LocalTokenV5(this.algos, tokenConfig);
          break;
        default:
          instance = new BaseLocalToken(this.algos, tokenConfig);
      }
      this.instances.set(key, instance);
    }
    
    return this.instances.get(key);
  }
}

module.exports = {
  BaseLocalToken,
  LocalTokenV3,
  LocalTokenV4,
  LocalTokenV5,
  TokenFactory
};
