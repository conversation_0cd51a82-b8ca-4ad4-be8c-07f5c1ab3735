const fs = require('fs');
const path = require('path');
const vm = require('vm');

class JSDeobfuscator {
    constructor(filePath) {
        this.filePath = filePath;
        this.content = fs.readFileSync(filePath, 'utf8');
        this.stringArray = [];
        this.decodeFunctionName = '';
        this.decodedStrings = new Map();
        this.decodeFunction = null;
    }

    // 提取字符串数组
    extractStringArray() {
        // 匹配类似 const _0x2dc1 = ['string1', 'string2', ...] 的模式
        const arrayMatch = this.content.match(/const\s+(_0x[a-f0-9]+)\s*=\s*\[([\s\S]*?)\];/);
        if (arrayMatch) {
            const arrayName = arrayMatch[1];
            const arrayContent = arrayMatch[2];

            // 解析字符串数组
            const strings = [];
            const stringMatches = arrayContent.match(/'([^'\\]|\\.)*'/g);
            if (stringMatches) {
                for (const match of stringMatches) {
                    // 移除引号
                    const str = match.slice(1, -1);
                    strings.push(str);
                }
            }

            this.stringArray = strings;
            console.log(`提取到 ${strings.length} 个字符串`);
            return arrayName;
        }
        return null;
    }

    // 提取解码函数
    extractDecodeFunction() {
        // 匹配整个解码函数定义 - 寻找实际的解码函数名
        // 在代码中寻找类似 _0x4f02('0x49', 'RmO]') 的调用模式
        const callMatch = this.content.match(/(_0x[a-f0-9]+)\s*\(\s*'0x[a-f0-9]+'\s*,\s*'[^']*'\s*\)/);
        if (callMatch) {
            this.decodeFunctionName = callMatch[1];
            console.log(`找到解码函数: ${this.decodeFunctionName}`);
            return callMatch[1];
        }

        // 备用方法：匹配函数定义
        const funcMatch = this.content.match(/const\s+(_0x[a-f0-9]+)\s*=\s*function\s*\([^)]*\)\s*\{([\s\S]*?)\};/);
        if (funcMatch) {
            this.decodeFunctionName = funcMatch[1];
            console.log(`找到解码函数: ${this.decodeFunctionName}`);
            return funcMatch[1];
        }
        return null;
    }

    // 创建真实的解码函数
    createRealDecodeFunction() {
        try {
            // 提取完整的解码逻辑
            const arrayName = this.extractStringArray();
            if (!arrayName) return null;

            // 创建一个安全的执行环境
            const sandbox = {
                console: { log: () => {} }, // 禁用console输出
                atob: (str) => Buffer.from(str, 'base64').toString('binary'),
                String: String,
                Math: Math,
                parseInt: parseInt,
                decodeURIComponent: decodeURIComponent
            };

            // 添加字符串数组到沙箱
            sandbox[arrayName] = this.stringArray;

            // 提取数组重排代码
            const shuffleMatch = this.content.match(/\(function\((_0x[a-f0-9]+),\s*(_0x[a-f0-9]+)\)\s*\{[\s\S]*?\}\((_0x[a-f0-9]+),\s*(0x[a-f0-9]+)\)\);/);
            if (shuffleMatch) {
                const shuffleCode = shuffleMatch[0];
                vm.runInNewContext(shuffleCode, sandbox);
            }

            // 提取解码函数的完整代码
            const decodeFuncMatch = this.content.match(/const\s+(_0x[a-f0-9]+)\s*=\s*function\s*\([^)]*\)\s*\{[\s\S]*?\};/);
            if (decodeFuncMatch) {
                const decodeFuncCode = decodeFuncMatch[0];
                vm.runInNewContext(decodeFuncCode, sandbox);

                // 返回解码函数
                this.decodeFunction = sandbox[this.decodeFunctionName];
                console.log('成功创建解码函数');
                return this.decodeFunction;
            }
        } catch (e) {
            console.log('创建解码函数失败:', e.message);
        }
        return null;
    }

    // 替换混淆的函数调用
    replaceObfuscatedCalls() {
        const decodeFunc = this.createRealDecodeFunction();
        if (!decodeFunc) {
            console.log('无法创建解码函数');
            return this.content;
        }

        let deobfuscated = this.content;

        // 匹配所有的 _0x4f02('0x...', '...') 调用
        const callPattern = new RegExp(`${this.decodeFunctionName}\\s*\\(\\s*'(0x[a-f0-9]+)'\\s*,\\s*'([^']*)'\\s*\\)`, 'g');

        let match;
        let replacements = 0;
        const matches = [];

        // 收集所有匹配项
        while ((match = callPattern.exec(this.content)) !== null) {
            matches.push({
                fullMatch: match[0],
                index: match[1],
                key: match[2],
                position: match.index
            });
        }

        console.log(`找到 ${matches.length} 个混淆调用`);

        // 从后往前替换，避免位置偏移
        for (let i = matches.length - 1; i >= 0; i--) {
            const matchInfo = matches[i];

            try {
                // 获取解码后的字符串
                const decoded = decodeFunc(matchInfo.index, matchInfo.key);

                if (decoded && typeof decoded === 'string') {
                    // 创建安全的替换字符串
                    let replacement;
                    if (decoded.includes("'") && !decoded.includes('"')) {
                        replacement = '"' + decoded + '"';
                    } else if (decoded.includes('"') && !decoded.includes("'")) {
                        replacement = "'" + decoded + "'";
                    } else if (decoded.includes("'") || decoded.includes('"') || decoded.includes('\n')) {
                        // 使用模板字符串
                        replacement = '`' + decoded.replace(/`/g, '\\`').replace(/\$/g, '\\$') + '`';
                    } else {
                        replacement = "'" + decoded + "'";
                    }

                    // 执行替换
                    const before = deobfuscated.substring(0, matchInfo.position);
                    const after = deobfuscated.substring(matchInfo.position + matchInfo.fullMatch.length);
                    deobfuscated = before + replacement + after;

                    replacements++;

                    // 记录替换
                    this.decodedStrings.set(matchInfo.fullMatch, decoded);
                }
            } catch (e) {
                console.log(`解码失败 ${matchInfo.fullMatch}: ${e.message}`);
            }
        }

        console.log(`完成 ${replacements} 个字符串替换`);
        return deobfuscated;
    }

    // 清理混淆代码
    cleanupObfuscation() {
        let cleaned = this.content;

        // 移除字符串数组定义
        cleaned = cleaned.replace(/const\s+_0x[a-f0-9]+\s*=\s*\[[\s\S]*?\];/, '// 字符串数组已移除');

        // 移除数组重排函数
        cleaned = cleaned.replace(/\(function\(_0x[a-f0-9]+,\s*_0x[a-f0-9]+\)\s*\{[\s\S]*?\}\(_0x[a-f0-9]+,\s*0x[a-f0-9]+\)\);/, '// 数组重排函数已移除');

        // 移除解码函数定义
        cleaned = cleaned.replace(/const\s+_0x[a-f0-9]+\s*=\s*function\s*\([^)]*\)\s*\{[\s\S]*?\};/, '// 解码函数已移除');

        return cleaned;
    }

    // 执行完整的解混淆过程
    deobfuscate() {
        console.log('开始解混淆...');

        // 1. 提取字符串数组
        const arrayName = this.extractStringArray();
        if (!arrayName) {
            console.log('未找到字符串数组');
            return this.content;
        }

        // 2. 提取解码函数
        const funcName = this.extractDecodeFunction();
        if (!funcName) {
            console.log('未找到解码函数');
            return this.content;
        }

        // 3. 替换混淆调用
        let result = this.replaceObfuscatedCalls();

        // 4. 清理混淆代码
        result = this.cleanupObfuscation();

        console.log('解混淆完成');
        return result;
    }

    // 保存解混淆结果
    saveDeobfuscated(outputPath) {
        const deobfuscated = this.deobfuscate();
        fs.writeFileSync(outputPath, deobfuscated, 'utf8');
        console.log(`解混淆结果已保存到: ${outputPath}`);

        // 保存解码映射
        const mappingPath = outputPath.replace('.js', '_mapping.json');
        const mapping = Object.fromEntries(this.decodedStrings);
        fs.writeFileSync(mappingPath, JSON.stringify(mapping, null, 2), 'utf8');
        console.log(`解码映射已保存到: ${mappingPath}`);
    }
}

// 使用示例
if (require.main === module) {
    const inputFile = process.argv[2] || 'main.min.js';
    const outputFile = process.argv[3] || 'main.deobfuscated.js';

    console.log(`输入文件: ${inputFile}`);
    console.log(`输出文件: ${outputFile}`);

    try {
        const deobfuscator = new JSDeobfuscator(inputFile);
        deobfuscator.saveDeobfuscated(outputFile);
    } catch (error) {
        console.error('解混淆失败:', error.message);
    }
}

module.exports = JSDeobfuscator;
