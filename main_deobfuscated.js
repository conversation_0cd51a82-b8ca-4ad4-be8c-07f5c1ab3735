// 解码后的 main.min.js
_0xod2 = 'jsjiami.com.v6';
const $ = new Env('领取优惠券');
const nowVersion = "20250526";
console.log("当前版本" + nowVersion);

const cron = require('node-cron');
const fs = require('fs');
const path = require('path');
const util = require('util');
const {JSDOM} = require('jsdom');
const dom = new JSDOM('<!DOCTYPE html><html><body></body></html>', {
    'url': 'https://www.jd.com'
});
const window = dom['window'];
const document = window['document'];
global['window'] = window;
global['document'] = document;
global['Element'] = window['Element'];
const configPath = path['join'](__dirname, 'config.json');
// const smashUtils = require('./jdJm.js')['smashUtils'];
const { SmashUtils } = require('./SmashUtils.js');
let smashUtils = null;
async function initSmashUtils() {
    if (!smashUtils && cookiesArr && cookiesArr[0]) {
        const url = 'https://h5static.m.jd.com/mall/active/3aEzDU3fpqYYtnNTFPAkyY3tRY8Y/index.html';
        const cookie = cookiesArr[0];
        // const cookie = 'pt_key=AAJoLw9lADBxs4e1jDG4PoJ83ChDoVQCn7DgX-Q0uEnN4gMgcKBZS2L8kdBbcsrGqeuq0KH0xrM; pt_pin=jd_dlsyruocXPmz;';
        const ua = user_agent || "Mozilla/5.0 (iPhone; CPU iPhone OS 14_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.49(0x1800312d) NetType/4G Language/zh_CN";

        smashUtils = new SmashUtils(url, cookie, ua);
        await smashUtils.init({
            appid: "babel_zeEzM8yPWTLWju6YSj1aoNSvPfQ",
            sceneid: "babel_zeEzM8yPWTLWju6YSj1aoNSvPfQ",
            uid: "4f3a81b78bd21600d3a287edb48d425d"
        });
    }
}
const ParamsSignLite = require('./jdJm2.js');
$['CryptoJS'] = require('crypto-js');
let apiList = require('./jdYhqApiList.js')['apiList'];
const USER_AGENTS = ['jdapp;android;10.1.0;10;network/wifi;Mozilla/5.0 (Linux; Android 10; ONEPLUS A5010 Build/QKQ1.191014.012; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/77.0.3865.120 MQQBrowser/6.2 TBS/045230 Mobile Safari/537.36', 'jdapp;iPhone;10.1.0;14.3;network/4g;Mozilla/5.0 (iPhone; CPU iPhone OS 14_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1', 'jdapp;android;10.1.0;9;network/4g;Mozilla/5.0 (Linux; Android 9; Mi Note 3 Build/PKQ1.181007.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/66.0.3359.126 MQQBrowser/6.2 TBS/045131 Mobile Safari/537.36', 'jdapp;android;10.1.0;10;network/wifi;Mozilla/5.0 (Linux; Android 10; GM1910 Build/QKQ1.190716.003; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/77.0.3865.120 MQQBrowser/6.2 TBS/045230 Mobile Safari/537.36', 'jdapp;android;10.1.0;9;network/wifi;Mozilla/5.0 (Linux; Android 9; 16T Build/PKQ1.190616.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/66.0.3359.126 MQQBrowser/6.2 TBS/044942 Mobile Safari/537.36', 'jdapp;iPhone;10.1.0;13.6;network/wifi;Mozilla/5.0 (iPhone; CPU iPhone OS 13_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1', 'jdapp;iPhone;10.1.0;13.6;network/wifi;Mozilla/5.0 (iPhone; CPU iPhone OS 13_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1', 'jdapp;iPhone;10.1.0;13.5;network/wifi;Mozilla/5.0 (iPhone; CPU iPhone OS 13_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1', 'jdapp;iPhone;10.1.0;14.1;network/wifi;Mozilla/5.0 (iPhone; CPU iPhone OS 14_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1', 'jdapp;iPhone;10.1.0;13.3;network/wifi;Mozilla/5.0 (iPhone; CPU iPhone OS 13_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1', 'jdapp;iPhone;10.1.0;13.7;network/wifi;Mozilla/5.0 (iPhone; CPU iPhone OS 13_7 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1', 'jdapp;iPhone;10.1.0;14.1;network/wifi;Mozilla/5.0 (iPhone; CPU iPhone OS 14_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1', 'jdapp;iPhone;10.1.0;13.3;network/wifi;Mozilla/5.0 (iPhone; CPU iPhone OS 13_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1', 'jdapp;iPhone;10.1.0;13.4;network/wifi;Mozilla/5.0 (iPhone; CPU iPhone OS 13_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1', 'jdapp;iPhone;10.1.0;14.3;network/wifi;Mozilla/5.0 (iPhone; CPU iPhone OS 14_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1', 'jdapp;android;10.1.0;9;network/wifi;Mozilla/5.0 (Linux; Android 9; MI 6 Build/PKQ1.190118.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/66.0.3359.126 MQQBrowser/6.2 TBS/044942 Mobile Safari/537.36', 'jdapp;android;10.1.0;11;network/wifi;Mozilla/5.0 (Linux; Android 11; Redmi K30 5G Build/RKQ1.200826.002; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/77.0.3865.120 MQQBrowser/6.2 TBS/045511 Mobile Safari/537.36', 'jdapp;iPhone;10.1.0;11.4;network/wifi;Mozilla/5.0 (iPhone; CPU iPhone OS 11_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15F79', 'jdapp;android;10.1.0;10;;network/wifi;Mozilla/5.0 (Linux; Android 10; M2006J10C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/77.0.3865.120 MQQBrowser/6.2 TBS/045230 Mobile Safari/537.36', 'jdapp;android;10.1.0;10;network/wifi;Mozilla/5.0 (Linux; Android 10; M2006J10C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/77.0.3865.120 MQQBrowser/6.2 TBS/045230 Mobile Safari/537.36', 'jdapp;android;10.1.0;10;network/wifi;Mozilla/5.0 (Linux; Android 10; ONEPLUS A6000 Build/QKQ1.190716.003; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/77.0.3865.120 MQQBrowser/6.2 TBS/045224 Mobile Safari/537.36', 'jdapp;android;10.1.0;9;network/wifi;Mozilla/5.0 (Linux; Android 9; MHA-AL00 Build/HUAWEIMHA-AL00; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/66.0.3359.126 MQQBrowser/6.2 TBS/044942 Mobile Safari/537.36', 'jdapp;android;10.1.0;8.1.0;network/wifi;Mozilla/5.0 (Linux; Android 8.1.0; 16 X Build/OPM1.171019.026; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/66.0.3359.126 MQQBrowser/6.2 TBS/044942 Mobile Safari/537.36', 'jdapp;android;10.1.0;8.0.0;network/wifi;Mozilla/5.0 (Linux; Android 8.0.0; HTC U-3w Build/OPR6.170623.013; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/66.0.3359.126 MQQBrowser/6.2 TBS/044942 Mobile Safari/537.36', 'jdapp;iPhone;10.1.0;14.0.1;network/wifi;Mozilla/5.0 (iPhone; CPU iPhone OS 14_0_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1', 'jdapp;android;10.1.0;10;network/wifi;Mozilla/5.0 (Linux; Android 10; LYA-AL00 Build/HUAWEILYA-AL00L; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/77.0.3865.120 MQQBrowser/6.2 TBS/045230 Mobile Safari/537.36', 'jdapp;iPhone;10.1.0;14.2;network/wifi;Mozilla/5.0 (iPhone; CPU iPhone OS 14_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1', 'jdapp;iPhone;10.1.0;14.3;network/wifi;Mozilla/5.0 (iPhone; CPU iPhone OS 14_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1', 'jdapp;iPhone;10.1.0;14.2;network/wifi;Mozilla/5.0 (iPhone; CPU iPhone OS 14_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1', 'jdapp;android;10.1.0;8.1.0;network/wifi;Mozilla/5.0 (Linux; Android 8.1.0; MI 8 Build/OPM1.171019.026; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/66.0.3359.126 MQQBrowser/6.2 TBS/045131 Mobile Safari/537.36', 'jdapp;android;10.1.0;10;network/wifi;Mozilla/5.0 (Linux; Android 10; Redmi K20 Pro Premium Edition Build/QKQ1.190825.002; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/77.0.3865.120 MQQBrowser/6.2 TBS/045227 Mobile Safari/537.36', 'jdapp;iPhone;10.1.0;14.3;network/wifi;Mozilla/5.0 (iPhone; CPU iPhone OS 14_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1', 'jdapp;iPhone;10.1.0;14.3;network/4g;Mozilla/5.0 (iPhone; CPU iPhone OS 14_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1', 'jdapp;android;10.1.0;11;network/wifi;Mozilla/5.0 (Linux; Android 11; Redmi K20 Pro Premium Edition Build/RKQ1.200826.002; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/77.0.3865.120 MQQBrowser/6.2 TBS/045513 Mobile Safari/537.36', 'jdapp;android;10.1.0;10;network/wifi;Mozilla/5.0 (Linux; Android 10; MI 8 Build/QKQ1.190828.002; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/77.0.3865.120 MQQBrowser/6.2 TBS/045227 Mobile Safari/537.36', 'jdapp;iPhone;10.1.0;14.1;network/wifi;Mozilla/5.0 (iPhone; CPU iPhone OS 14_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1'];

function randomNumber(_0x55d971 = 0x0, _0x325d4e = 0x64) {
    return Math['min'](Math['floor'](_0x55d971 + Math['random']() * (_0x325d4e - _0x55d971)), _0x325d4e);
}
let config = {};
try {
    config = JSON['parse'](fs['readFileSync'](configPath, 'utf8'));
} catch (_0x32f029) {
    console['error']('config.json文件读取失败，请检查文件格式否正确！');
    return;
}
const logDir = path['join'](__dirname, 'log');
if (!fs['existsSync'](logDir)) {
    fs['mkdirSync'](logDir);
}
const logFileName = 'app_' + timeFormat(new Date(), 'date') + '.log';
const logFilePath = path['join'](logDir, logFileName);
const originalConsoleLog = console['log'];
console['log'] = function(..._0x5d5c5b) {
    const _0x49eebc = util['format'](..._0x5d5c5b) + '';
    fs['appendFileSync'](logFilePath, _0x49eebc);
    originalConsoleLog['apply'](console, _0x5d5c5b);
};
const originalConsoleError = console['error'];
console['error'] = function(..._0x5f49a3) {
    const _0x4ecf92 = util['format'](..._0x5f49a3) + '';
    fs['appendFileSync'](logFilePath, _0x4ecf92);
    originalConsoleError['apply'](console, _0x5f49a3);
};
let tryNum = 0x4;
let maxQq = 0x14;
let maxXc = 0x3;
let qqjgTime = 0xfa;
let maxAccount = 0x8;
let ycTime = 0x64;
let cookiesArr = [],
    cookie = '';
let canTaskFlag = [];
let TgCkArray = [];
let lqSucArray = [];
let AllEendCode = '|A9|A6|A14|D2|';
let PEendCode = '|A1|A12|A13|A19|A26|';
let JDTimes = new Date()['getTime']();
let apiArray = [];
let nowIndex = 0x0;
let JDTimeJg = 0x0;
let yhqAPiHasSuccess = {};
let nextHour = 0x0;
let ckerror = [];
let removeYhq = [];
let nowRunYhq = '';
let user_agent = '';
let paramsSignLiteMy = '';
if (config['JD_COOKIE']) {
    cookiesArr = config['JD_COOKIE'];
} else {
    console['log']('【提示】请先添加JD_COOKIE');
    return ![];
} if (config['YHQ_REMOVE'] && config['YHQ_REMOVE']['split'](',')['length'] >= 0x1) {
    if (config['YHQ_REMOVE']['toLowerCase']() == 'all') {
        console['log']('读取环境变量排除的优惠券为：不抢作者所有的券！');
        apiList = [];
    } else {
        console['log']('读取环境变量排除的优惠券为：' + config['YHQ_REMOVE']);
        removeYhq = config['YHQ_REMOVE']['split'](',');
    }
}
if (config['YHQ_NOWRUN']) {
    console['log']('读取环境变量立即执行优惠券为：' + config['YHQ_NOWRUN']);
    nowRunYhq = config['YHQ_NOWRUN'];
}
try {
    const apiListMy = require('./jdYhqApiListMy.js')['apiList'];
    if (apiListMy['length'] > 0x0) {
        for (var alm in apiListMy) {
            if (apiListMy[alm]['qName'] && apiListMy[alm]['qApi'] && apiListMy[alm]['qTime']) {
                apiList['push'](apiListMy[alm]);
                console['log']('加载自定义API:' + apiListMy[alm]['qName']);
            }
        }
    }
} catch (_0x3c07a2) {
    console['log']('未配置自定义API！');
}
try {
    paramsSignLiteMy = new window[('ParamsSignLite')]({
        'appId': '35fa0',
        'preRequest': !0x1
    });
} catch (_0x1210c3) {}
if (config['YHQ_API'] && config['YHQ_API']['indexOf'](',') > -0x1 && config['YHQ_API']['split'](',')['length'] >= 0x5) {
    console['log']('读取环境变量成功：' + config['YHQ_API']);
    let YHQ_API_ARR = config['YHQ_API']['split'](',');
    tryNum = parseInt(YHQ_API_ARR[0x0]);
    if (parseInt(YHQ_API_ARR[0x1]) > maxQq) {
        maxQq = parseInt(YHQ_API_ARR[0x1]);
    }
    maxXc = parseInt(YHQ_API_ARR[0x2]);
    qqjgTime = parseInt(YHQ_API_ARR[0x3]);
    maxAccount = parseInt(YHQ_API_ARR[0x4]);
    if (YHQ_API_ARR['length'] >= 0x6) {
        ycTime = parseInt(YHQ_API_ARR[0x5]);
    }
}
console['log']('' + timeFormat() + ':' + '----脚本运行成功，请不要关闭窗口----');
let isMainRunning = ![];
async function executeMain() {
    if (isMainRunning) {
        console['log']('任务执行中...');
        return;
    }
    isMainRunning = !![];
    try {
        resertCs();
        await main();
    } catch (_0x5b94da) {
        console['error']('main 函数执行失败:', _0x5b94da);
    } finally {
        isMainRunning = ![];
    }
}
executeMain();
cron['schedule']('* * * * *', () => {
    try {
        const _0x2d60f1 = new Date()['getMinutes']();
        if (_0x2d60f1 === 0x3b) {
            executeMain();
        } else {
            if (!isMainRunning && _0x2d60f1 % 0x5 === 0x0) {
                console['log']('' + timeFormat() + ':' + '未到任务执行时间，跳过执行......');
            }
        }
    } catch (_0x550b75) {}
});
async function main() {
    try {
        if (!cookiesArr[0x0]) {
            console['log']('【提示】请先增加JD账号一cookie');
            return;
        } else {
            console['log']('共检测到' + cookiesArr['length'] + '个cookie');
        }

        // 初始化 SmashUtils
        await initSmashUtils();

        if (new Date()['getDate']() == 0x1 && new Date()['getHours']() == 0x0) {
            $['setjson']({}, 'yhqAPiHasSuccess');
            console['log']('清空缓存！');
        }
        nextHour = nextHourF();
        console['log']('下次抢券时间：' + nextHour + ':00:00');
        user_agent = USER_AGENTS[randomNumber(0x0, USER_AGENTS['length'])];
        for (var _0x59d662 in apiList) {
            if (nowRunYhq && nowRunYhq['length'] > 0x0 && nowRunYhq == apiList[_0x59d662]['qName']) {
                console['log']('立即抢券（跑完记得删除或禁用该环境变量）：' + apiList[_0x59d662]['qName']);
                apiArray['push'](apiList[_0x59d662]);
                doAPIList(apiArray['length'] - 0x1);
                continue;
            }
            if (checkYhq(apiList[_0x59d662], nextHour) && !isRemoveYhqF(apiList[_0x59d662]) && apiArray['length'] < maxQq) {
                apiArray['push'](apiList[_0x59d662]);
                console['log']('名称：' + apiList[_0x59d662]['qName']);
            }
        }
        if (apiArray['length'] <= 0x0) {
            console['log']('当前时间段没有优惠券需要领取！');
            return;
        }
        if ($['getdata']('JDTimeJg') && $['getdata']('JDTimeJg') != 0x0) {
            JDTimeJg = $['getdata']('JDTimeJg');
        }
        if ($['getjson']('yhqAPiHasSuccess')) {
            yhqAPiHasSuccess = $['getjson']('yhqAPiHasSuccess');
        }
        let _0x5a99f8 = jgNextHourF() + JDTimeJg - ycTime;
        if (_0x5a99f8 > 0x2 * 0x3c * 0x3e8) {
            console['log'](parseInt(_0x5a99f8 / 0x3c / 0x3e8) + '分后才开始！');
            return;
        }
        if (_0x5a99f8 > 0x0) {
            console['log'](parseInt(_0x5a99f8 / 0x3c / 0x3e8) + '分后开始任务，请不要结束任务！');
            await $['wait'](_0x5a99f8);
        }
        for (let _0x57b2e7 in apiArray) {
            if (!yhqAPiHasSuccess[apiArray[_0x57b2e7]['qName']]) {
                yhqAPiHasSuccess[apiArray[_0x57b2e7]['qName']] = {};
            }
            doAPIList(_0x57b2e7);
        }
        await $['wait'](0x3 * 0x3e8);
        for (let _0x160780 in apiArray) {
            let _0x1d34e9 = '';
            if (lqSucArray[_0x160780]['length'] > 0x0) {
                if (apiArray[_0x160780]['qName']) {
                    _0x1d34e9 += '券【' + apiArray[_0x160780]['qName'] + '】';
                }
                _0x1d34e9 += '成功领取的用户有：';
                for (var _0x3a4043 in lqSucArray[_0x160780]) {
                    cookie = cookiesArr[lqSucArray[_0x160780][_0x3a4043]];
                    let _0x18ba0d = decodeURIComponent(cookie['match'](/pt_pin=([^; ]+)(?=;?)/) && cookie['match'](/pt_pin=([^; ]+)(?=;?)/)[0x1]);
                    _0x1d34e9 += '' + (lqSucArray[_0x160780][_0x3a4043] + 0x1) + '、' + _0x18ba0d;
                }
                console['log']('************************');
                console['log'](_0x1d34e9);
            }
            if (_0x1d34e9) {
                myNotice(_0x1d34e9);
                console['log'](_0x1d34e9);
                _0x1d34e9 = '';
            }
        }
        $['setjson'](yhqAPiHasSuccess, 'yhqAPiHasSuccess');
    } catch (_0x2aab18) {
        console['error']('main function error:', _0x2aab18);
    }
}

function resertCs() {
    canTaskFlag = [];
    TgCkArray = [];
    lqSucArray = [];
    apiArray = [];
    nowIndex = 0x0;
    yhqAPiHasSuccess = {};
}
async function doAPIList(_0x315c72) {
    canTaskFlag[_0x315c72] = !![];
    TgCkArray[_0x315c72] = [];
    lqSucArray[_0x315c72] = [];
    for (let _0x44db88 = 0x1; _0x44db88 <= tryNum; _0x44db88++) {
        if (canTaskFlag[_0x315c72] && TgCkArray[_0x315c72]['length'] < cookiesArr['length'] && TgCkArray[_0x315c72]['length'] < maxAccount) {
            console['log']('***开始领券【' + apiArray[_0x315c72]['qName'] + '】第' + _0x44db88 + '次请求***');
            for (let _0x44d8f6 = 0x0; _0x44d8f6 < cookiesArr['length'] && _0x44d8f6 < maxAccount; _0x44d8f6++) {
                let _0x35647f = apiArray[_0x315c72]['ckIndex'] ? apiArray[_0x315c72]['ckIndex'] : 0x0;
                if (_0x35647f > 0x0) {
                    if (_0x44d8f6 + 0x1 < _0x35647f) {
                        continue;
                    } else if (_0x44d8f6 + 0x1 > _0x35647f) {
                        break;
                    } else {
                        console['log']('开始执行账号' + _0x35647f + '专属ck:');
                    }
                }
                if (canTaskFlag[_0x315c72]) {
                    if (cookiesArr[_0x44d8f6]) {
                        let _0x513bfb = decodeURIComponent(cookiesArr[_0x44d8f6]['match'](/pt_pin=([^; ]+)(?=;?)/) && cookiesArr[_0x44d8f6]['match'](/pt_pin=([^; ]+)(?=;?)/)[0x1]);
                        if (TgCkArray[_0x315c72]['includes'](_0x44d8f6)) {
                            console['log']('跳过账号' + (_0x44d8f6 + 0x1) + ':' + _0x513bfb + '！');
                            continue;
                        }
                        try {
                            if (yhqAPiHasSuccess[apiArray[_0x315c72]['qName']][_0x513bfb] && nextHour != 0x0) {
                                let _0x43b1ec = getNowDate();
                                if (DateDiff(_0x43b1ec, yhqAPiHasSuccess[apiArray[_0x315c72]['qName']][_0x513bfb]) < apiArray[_0x315c72]['lqSpace']) {
                                    console['log']('其他时间领取成功跳过账号' + (_0x44d8f6 + 0x1) + ':' + _0x513bfb + '！');
                                    TgCkArray[_0x315c72]['push'](_0x44d8f6);
                                    continue;
                                }
                            }
                        } catch (_0x29f0f3) {}
                        nowIndex++;
                        if (nowIndex >= maxXc) {
                            if (nowIndex % maxXc == 0x0) {
                                await $['wait'](qqjgTime - 0x14);
                            } else {
                                await $['wait'](0xa);
                            }
                        }
                        doApiTask(_0x315c72, _0x44d8f6);
                    }
                } else {
                    console['log']('该券已无或者无账号需要请求！');
                    break;
                }
            }
        } else {
            break;
        }
    }
}
async function doApiTask(_0x209282, _0x1f66b9) {
    console['log']('' + nowIndex + '、' + timeFormat() + (':开始领取' + apiArray[_0x209282]['qName'] + '_账号' + (_0x1f66b9 + 0x1)));
    return new Promise(async _0x496286 => {
        if (canTaskFlag[_0x209282]) {
            if (apiArray[_0x209282]['qName']['indexOf']('G') > -0x1 || apiArray[_0x209282]['qApi']['indexOf']('https://s.m.jd.com') > -0x1 || apiArray[_0x209282]['qApi']['indexOf']('h5_awake_wxapp') > -0x1) {
                const _0x118d4c = await getApiUrlGet(_0x209282, _0x1f66b9);
                $['get'](_0x118d4c, (_0x3e42d3, _0x3a7eee, _0x256d53) => {
                    try {
                        if (_0x3e42d3) {
                            console['log']('API请求失败，请检查网络重试');
                        } else {
                            cookie = cookiesArr[_0x1f66b9];
                            let _0x3b8112 = decodeURIComponent(cookie['match'](/pt_pin=([^; ]+)(?=;?)/) && cookie['match'](/pt_pin=([^; ]+)(?=;?)/)[0x1]);
                            console['log']('*' + apiArray[_0x209282]['qName'] + '_【账号' + (_0x1f66b9 + 0x1) + '】' + _0x3b8112 + '*');
                            console['log'](timeFormat() + ':' + _0x256d53);
                            if (_0x256d53['indexOf']('成功') > -0x1) {
                                lqSucArray[_0x209282]['push'](_0x1f66b9);
                                yhqAPiHasSuccess[apiArray[_0x209282]['qName']][_0x3b8112] = getNowDate();
                            } else if (_0x256d53['indexOf']('再来') > -0x1 || _0x256d53['indexOf']('抢光') > -0x1) {
                                canTaskFlag[_0x209282] = ![];
                            }
                        }
                    } catch (_0x761396) {
                        TgCkArray[_0x209282]['push'](_0x1f66b9);
                        $['logErr'](_0x761396, _0x3a7eee);
                    } finally {
                        _0x496286(_0x256d53);
                    }
                });
            } else {
                const _0xf78332 = await getApiUrl(_0x209282, _0x1f66b9);
                $['post'](_0xf78332, (_0xa9f8e8, _0x353712, _0x250069) => {
                    try {
                        if (_0xa9f8e8) {
                            console['log']('' + JSON['stringify'](_0xa9f8e8));
                            console['log']('API请求失败，请检查网络重试');
                        } else {
                            cookie = cookiesArr[_0x1f66b9];
                            let _0x31eecc = decodeURIComponent(cookie['match'](/pt_pin=([^; ]+)(?=;?)/) && cookie['match'](/pt_pin=([^; ]+)(?=;?)/)[0x1]);
                            console['log']('*' + apiArray[_0x209282]['qName'] + '_【账号' + (_0x1f66b9 + 0x1) + '】' + _0x31eecc + '*');
                            _0x250069 = JSON['parse'](_0x250069);
                            let _0x2cf1c0 = '';
                            let _0x3d7c3d = '';
                            try {
                                _0x3d7c3d = '|' + _0x250069['subCode'] + '|';
                                _0x2cf1c0 = _0x250069['subCodeMsg'] || _0x250069['resultData']['msg'];
                            } catch (_0x4ea171) {}
                            if (_0x250069['subCode'] && (_0x250069['subCode'] == 'A1' || _0x250069['subCode'] == '0') || _0x2cf1c0 && _0x2cf1c0['indexOf']('成功') > -0x1) {
                                lqSucArray[_0x209282]['push'](_0x1f66b9);
                                yhqAPiHasSuccess[apiArray[_0x209282]['qName']][_0x31eecc] = getNowDate();
                            }
                            if (AllEendCode['indexOf'](_0x3d7c3d) > -0x1) {
                                if (_0x250069['subCode'] == 'D2' && _0x2cf1c0['substr'](_0x2cf1c0['indexOf']('请') + 0x1, 0x2) == nextHour) {
                                    console['log'](timeFormat() + ':时间未到继续：' + _0x2cf1c0);
                                } else if (nextHour == 0x0) {
                                    console['log'](timeFormat() + ':继续：' + _0x2cf1c0);
                                } else {
                                    canTaskFlag[_0x209282] = ![];
                                    console['log'](timeFormat() + ':' + _0x2cf1c0);
                                }
                            } else if (PEendCode['indexOf'](_0x3d7c3d) > -0x1) {
                                TgCkArray[_0x209282]['push'](_0x1f66b9);
                                console['log'](timeFormat() + ':' + _0x2cf1c0 + ',subCode2_' + _0x3d7c3d);
                            } else if (_0x250069['code'] && _0x250069['code'] == '3') {
                                TgCkArray[_0x209282]['push'](_0x1f66b9);
                                console['log'](timeFormat() + ':ck过期！');
                                if (!checkHasCz(ckerror, _0x1f66b9)) {
                                    ckerror['push'](_0x1f66b9);
                                    myNotice('【账号' + (_0x1f66b9 + 0x1) + '】' + _0x31eecc + '——ck过期!');
                                    console['error']('【账号' + (_0x1f66b9 + 0x1) + '】' + _0x31eecc + '——ck过期!');
                                }
                            } else {
                                console['log'](timeFormat() + ':' + JSON['stringify'](_0x250069));
                            }
                        }
                    } catch (_0x279aaf) {
                        TgCkArray[_0x209282]['push'](_0x1f66b9);
                        $['logErr'](_0x279aaf, _0x353712);
                    } finally {
                        _0x496286(_0x250069);
                    }
                });
            }
        } else {
            console['log']('该券已无或已结束！');
        }
    });
}

function getJDTime() {
    return new Promise(_0x21ee15 => {
        $['post']({
            'url': 'https://api.m.jd.com/client.action?functionId=queryMaterialProducts&client=wh5'
        }, async(_0x2e7080, _0x1522aa, _0x1dbcae) => {
            try {
                if (_0x2e7080) {
                    console['log']('获取JD时间失败');
                } else {
                    _0x1dbcae = JSON['parse'](_0x1dbcae);
                    if (_0x1dbcae['code'] && _0x1dbcae['code'] == '0') {
                        JDTimes = parseInt(_0x1dbcae['currentTime2']);
                        if (JDTimeJg == 0x0 || JDTimeJg != 0x0 && new Date()['getTime']() - JDTimes < JDTimeJg) {
                            JDTimeJg = new Date()['getTime']() - JDTimes;
                        }
                    } else {
                        console['log']('获取JD时间失败:' + JSON['stringify'](_0x1dbcae));
                    }
                }
            } catch (_0x5206ce) {
                $['logErr'](_0x5206ce, _0x1522aa);
            } finally {
                _0x21ee15(_0x1dbcae);
            }
        });
    });
}

function checkYhq(_0x3d720a, _0x5c25bd) {
    if (!_0x3d720a['endDate']) {
        return !![];
    }
    if (_0x3d720a['endDate'] && _0x3d720a['qTime'] && new Date(_0x3d720a['endDate'] + ' 23:59:59')['getTime']() > new Date()['getTime']()) {
        let _0x2776bb = _0x3d720a['qTime']['split'](',');
        if (_0x2776bb['length'] > 0x0 && _0x2776bb['includes'](_0x5c25bd + '')) {
            return !![];
        }
    }
    return ![];
}

function isRemoveYhqF(_0xee10e3) {
    let _0x27a404 = ![];
    if (removeYhq && removeYhq['length'] > 0x0) {
        for (var _0x248875 in removeYhq) {
            if (_0xee10e3['qName'] == removeYhq[_0x248875]) {
                console['log']('排除优惠券：' + _0xee10e3['qName']);
                _0x27a404 = !![];
                break;
            }
        }
    }
    return _0x27a404;
}
async function getApiUrl(_0x56778b, _0x23696c) {
    const apiLogResult = await getApiLog(apiArray[_0x56778b]['qApi']);
    const _0xcfebdf = await getDecryptUrlTy(apiLogResult);
    return {
        'url': _0xcfebdf,
        'headers': {
            'user-agent': user_agent,
            'content-Type': 'application/x-www-form-urlencoded',
            'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
            'accept-encoding': 'gzip, deflate, br',
            'accept-language': 'zh-CN,zh;q=0.9',
            'cache-control': 'max-age=0',
            'cookie': cookiesArr[_0x23696c]
        }
    };
}
async function getApiUrlGet(_0x5e747e, _0xc808e0) {
    if (apiArray[_0x5e747e]['qApi']['indexOf']('https://s.m.jd.com') > -0x1 || apiArray[_0x5e747e]['qApi']['indexOf']('h5_awake_wxapp') > -0x1) {
        const apiLogResult = await getApiLog(apiArray[_0x5e747e]['qApi']);
        const _0x4d74f8 = await getDecryptUrlTy(apiLogResult);
        return {
            'url': _0x4d74f8,
            'headers': {
                'User-Agent': user_agent,
                'Cookie': cookiesArr[0x0],
                'Accept': '*/*',
                'Accept-Encoding': 'gzip, deflate, br',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Referer': 'https://coupon.m.jd.com/'
            }
        };
    } else if (apiArray[_0x5e747e]['qApi']['indexOf']('appid=plus_business') > -0x1) {
        return {
            'url': apiArray[_0x5e747e]['qApi'],
            'headers': {
                'User-Agent': user_agent,
                'accept-encoding': 'gzip, deflate, br',
                'accept-language': 'zh-CN,zh;q=0.9',
                'Cookie': cookiesArr[_0xc808e0],
                'origin': 'https://plus.m.jd.com'
            }
        };
    } else {
        return {
            'url': apiArray[_0x5e747e]['qApi'],
            'headers': {
                'User-Agent': user_agent,
                'accept-encoding': 'gzip, deflate, br',
                'accept-language': 'zh-CN,zh;q=0.9',
                'Cookie': cookiesArr[_0xc808e0]
            }
        };
    }
}

function jgNextHourF() {
    let _0x3e1e1e = timeFormat()['substr'](0x0, 0xd) + ':00:00';
    let _0x326886 = Date['parse'](new Date(_0x3e1e1e)) + 0x3c * 0x3c * 0x3e8;
    return _0x326886 - new Date()['getTime']();
}

function myNotice(_0x16de16) {}

function nextHourF() {
    let _0x3e1c44 = new Date();
    return _0x3e1c44['getHours']() + 0x1 >= 0x18 ? 0x0 : _0x3e1c44['getHours']() + 0x1;
}

function DateDiff(_0x58a163, _0x452cf8) {
    var _0x14660f, _0x3ffd45, _0x4e88e6, _0x530806;
    _0x14660f = _0x58a163['split']('-');
    _0x3ffd45 = new Date(_0x14660f[0x1] + '-' + _0x14660f[0x2] + '-' + _0x14660f[0x0]);
    _0x14660f = _0x452cf8['split']('-');
    _0x4e88e6 = new Date(_0x14660f[0x1] + '-' + _0x14660f[0x2] + '-' + _0x14660f[0x0]);
    _0x530806 = parseInt(Math['abs'](_0x3ffd45 - _0x4e88e6) / 0x3e8 / 0x3c / 0x3c / 0x18);
    return _0x530806;
}

function getNowDate() {
    let _0x11667f = new Date();
    return _0x11667f['getFullYear']() + '-' + (_0x11667f['getMonth']() + 0x1 >= 0xa ? _0x11667f['getMonth']() + 0x1 : '0' + (_0x11667f['getMonth']() + 0x1)) + '-' + (_0x11667f['getDate']() >= 0xa ? _0x11667f['getDate']() : '0' + _0x11667f['getDate']());
}

function timeFormat(_0x476759, _0x18f799) {
    let _0x1b1c03;
    if (_0x476759) {
        _0x1b1c03 = new Date(_0x476759);
    } else {
        _0x1b1c03 = new Date();
    } if (_0x18f799 == 'date') {
        return _0x1b1c03['getFullYear']() + '-' + (_0x1b1c03['getMonth']() + 0x1 >= 0xa ? _0x1b1c03['getMonth']() + 0x1 : '0' + (_0x1b1c03['getMonth']() + 0x1)) + '-' + (_0x1b1c03['getDate']() >= 0xa ? _0x1b1c03['getDate']() : '0' + _0x1b1c03['getDate']());
    }
    return _0x1b1c03['getFullYear']() + '-' + (_0x1b1c03['getMonth']() + 0x1 >= 0xa ? _0x1b1c03['getMonth']() + 0x1 : '0' + (_0x1b1c03['getMonth']() + 0x1)) + '-' + (_0x1b1c03['getDate']() >= 0xa ? _0x1b1c03['getDate']() : '0' + _0x1b1c03['getDate']()) + ' ' + (_0x1b1c03['getHours']() >= 0xa ? _0x1b1c03['getHours']() : '0' + _0x1b1c03['getHours']()) + ':' + (_0x1b1c03['getMinutes']() >= 0xa ? _0x1b1c03['getMinutes']() : '0' + _0x1b1c03['getMinutes']()) + ':' + (_0x1b1c03['getSeconds']() >= 0xa ? _0x1b1c03['getSeconds']() : '0' + _0x1b1c03['getSeconds']()) + ':' + _0x1b1c03['getMilliseconds']();
}

async function getApiLog(_0x3a204f) {
    let _0x1ebbfc = smashUtils['getRandom'](0x8);
    let smashResult = await smashUtils['get_risk_result']({
        'id': 'coupon',
        'data': {
            'random': _0x1ebbfc
        }
    }, "") || {};
    let _0x4ae01c = smashResult['log'];
    let _0xfb9364 = encodeURIComponent(',"log":"' + _0x4ae01c + '","random":"' + _0x1ebbfc + '"');
    if (_0x3a204f && _0x3a204f['indexOf']('%7D') > -0x1) {
        _0xfb9364 = _0x3a204f['substring'](0x0, _0x3a204f['indexOf']('%7D')) + _0xfb9364 + _0x3a204f['substring'](_0x3a204f['indexOf']('%7D'), _0x3a204f['length']);
    }
    return _0xfb9364;
}

function checkHasCz(_0xbf4a7f, _0x244387) {
    let _0x406a4f = ![];
    if (_0xbf4a7f) {
        for (var _0x349d99 in _0xbf4a7f) {
            if (_0xbf4a7f[_0x349d99] == _0x244387) {
                _0x406a4f = !![];
                break;
            }
        }
    }
    return _0x406a4f;
}

function getUrlQueryParams(_0x4c1021, _0x1ce2ac) {
    let _0x1da674 = new RegExp('(^|&)' + _0x1ce2ac + '=([^&]*)(&|$)', 'i');
    const split = 'split'; 
    const urlParts = _0x4c1021[split]('?');
    let _0x309e14 = urlParts[0x1]['substr'](0x0)['match'](_0x1da674);
    if (_0x309e14 != null) {
        return decodeURIComponent(_0x309e14[0x2]);
    };
    return '';
}

function sha256Hash(_0x31e864) {
    const _0x39d785 = new TextEncoder();
    const _0x2355d8 = _0x39d785['encode'](_0x31e864);
    const _0x4f239d = $['CryptoJS']['SHA256']($['CryptoJS']['enc']['Utf8']['parse'](_0x31e864));
    const _0x5bad46 = _0x4f239d['toString']($['CryptoJS']['enc']['Hex']);
    return _0x5bad46;
}

function getDecryptUrlTy(_0x1462ff) {
    return new Promise((_0x1c381e, _0xa1473d) => {
        let _0x20fc97 = sha256Hash(getUrlQueryParams(_0x1462ff, 'body'));
        let _0x2e7773 = {
            'appid': 'babelh5',
            'body': _0x20fc97,
            'client': 'wh5',
            'clientVersion': '1.0.0',
            'functionId': 'newBabelAwardCollection'
        };
        paramsSignLiteMy['sign'](_0x2e7773)['then'](_0xe4545 => {
            _0x1c381e(_0x1462ff + '&h5st=' + _0xe4545['h5st']);
        })['catch'](_0x52cccd => {
            console['error']('签名失败:', _0x52cccd);
            _0x1c381e(_0x1462ff);
        });
    });
}

function getDecryptUrl(_0x1a1eaf) {
    _0x1a1eaf = _0x1a1eaf + '&t=' + Date['now']();
    stk = getUrlQueryParams(_0x1a1eaf, '_stk');
    if (stk) {
        const _0x660091 = format('yyyyMMddhhmmssSSS', Date['now']());
        const _0x3b96a4 = $['genKey']($['token'], $['fp']['toString'](), _0x660091['toString'](), $['appId']['toString'](), $['CryptoJS'])['toString']($['CryptoJS']['enc']['Hex']);
        let _0x2a3caf = '';
        stk['split'](',')['map']((_0x1b945f, _0x23c740) => {
            _0x2a3caf += _0x1b945f + ':' + getUrlQueryParams(_0x1a1eaf, _0x1b945f) + (_0x23c740 === stk['split'](',')['length'] - 0x1 ? '' : '&');
        });
        const _0x35ce14 = $['CryptoJS']['HmacSHA256'](_0x2a3caf, _0x3b96a4['toString']())['toString']($['CryptoJS']['enc']['Hex']);
        return _0x1a1eaf + '&h5st=' + encodeURIComponent(['' ['concat'](_0x660091['toString']()), '' ['concat']($['fp']['toString']()), '' ['concat']($['appId']['toString']()), '' ['concat']($['token']), '' ['concat'](_0x35ce14), '3.0;'['concat'](_0x660091)]['join'](';')) + '&__t=' + Date['now']();
    }
}
async function requestAlgo() {
    $['appId'] = '8ba9b';
    $['fp'] = (getRandomIDPro({
        'size': 0xd
    }) + Date['now']())['slice'](0x0, 0x10);
    const _0xf4cd6b = {
        'url': 'https://cactus.jd.com/request_algo?g_ty=ajax',
        'headers': {
            'Authority': 'cactus.jd.com',
            'Pragma': 'no-cache',
            'Cache-Control': 'no-cache',
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'Origin': 'https://st.jingxi.com',
            'Sec-Fetch-Site': 'cross-site',
            'User-Agent': $['user_agent'],
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'Referer': 'https://st.jingxi.com/',
            'Accept-Language': 'zh-CN,zh;q=0.9,zh-TW;q=0.8,en;q=0.7'
        },
        'body': JSON['stringify']({
            'version': '1.0',
            'fp': $['fp'],
            'appId': $['appId'],
            'timestamp': Date['now'](),
            'platform': 'web',
            'expandParams': ''
        })
    };
    return new Promise(async _0x5058db => {
        $['post'](_0xf4cd6b, (_0xe25c05, _0x6e76dd, _0xf224a0) => {
            try {
                const {
                    ret, msg, data: {
                        result
                    } = {}
                } = JSON['parse'](_0xf224a0);
                $['token'] = result['tk'];
                $['genKey'] = new Function('return ' + result['algo'])();
            } catch (_0x38239b) {
                $['logErr'](_0x38239b, _0x6e76dd);
            } finally {
                _0x5058db();
            }
        });
    });
}

function getRandomIDPro() {
    var _0x158423, _0x35153a, _0x281d64 = void 0x0 === (_0x55a423 = (_0x35153a = 0x0 < arguments['length'] && void 0x0 !== arguments[0x0] ? arguments[0x0] : {})['size']) ? 0xa : _0x55a423,
        _0x55a423 = void 0x0 === (_0x55a423 = _0x35153a['dictType']) ? 'number' : _0x55a423,
        _0x2bdbe7 = '';
    if ((_0x35153a = _0x35153a['customDict']) && 'string' == typeof _0x35153a) _0x158423 = _0x35153a;
    else switch (_0x55a423) {
        case 'alphabet':
            _0x158423 = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
            break;
        case 'max':
            _0x158423 = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ_-';
            break;
        case 'number':
        default:
            _0x158423 = '0123456789';
    }
    for (; _0x281d64--;) _0x2bdbe7 += _0x158423[Math['random']() * _0x158423['length'] | 0x0];
    return _0x2bdbe7;
}

function format(_0x4e2821, _0x1830a2) {
    if (!_0x4e2821) _0x4e2821 = 'yyyy-MM-dd';
    var _0x1660d1;
    if (!_0x1830a2) {
        _0x1660d1 = Date['now']();
    } else {
        _0x1660d1 = new Date(_0x1830a2);
    }
    var _0x5b159f, _0x2c72b9 = new Date(_0x1660d1),
        _0x557d25 = _0x4e2821,
        _0x22de01 = {
            'M+': _0x2c72b9['getMonth']() + 0x1,
            'd+': _0x2c72b9['getDate'](),
            'D+': _0x2c72b9['getDate'](),
            'h+': _0x2c72b9['getHours'](),
            'H+': _0x2c72b9['getHours'](),
            'm+': _0x2c72b9['getMinutes'](),
            's+': _0x2c72b9['getSeconds'](),
            'w+': _0x2c72b9['getDay'](),
            'q+': Math['floor']((_0x2c72b9['getMonth']() + 0x3) / 0x3),
            'S+': _0x2c72b9['getMilliseconds']()
        };
    /(y+)/i ['test'](_0x557d25) && (_0x557d25 = _0x557d25['replace'](RegExp['$1'], '' ['concat'](_0x2c72b9['getFullYear']())['substr'](0x4 - RegExp['$1']['length'])));
    Object['keys'](_0x22de01)['forEach'](_0x3a57f9 => {
        if (new RegExp('(' ['concat'](_0x3a57f9, ')'))['test'](_0x557d25)) {
            var _0x4c2a9e, _0xc9b7ea = 'S+' === _0x3a57f9 ? '000' : '00';
            _0x557d25 = _0x557d25['replace'](RegExp['$1'], 0x1 == RegExp['$1']['length'] ? _0x22de01[_0x3a57f9] : '' ['concat'](_0xc9b7ea)['concat'](_0x22de01[_0x3a57f9])['substr']('' ['concat'](_0x22de01[_0x3a57f9])['length']));
        }
    });
    return _0x557d25;
}
function Env(t, e) {
    "undefined" != typeof process && JSON.stringify(process.env).indexOf("GITHUB") > -1 && process.exit(0);
    class s {
        constructor(t) {
            this.env = t
        }
        send(t, e = "GET") {
            t = "string" == typeof t ? {
                url: t
            } : t;
            let s = this.get;
            return "POST" === e && (s = this.post), new Promise((e, i) => {
                s.call(this, t, (t, s, r) => {
                    t ? i(t) : e(s)
                })
            })
        }
        get(t) {
            return this.send.call(this.env, t)
        }
        post(t) {
            return this.send.call(this.env, t, "POST")
        }
    }
    return new class {
        constructor(t, e) {
            this.name = t, this.http = new s(this), this.data = null, this.dataFile = "box.dat", this.logs = [], this.isMute = !1, this.isNeedRewrite = !1, this.logSeparator = "\n", this.startTime = (new Date).getTime(), Object.assign(this, e), this.log("", `🔔${this.name}, 开始!`)
        }
        isNode() {
            return "undefined" != typeof module && !!module.exports
        }
        isQuanX() {
            return "undefined" != typeof $task
        }
        isSurge() {
            return "undefined" != typeof $httpClient && "undefined" == typeof $loon
        }
        isLoon() {
            return "undefined" != typeof $loon
        }
        toObj(t, e = null) {
            try {
                return JSON.parse(t)
            } catch {
                return e
            }
        }
        toStr(t, e = null) {
            try {
                return JSON.stringify(t)
            } catch {
                return e
            }
        }
        getjson(t, e) {
            let s = e;
            const i = this.getdata(t);
            if (i) try {
                s = JSON.parse(this.getdata(t))
            } catch {}
            return s
        }
        setjson(t, e) {
            try {
                return this.setdata(JSON.stringify(t), e)
            } catch {
                return !1
            }
        }
        getScript(t) {
            return new Promise(e => {
                this.get({
                    url: t
                }, (t, s, i) => e(i))
            })
        }
        runScript(t, e) {
            return new Promise(s => {
                let i = this.getdata("@chavy_boxjs_userCfgs.httpapi");
                i = i ? i.replace(/\n/g, "").trim() : i;
                let r = this.getdata("@chavy_boxjs_userCfgs.httpapi_timeout");
                r = r ? 1 * r : 20, r = e && e.timeout ? e.timeout : r;
                const [o, h] = i.split("@"), n = {
                    url: `http://${h}/v1/scripting/evaluate`,
                    body: {
                        script_text: t,
                        mock_type: "cron",
                        timeout: r
                    },
                    headers: {
                        "X-Key": o,
                        Accept: "*/*"
                    }
                };
                this.post(n, (t, e, i) => s(i))
            }).catch(t => this.logErr(t))
        }
        loaddata() {
            if (!this.isNode()) return {}; {
                this.fs = this.fs ? this.fs : require("fs"), this.path = this.path ? this.path : require("path");
                const t = this.path.resolve(this.dataFile),
                    e = this.path.resolve(process.cwd(), this.dataFile),
                    s = this.fs.existsSync(t),
                    i = !s && this.fs.existsSync(e);
                if (!s && !i) return {}; {
                    const i = s ? t : e;
                    try {
                        return JSON.parse(this.fs.readFileSync(i))
                    } catch (t) {
                        return {}
                    }
                }
            }
        }
        writedata() {
            if (this.isNode()) {
                this.fs = this.fs ? this.fs : require("fs"), this.path = this.path ? this.path : require("path");
                const t = this.path.resolve(this.dataFile),
                    e = this.path.resolve(process.cwd(), this.dataFile),
                    s = this.fs.existsSync(t),
                    i = !s && this.fs.existsSync(e),
                    r = JSON.stringify(this.data);
                s ? this.fs.writeFileSync(t, r) : i ? this.fs.writeFileSync(e, r) : this.fs.writeFileSync(t, r)
            }
        }
        lodash_get(t, e, s) {
            const i = e.replace(/\[(\d+)\]/g, ".$1").split(".");
            let r = t;
            for (const t of i)
                if (r = Object(r)[t], void 0 === r) return s;
            return r
        }
        lodash_set(t, e, s) {
            return Object(t) !== t ? t : (Array.isArray(e) || (e = e.toString().match(/[^.[\]]+/g) || []), e.slice(0, -1).reduce((t, s, i) => Object(t[s]) === t[s] ? t[s] : t[s] = Math.abs(e[i + 1]) >> 0 == +e[i + 1] ? [] : {}, t)[e[e.length - 1]] = s, t)
        }
        getdata(t) {
            let e = this.getval(t);
            if (/^@/.test(t)) {
                const [, s, i] = /^@(.*?)\.(.*?)$/.exec(t), r = s ? this.getval(s) : "";
                if (r) try {
                    const t = JSON.parse(r);
                    e = t ? this.lodash_get(t, i, "") : e
                } catch (t) {
                    e = ""
                }
            }
            return e
        }
        setdata(t, e) {
            let s = !1;
            if (/^@/.test(e)) {
                const [, i, r] = /^@(.*?)\.(.*?)$/.exec(e), o = this.getval(i), h = i ? "null" === o ? null : o || "{}" : "{}";
                try {
                    const e = JSON.parse(h);
                    this.lodash_set(e, r, t), s = this.setval(JSON.stringify(e), i)
                } catch (e) {
                    const o = {};
                    this.lodash_set(o, r, t), s = this.setval(JSON.stringify(o), i)
                }
            } else s = this.setval(t, e);
            return s
        }
        getval(t) {
            return this.isSurge() || this.isLoon() ? $persistentStore.read(t) : this.isQuanX() ? $prefs.valueForKey(t) : this.isNode() ? (this.data = this.loaddata(), this.data[t]) : this.data && this.data[t] || null
        }
        setval(t, e) {
            return this.isSurge() || this.isLoon() ? $persistentStore.write(t, e) : this.isQuanX() ? $prefs.setValueForKey(t, e) : this.isNode() ? (this.data = this.loaddata(), this.data[e] = t, this.writedata(), !0) : this.data && this.data[e] || null
        }
        initGotEnv(t) {
            this.got = this.got ? this.got : require("got"), this.cktough = this.cktough ? this.cktough : require("tough-cookie"), this.ckjar = this.ckjar ? this.ckjar : new this.cktough.CookieJar, t && (t.headers = t.headers ? t.headers : {}, void 0 === t.headers.Cookie && void 0 === t.cookieJar && (t.cookieJar = this.ckjar))
        }
        get(t, e = (() => {})) {
            t.headers && (delete t.headers["Content-Type"], delete t.headers["Content-Length"]), this.isSurge() || this.isLoon() ? (this.isSurge() && this.isNeedRewrite && (t.headers = t.headers || {}, Object.assign(t.headers, {
                "X-Surge-Skip-Scripting": !1
            })), $httpClient.get(t, (t, s, i) => {
                !t && s && (s.body = i, s.statusCode = s.status), e(t, s, i)
            })) : this.isQuanX() ? (this.isNeedRewrite && (t.opts = t.opts || {}, Object.assign(t.opts, {
                hints: !1
            })), $task.fetch(t).then(t => {
                const {
                    statusCode: s,
                    statusCode: i,
                    headers: r,
                    body: o
                } = t;
                e(null, {
                    status: s,
                    statusCode: i,
                    headers: r,
                    body: o
                }, o)
            }, t => e(t))) : this.isNode() && (this.initGotEnv(t), this.got(t).on("redirect", (t, e) => {
                try {
                    if (t.headers["set-cookie"]) {
                        const s = t.headers["set-cookie"].map(this.cktough.Cookie.parse).toString();
                        s && this.ckjar.setCookieSync(s, null), e.cookieJar = this.ckjar
                    }
                } catch (t) {
                    this.logErr(t)
                }
            }).then(t => {
                const {
                    statusCode: s,
                    statusCode: i,
                    headers: r,
                    body: o
                } = t;
                e(null, {
                    status: s,
                    statusCode: i,
                    headers: r,
                    body: o
                }, o)
            }, t => {
                const {
                    message: s,
                    response: i
                } = t;
                e(s, i, i && i.body)
            }))
        }
        post(t, e = (() => {})) {
            if (t.body && t.headers && !t.headers["Content-Type"] && (t.headers["Content-Type"] = "application/x-www-form-urlencoded"), t.headers && delete t.headers["Content-Length"], this.isSurge() || this.isLoon()) this.isSurge() && this.isNeedRewrite && (t.headers = t.headers || {}, Object.assign(t.headers, {
                "X-Surge-Skip-Scripting": !1
            })), $httpClient.post(t, (t, s, i) => {
                !t && s && (s.body = i, s.statusCode = s.status), e(t, s, i)
            });
            else if (this.isQuanX()) t.method = "POST", this.isNeedRewrite && (t.opts = t.opts || {}, Object.assign(t.opts, {
                hints: !1
            })), $task.fetch(t).then(t => {
                const {
                    statusCode: s,
                    statusCode: i,
                    headers: r,
                    body: o
                } = t;
                e(null, {
                    status: s,
                    statusCode: i,
                    headers: r,
                    body: o
                }, o)
            }, t => e(t));
            else if (this.isNode()) {
                this.initGotEnv(t);
                const {
                    url: s,
                    ...i
                } = t;
                this.got.post(s, i).then(t => {
                    const {
                        statusCode: s,
                        statusCode: i,
                        headers: r,
                        body: o
                    } = t;
                    e(null, {
                        status: s,
                        statusCode: i,
                        headers: r,
                        body: o
                    }, o)
                }, t => {
                    const {
                        message: s,
                        response: i
                    } = t;
                    e(s, i, i && i.body)
                })
            }
        }
        time(t, e = null) {
            const s = e ? new Date(e) : new Date;
            let i = {
                "M+": s.getMonth() + 1,
                "d+": s.getDate(),
                "H+": s.getHours(),
                "m+": s.getMinutes(),
                "s+": s.getSeconds(),
                "q+": Math.floor((s.getMonth() + 3) / 3),
                S: s.getMilliseconds()
            };
            /(y+)/.test(t) && (t = t.replace(RegExp.$1, (s.getFullYear() + "").substr(4 - RegExp.$1.length)));
            for (let e in i) new RegExp("(" + e + ")").test(t) && (t = t.replace(RegExp.$1, 1 == RegExp.$1.length ? i[e] : ("00" + i[e]).substr(("" + i[e]).length)));
            return t
        }
        msg(e = t, s = "", i = "", r) {
            const o = t => {
                if (!t) return t;
                if ("string" == typeof t) return this.isLoon() ? t : this.isQuanX() ? {
                    "open-url": t
                } : this.isSurge() ? {
                    url: t
                } : void 0;
                if ("object" == typeof t) {
                    if (this.isLoon()) {
                        let e = t.openUrl || t.url || t["open-url"],
                            s = t.mediaUrl || t["media-url"];
                        return {
                            openUrl: e,
                            mediaUrl: s
                        }
                    }
                    if (this.isQuanX()) {
                        let e = t["open-url"] || t.url || t.openUrl,
                            s = t["media-url"] || t.mediaUrl;
                        return {
                            "open-url": e,
                            "media-url": s
                        }
                    }
                    if (this.isSurge()) {
                        let e = t.url || t.openUrl || t["open-url"];
                        return {
                            url: e
                        }
                    }
                }
            };
            if (this.isMute || (this.isSurge() || this.isLoon() ? $notification.post(e, s, i, o(r)) : this.isQuanX() && $notify(e, s, i, o(r))), !this.isMuteLog) {
                let t = ["", "==============📣系统通知📣=============="];
                t.push(e), s && t.push(s), i && t.push(i), console.log(t.join("\n")), this.logs = this.logs.concat(t)
            }
        }
        log(...t) {
            t.length > 0 && (this.logs = [...this.logs, ...t]), console.log(t.join(this.logSeparator))
        }
        logErr(t, e) {
            const s = !this.isSurge() && !this.isQuanX() && !this.isLoon();
            s ? this.log("", `❗️${this.name}, 错误!`, t.stack) : this.log("", `❗️${this.name}, 错误!`, t)
        }
        wait(t) {
            return new Promise(e => setTimeout(e, t))
        }
        done(t = {}) {
            const e = (new Date).getTime(),
                s = (e - this.startTime) / 1e3;
            this.log("", `🔔${this.name}, 结束! 🕛 ${s} 秒`), this.log(), (this.isSurge() || this.isQuanX() || this.isLoon()) && $done(t)
        }
    }(t, e)
}