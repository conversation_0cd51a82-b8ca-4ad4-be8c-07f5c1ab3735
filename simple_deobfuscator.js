const fs = require('fs');

// 读取混淆文件
const content = fs.readFileSync('main.min.js', 'utf8');

// 提取字符串数组和解码函数
const arrayMatch = content.match(/const\s+(_0x[a-f0-9]+)\s*=\s*\[([\s\S]*?)\];/);
const shuffleMatch = content.match(/\(function\((_0x[a-f0-9]+),\s*(_0x[a-f0-9]+)\)\s*\{[\s\S]*?\}\((_0x[a-f0-9]+),\s*(0x[a-f0-9]+)\)\);/);
const decodeFuncMatch = content.match(/const\s+(_0x[a-f0-9]+)\s*=\s*function\s*\([^)]*\)\s*\{[\s\S]*?\};/);

if (!arrayMatch || !decodeFuncMatch) {
    console.log('无法找到必要的混淆组件');
    process.exit(1);
}

console.log('找到字符串数组:', arrayMatch[1]);
console.log('找到解码函数:', decodeFuncMatch[1]);

// 创建一个安全的执行环境
const vm = require('vm');
const sandbox = {
    console: { log: () => {} },
    atob: (str) => Buffer.from(str, 'base64').toString('binary'),
    String: String,
    Math: Math,
    parseInt: parseInt,
    decodeURIComponent: decodeURIComponent,
    Buffer: Buffer,
    window: {}
};

try {
    // 执行字符串数组定义
    vm.runInNewContext(arrayMatch[0], sandbox);
    
    // 执行数组重排（如果存在）
    if (shuffleMatch) {
        vm.runInNewContext(shuffleMatch[0], sandbox);
    }
    
    // 执行解码函数定义
    vm.runInNewContext(decodeFuncMatch[0], sandbox);
    
    const decodeFuncName = decodeFuncMatch[1];
    const decodeFunc = sandbox[decodeFuncName];
    
    if (typeof decodeFunc !== 'function') {
        console.log('解码函数创建失败');
        process.exit(1);
    }
    
    console.log('解码函数创建成功');
    
    // 查找所有混淆调用
    const callPattern = new RegExp(`${decodeFuncName}\\s*\\(\\s*'(0x[a-f0-9]+)'\\s*,\\s*'([^']*)'\\s*\\)`, 'g');
    const matches = [];
    let match;
    
    while ((match = callPattern.exec(content)) !== null) {
        matches.push({
            fullMatch: match[0],
            index: match[1],
            key: match[2],
            position: match.index
        });
    }
    
    console.log(`找到 ${matches.length} 个混淆调用`);
    
    // 解码并替换
    let deobfuscated = content;
    let successCount = 0;
    
    // 从后往前替换
    for (let i = matches.length - 1; i >= 0; i--) {
        const matchInfo = matches[i];
        
        try {
            const decoded = decodeFunc(matchInfo.index, matchInfo.key);
            
            if (decoded && typeof decoded === 'string') {
                // 创建安全的替换字符串
                let replacement;
                if (decoded.includes("'") && !decoded.includes('"')) {
                    replacement = '"' + decoded + '"';
                } else if (decoded.includes('"') && !decoded.includes("'")) {
                    replacement = "'" + decoded + "'";
                } else if (decoded.includes("'") || decoded.includes('"') || decoded.includes('\n')) {
                    replacement = '`' + decoded.replace(/`/g, '\\`').replace(/\$/g, '\\$') + '`';
                } else {
                    replacement = "'" + decoded + "'";
                }
                
                // 执行替换
                const before = deobfuscated.substring(0, matchInfo.position);
                const after = deobfuscated.substring(matchInfo.position + matchInfo.fullMatch.length);
                deobfuscated = before + replacement + after;
                
                successCount++;
                
                // 显示进度
                if (successCount % 50 === 0) {
                    console.log(`已解码 ${successCount}/${matches.length} 个字符串`);
                }
            }
        } catch (e) {
            console.log(`解码失败 ${matchInfo.fullMatch}: ${e.message}`);
        }
    }
    
    console.log(`成功解码 ${successCount} 个字符串`);
    
    // 清理混淆代码
    deobfuscated = deobfuscated.replace(/const\s+_0x[a-f0-9]+\s*=\s*\[[\s\S]*?\];/, '// 字符串数组已移除');
    deobfuscated = deobfuscated.replace(/\(function\(_0x[a-f0-9]+,\s*_0x[a-f0-9]+\)\s*\{[\s\S]*?\}\(_0x[a-f0-9]+,\s*0x[a-f0-9]+\)\);/, '// 数组重排函数已移除');
    deobfuscated = deobfuscated.replace(/const\s+_0x[a-f0-9]+\s*=\s*function\s*\([^)]*\)\s*\{[\s\S]*?\};/, '// 解码函数已移除');
    
    // 保存结果
    fs.writeFileSync('main.deobfuscated.js', deobfuscated, 'utf8');
    console.log('解混淆完成，结果已保存到 main.deobfuscated.js');
    
} catch (e) {
    console.log('解混淆失败:', e.message);
    console.log(e.stack);
}
