// 直接从 main.min.js 中提取并执行解码逻辑
const fs = require('fs');

// 读取文件内容
const content = fs.readFileSync('main.min.js', 'utf8');

// 创建一个模拟的全局环境
global.window = {};
global.document = {};

// 模拟 atob 函数
global.atob = function(str) {
    return Buffer.from(str, 'base64').toString('binary');
};

// 模拟 Env 函数
global.Env = function(name) {
    return {
        name: name,
        log: console.log,
        getdata: () => null,
        getjson: () => ({}),
        setdata: () => {},
        setjson: () => {},
        wait: (ms) => new Promise(resolve => setTimeout(resolve, ms))
    };
};

// 模拟 $ 对象
global.$ = new Env('test');

try {
    // 直接执行文件的前89行（包含所有必要的解码逻辑）
    const lines = content.split('\n');
    const codeToExecute = lines.slice(0, 89).join('\n');
    
    console.log('正在执行解码逻辑...');
    
    // 执行代码
    eval(codeToExecute);
    
    // 现在 _0x4f02 应该可用了
    if (typeof _0x4f02 === 'function') {
        console.log('✅ 解码函数创建成功！');
        
        // 测试解码
        const testCalls = [
            ['0x1b0', 'NY7O'],  // 目标调用
            ['0x49', 'RmO]'],   // require
            ['0x14', 'v1Rp'],   // path
            ['0x87', '^jVw'],   // util
            ['0x10', '^jVw'],   // HTML字符串
            ['0x102', 'gdIQ'],  // URL
            ['0x2d', 'pyqW'],   // window
            ['0x15d', 'frUB'],  // document
            ['0x28', 'i*IW'],   // window
            ['0x33', '$uwW']    // document
        ];
        
        console.log('\n=== 解码结果 ===');
        for (const [index, key] of testCalls) {
            try {
                const result = _0x4f02(index, key);
                console.log(`_0x4f02('${index}', '${key}') = "${result}"`);
            } catch (e) {
                console.log(`_0x4f02('${index}', '${key}') = 解码失败: ${e.message}`);
            }
        }
        
        // 重点关注目标调用
        console.log('\n=== 重点关注 ===');
        try {
            const targetResult = _0x4f02('0x1b0', 'NY7O');
            console.log(`_0x4f02('0x1b0', 'NY7O') = "${targetResult}"`);
            
            if (targetResult === 'log') {
                console.log('✅ 确认：_0x4f02(\'0x1b0\', \'NY7O\') 确实返回 "log"');
            } else {
                console.log(`❌ 意外结果：期望 "log"，实际得到 "${targetResult}"`);
            }
        } catch (e) {
            console.log('❌ 解码失败:', e.message);
        }
        
        // 额外测试一些可能相关的调用
        console.log('\n=== 额外测试 ===');
        const extraTests = [
            ['0x137', 'r1T%'],  // 可能是 log
            ['0x146', 'ZKy*'],  // 可能是 log
            ['0x1b0', 'NY7O'],  // 再次确认
            ['0x64', 'mTqS'],   // 测试
            ['0x108', '!^Fw'],  // 测试
            ['0x63', 'TUdS'],   // 测试
            ['0xa5', 'Z9)i'],   // 测试
            ['0x162', 'UWQF'],  // 测试
            ['0x127', 'cb!D'],  // 测试
            ['0x126', ')EJa']   // 测试
        ];
        
        for (const [index, key] of extraTests) {
            try {
                const result = _0x4f02(index, key);
                console.log(`_0x4f02('${index}', '${key}') = "${result}"`);
            } catch (e) {
                console.log(`_0x4f02('${index}', '${key}') = 解码失败: ${e.message}`);
            }
        }
        
    } else {
        console.log('❌ 解码函数创建失败，类型:', typeof _0x4f02);
    }
    
} catch (e) {
    console.log('❌ 执行失败:', e.message);
    console.log(e.stack);
}
