const { SmashUtils } = require('./SmashUtils.js');

const url = 'https://h5static.m.jd.com/mall/active/3aEzDU3fpqYYtnNTFPAkyY3tRY8Y/index.html';
const cookie = 'pt_key=AAJoLw9lADBxs4e1jDG4PoJ83ChDoVQCn7DgX-Q0uEnN4gMgcKBZS2L8kdBbcsrGqeuq0KH0xrM; pt_pin=jd_dlsyruocXPmz;';
const ua = "Mozilla/5.0 (iPhone; CPU iPhone OS 14_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.49(0x1800312d) NetType/4G Language/zh_CN";

(async () => {
  const sm = new SmashUtils(url, cookie, ua);
  await sm.init({
    appid: "babel_zeEzM8yPWTLWju6YSj1aoNSvPfQ",
    sceneid: "babel_zeEzM8yPWTLWju6YSj1aoNSvPfQ",
    uid: "4f3a81b78bd21600d3a287edb48d425d"
  });
  
  const random = await sm.getRandom(8);
  console.log(random)
  
  const log = await sm.get_risk_result({
    id: "receiveButtonId",
    data: {
      random: random
    }
  });
  console.log(log);
})();