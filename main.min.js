
_0xod2 = 'jsjiami.com.v6';
const $ = new Env('领取优惠券');
const nowVersion = "20250526";
console.log("当前版本" + nowVersion);

const _0x2dc1 = ['f8Kewq18fcK7w5w=', 'DsKvIMKyBXw=', 'WsKzwqLDnG5Bw69s', 'wqHCp8OC6K6Q5rKV5aWg6LWn776m6K6f5qGr5p2R57yF57qJ6YW96K2k', 'w4rDqAjCjBTDlHEe', '5bys5YuQ5paP6ZSr5q+b5rGw5p2T5L2J5oG15Ym46ZyT6KWK6aCK5Yyo772q', 'wo3DsjEJw5LCqTvDtsOfw6Y=', 'E8OkwoI=', 'wr3DmEnDmg==', 'UcK5wrHDkXNG', 'woTCtcKh', 'fUhJHTVRw4DChDA=', 'w5TDtsK9YsKge8KUVsKlFMKsS8K0w6DDk8KWw6TDph1QLsK+GGMFEcOMw44cWWHCn0fDncO2UWcGwovDrsOww6rDiMKjw5jDv37CpsOaT14zWcOGWCF4OMOEw77DpcKawp3DlMKgw5Usc0NqS8Kxwp/ClR3Cq8KUCVvDhQw9H8KYwqQ8w6Q2w5zDp2kKwrwHwpvDsSxcw5rDlWQCR8O+J8OswrRWwonDncKGIlLCqMORHMOpw5E4bcOew6/CgMKV', 'RTfDrlpT', 'wr9Iw7s=', 'YQsT', 'w6bCucOa', 'AMKXGhPCi8Kz', 'DSDDgCwv', 'w5XDiUTCghA=', 'w6ljHcKPQyoGBcOL', 'wqvDsCc=', 'DjHDnjY+', 'wqbCgsOB', 'dMKAwrx7', 'b8KEwrtoWcOv', 'wqrCrsOiw64M', 'wrsJwqfCocOxCiPDsQ==', 'CU9uwqrohJDmnarovJPoo7Dmir7lirvvvrTorKLkuqLopI/lh6Xpl7fnqaflj43CsHXCn8Ol', 'wpzDhcKYw6XDoQ==', 'JsOWwpE=', 'wpAEVXrCssKxChrCgMOEasKOw610HMOGwpg=', 'wqDCicOHIsOrb8OELjYUdcOqTMOyK8O6w7wawq4OwoZJA0c2wrQswqVkw5FkJMKmw7XDrjvDrcObwq52OcO8wqMIdcOZw5glwonCicOswr1gw4LCpMKuccOFPUDCnwbCg8KVwopbOhw8w4R8wo1fUxHClFPCn1PChEoJwp0tw6PDgDzDu1FEwo3DkMOqfg3DihLCsG7DkMO1PMORw6I2HsORw7kfQMOnw4HCgcK8wpjDlB9BTEjCjSY7a8Ozw6HChULDv13DlQ5RakNHGcK6wplFZS9Zw7E2w5nCm8Kkw6nDmWASwoAnwpI9w7TDtsKxw6jCg8OwFsOmN3XCrANHNEvCsCM4wroEPkzCtsKwwqHClcKXYjdRw7EIwqcmw7lZR8KuwovCtjoKw6jCrsOcwo4NPDDDiMKhEcKEw7/DthXCvBnDo8KtRMKJwqjDtMOIOlQFNcKew47CgQ==', 'wpsRWw==', 'VA0ww7M=', 'UcK5wrE=', 'GTXDmBY+w60NQUXCoA==', 'aEhUBig=', 'wocLT2I=', 'w4DCksOpw4k2ezNf', '44KR6LSm5Yyp', 'wpTCtcK3wqpXw7zCgsOT', 'VUlQw7HDtsOvIFM=', 'c8Kewq1dZMKAw58=', 'wpgPK8OkPUNE', 'BcOuBQ==', 'M8OQw6c8GcKywqbChmQv', 'QMKQCRPCksOyw7fDmcOCSF1q', 'KMORK+itk+axmOWkk+i0ne+9tuitpOajm+adh+e+sOe5jemHm+itjg==', 'UjPDqHpTVsOUOnLDkw==', 'VcOjwqXDoA==', 'UlJxw6zDqsOhJ1LCkg==', 'JMKGw4k7w5A=', 'M2dNOsKgw7E=', 'V8OwdMOt', 'C8KvOlhs', '5Lim5bCIw79CDA==', 'U8O6fsODUDU=', 'TkhfGS5Kw7PCpw==', 'wqTCm8OPw4RsYzBlLgU=', 'KMKAw7c7', 'VMO1Ow==', 'w5InwqXDk8OoWsO4', 'wqzjg4Dot6XljJA=', 'OxvClnRMw6bDt3oU', 'wrjCgcO3w6AB', 'JB/Cp3dZw7fDuw==', 'wrDDhsKbwrfDl8OkwrjDk8KgwpF9J8KKSQ==', 'w5fCoMKVwqZsECQ=', 'MEPCicKnwq3Dk8OPwqUtHQ==', 'worCqE/Ciz/DmjcRfA==', 'wr3CksOPw4d0DSViJgRywqTCvVnCmcOFA8KawrDCuVkdwpEDw7XDhSXCocKUAsOgw5TDn17CsyTDrywawoDDjSjDpcKBwpoodVLCmBIfwrrClsOFFsK9P13DqF0kfVsrMijCt8OPw5oOwohwS0FrbMOTGz5jw7nDsyZdwrbDqWAUXcKFW8KOwqbDlcKJFcO4acO+wrvDpMKuwoQ7wp9PVsKiIEPDjh1dcSLDhxFQNSDDgMK8ZWNCw6PChcKTWcKEw4UxaH9+U8OGwrfCjj/DiFTCicKUTj7Cizo1VUlZUMKjO1jCiCjCo8KbaHJ3e8KxSkTDjUBuWm7ChmNWRwVywqALw40fw5/CkcONSMKxwp1wcMO0MC7CnsKoXcKfw5PCnsKAXyfCrMKXw5bCpcK4YjpWw5otTR3DksKvEMOcF2/Cn8KJOVMd', 'wqLDuiQuw6DClR8=', 'wojDvsK6wo3Dt8OPwqnDqMKZwro=', 'w502wqXDp8O6FMKyw6/DjMKzw5HCvzh7McOhBMKXw4rDqMKzRw==', 'wqbDtMORw488PEMa', 'w7UuAkrDpjHClcO0cA==', '6K6+5Y+m542D5aGW5Yyq6YW95oq45Ymf772Z', 'w5nCq8KFwpd9Mic=', 'w4Jxw4XCg8KCwoAqdyDDmDEww6VZw4DCqC5i', 'LzFO', 'GMO4DcKSAcK/Ljk=', 'wqHDl8KZwqHDnA==', 'w6UjwqPDtsOkXcOOwqnDmMKpwrPCvCVw', '5r6c5reT56KT5ban6Ly45pyN7726', 'M21E', 'w7Fvwq0U', 'TsKjwrTDl25Qw7g=', 'T8O7NU0=', 'w77CgwrCiQ==', 'w6x2EcOSUXdMF8KILsO9w4Ajwq5ZwopZEMOWwplq', 'wrADwrQ=', '5p+L6YS+576q6IWw5ayN5LmpeMKhBu+8lA==', '5r2B5rS756Of5baS6L6Y5p2x772v', 'T8OePcOQJw==', 'wqXCoMOx', 'wqzDj0k=', 'wpnCtMKiwpdIwqnCl8OVwplwM8OdwonDuMOrw5oRMcONw4lKcsK0EcKjwqZFw6/CkMKyw4Rsw57CrsK5w6PDihTChD4owpVDwovDtTUXwpcGWXDCuUTCpsK3w5bCr8ORecOpFW7CncKWwqp/wpddHcK5RmHDl8Khw4BFwr3Dk8O9ZTAcdcOHwr0Iw4Z8FcKCO3zCrcKpVHvDqyLCp0BVTMKjdUQcNcKBA8KeZk11OMK5bmvCtsOMdH/DjsOQwqJiwqLDm8Obw7rDqcKAwoF1J8Kqwo0xwqvChsK3BkjCl8OGICBZwpJVwrNUw4/CtMOhw59JwpMBw4rDnzLCjcOpw4bDtD0Fwp/DjGdrSMKiwpbDgSxWTMO0w6oYw6JSw5DDjhkEf03ChMO3T8Kmw4DDmUfDlS/DtMKZw6xzPsOJwqTDu8OzaTs8OGnDrMKEFTvDucO/RsOt', 'w4U3wqLDvw==', 'wpjDv8KfwrA=', 'e8KRwqB3JeWEieaXiuaKouijr+Wngui3gsOz', 'NsKMw7Mlwo8pEsOCw5DDh0hoemrDjcKCwrZvY8KRfFvComAHKsKfAnPCkMOmw51xw5rChsOAwpPDpMK2NMKOw6NTFcOAw6vCrmPCpDbCjVzCpg0rw4Naw4cFSSwzG8Oow4vDr8K4bh5AwqzCuXrChX4edXvCosOmV8ObTcKzLMKuw5DDritAbcOlwqHDnB5uw5pJwqxww5XChwnCvsKPwp8Vw4IITFzDswDCgMOiw64gw6VYw5YOw4HDksK3dcKyRE8hw4MuKMOQacOmwpwYw5DCmWEodHR1woLCl8Ofw4MBLcKBe8KbwpbCqsKDwpXDqWvDvcOHwqkn', 'wrAbGgXCvQ==', 'fkpKAC4=', 'wpXCvMKswohK', 'wrvDuMKNwo5r', 'w7DDk8KbVcKwCsKKARkhWk8MRE8=', 'w43DmsKmSuW/l+Wmmui+kuijpuiFsuaeh8KZeXjCnQ==', 'WMKkwqTDu3M=', 'bcKOwqI=', 'w7VMBMOPRw==', '44KR5o+Q56ek44Gx6K6o5YSm5raV5YqZFx/CmsK6LCzCu8K4cg==', 'wpJMwqHCkcOUw7PCilEww7NEwqxKwqfDvcKOMA==', 'ZMKEwqFCQsOpw6TDmQ==', 'Z15HGSoew5DCpD3DqQhqwocsO8KNw7jDhMK5FcOiSsOjE37DnMOJwpsqGMOpwp7DlgAiOxthZQTDlcOdAU9Vw6PCtAg1csKKDsK/SXrCucOQVcOBRcOYw406w4dXw5l6bcODcCXCgsOFw5N9w61Kw63ChV7CoD5VaAQ7BRsxI8KiBnDDlcKyAsK8bn7DsSDDnlZcwpTDnmLDn8KDFQHCkcOPf33ChcOuw43CpMK2w7gsw77CoMOBwofDoMK2wq99wrnDpn7CvWfCjBzDi1kcCxrCmRrCukbDug3Dv8KdXsKTSiJacTjCqXI/IzrCnw==', 'wocVLg==', 'WTnDuw==', 'wpDnu6LnuZzvv6k=', 'wr7DmFjDgcKRDA==', 'K8OHw6bDlEc=', '5Ym56L6O6IeR5ayx5LqrM8OPwpDCkA==', 'C8KXADDCnsKvaw==', 'fSsTw6g=', 'N8Oc5Yag5LmC5pe36ZeA6aCb5Y+J5oiW5Yq66LSX6L2g6LWv5Y6t', 'JDtdwo/Dg8OnwpRiaXU=', 'LsKaUQZkw5Q=', 'U8KQCAk=', '44OZ5oyf56as44Kc6K6T5YaT5aK65YuOKU/otIflj73kuZ7DukkCwrIhw7g=', 'wozDu8KJw4HDoA==', 'woDCoMKvwo5M', 'w6fDg8KccA==', 'wpvDssK0wpHDtsOPwrXDs8KJ', 'TUlk', 'EMKvw6wHw7l6wq8=', 'wpLCvcKEwqFXMCbCu8KaGw==', '5p6A5Yi15LmK5YmS5oqW6KO25pez6Zas772b6LWc6L6B5om56KODE8OAw6VvIRg=', 'PMOvAMKvGsK+EAI=', 'wqvDnsOGwpHDt8KswoTDksO3woUTbsOKExXCoQbCmjsFwqzDmmM=', 'LFMSZcORwqlQ', 'w6TCo8OQw4I+bA==', 'UGhiw6jDoQ==', 'wp0aXXrDrsKqBy/CjsOHZcKfwrNpDMKKw5t3w5TDt3M9NHzClMO/PyPCtsOlw63DhMKfMBvDnxYLw67Cs8OxFcOJWMKOZWTCjMKuLGhuDVLDvMKgwozDrzJNwpXCjRcSbcK+D8KWAjPDgsKawqAgwqvDoW5nw7DDtTvDosOsZcKIw5E+BGxJHsOKwotVUMKDwo7DkF5cwoITeGHDqcObOcKMwo7Cr3LDo1HCnMOaw7PDnFMnScOTIcKDLcKUw4IfBHhvwqJoR8KwwoPDrgoNw4rDgwtLehAnw7PDkn9UwpzDqUUSwqHCpcKzw6/DrnLCuko=', 'eV9VHQ==', '5ZGy56Wt772j', 'wqLDuiQnw6jCjw/CsMKPwqM=', 'O8KNw6YdwpBnCcOh', 'w7NNw6DCrMK/w7VSCgLDoQ==', 'wqnDsDc=', 'UcKzwrjDs3Vc', 'w6LCosOJw5AoJFYXw4/Dti/DucKCUsK/O2xBD3NhwqHCmBHDg8KMKMKrw6wgGcOtbcK/woQYwo80w5V2wpx9wpHDtMOewppVOMO9ZMKaDDbCu31+w5NVw4/DrjFBw49ZXmwUwqcYWMK0w5zChRg2FC0f', 'OSDClWpd', 'w4w7wqjDrsKkY8OQw63Dm8Kj', 'wodQwrbCj8Otw5Y=', 'QMKKFQLClsOo', 'ccKVwr1dZMKAw58=', 'RkN3w4HDpcOyKw==', 'ZMKEwqFLTMOzw7U=', 'w6DCmMOb', 'wosXwqXDqsOnG1DCoSd6HynDgVXCvDjDhV7Dplozw4UHO2Nrw7gXVcO1ASx7GyfDgkkIwqXDjFBNCQfCrMKfw79dJVbDqXYOfsOEe8K9AWPCh8KwDMOFw4pUf0NMRcKWXsKOw5V2TDJ8bcOvwovCnnJvbXoxw51KwovCmMOow4dBw6sHwqjDmMK9wpYwXDppwroTKsKQXsKdWG0OwqXDmcKlJcO5wrBRCcOEZcOnWsK8DErCsHNWw5ZkFXIDw6vDuQnCtScKJsKTwpRwI8KLw5nCsxLCpwnDoMKNwpHDowzCtUvCjVVHEA==', 'wpzDlwE1w4DCsTM=', 'W8O1Mlo9wrk=', 'w6/Cp8Kjw74Qw6Y=', 'wq/DuzEaw7HDmhPClMKCwr/Cr3gfG8KQGjMZSBM0wrbCriMsfMKOw4TDlR7Ch8K8w5JTEXjDvcK8S8OPdcOFwqLDr1TDgQNyPCMWDUHCmy0xw4YuAMKDwpFYwqvDkhLCjsK+wqPDlnVnbzlATcKVLXTDk8KIETRnwpUYK8OhBcKkw7RSDl1vw5R4FMOdw7XCuMO4w5zDgcKken/CozhyHGUHOMOeOUZnw4XDrELDqcKGGhFYwoJXw6oSwpXCpMKROMKywrjDr8Kgw6jDtg12csO3OsKUw7czwpJjN0zDhQDCoAwcQcO8wpjCn8Oj', 'wpjCtcK6wpQ=', 'wp3DqsKLw7vDoQ==', '5ouB5Yip6aGt5YyE55i955ao5ouJ5p6z77+W', 'DcKQFxDCmsK9aVLCn8OKwqzCjn8ZTSAAw5XDpTZhUQrCoiAZdMOTw4vCi8ORMMOrZDnCjwjDuE9Jw5DDmBTDlVAnJcOGF8OFwrwO', 'wpPCh8OnDsKj', 'wrbDk8KfwoHDnMOjwpHDlMKowoc=', 'I8KDw5wow4gRwo/CvcOYwpdUBlIXwpADU8OyFMKcw6TDsgXDvMOowoLCu1Bpw7LDhsKpwrMDPsKkwrbDpWHCscKuw75KXFVAHSbDisKNwpLDgsOGYGHDkwXDtsOjLMKObW1LNlPCu8KlJwIzVk0aRcK6DMO/c8KPSDtjSArCgy7DpMK2woHDvMOBwq7Cn0tyVhvDp0LCqV/CnMOBHXsXOMKlIsOTZsKIEVHCtXrDq3LCmh7CtjARcibCisOQPcOgE8K/wqcRKsOpwoFdwrDCj3tHw5fCtx7DrUIBwrQyVcKUw5MgaAbChMK6wpXDs8Obw5oP', 'WcK5wrXDoWxRw7Nr', 'WMOwdw==', 'w61sAcOHWgIF', 'wqrDiE7DtMKQEmNuwrvCsW0=', 'HyDDnAw/', 'esKfwq4=', 'VsKVwrM3', 'w7sqCEDDtRY=', 'wqrCs8OfDsK6NAjDjQ==', 'ecKJw7hMY8Krw6rDmWdnwqjDrsOkcQ==', 'worCuMKywqZow7vCvsOawo5RKcOXwo7CpsKpwpk=', '6Ky05YqO5bSZ5pSy5oqv6IKF5pSe6Lac5Yy76Z+06Kav6K6p5rKm77yr', 'UMOuKEkvw7fDjMKKKhgXwqQmwpsLG05VAx8u', 'Txgaw7F7wojCnsOd', 'wqjCo8OGK8KUMCzDnAnDmMOxODDDncOUw503', 'XsO7ccO4RXfDnsKMUy3CvknDlANQw4kFAnJlw67DucOVesKmRcKcZGROAcKGcUEYVBR+OcOqHsK4I33DjMKowp9rUA7ChxMrw7s6wr/CgsKQIMKTdMKlwohIw6PCjHEqwqV0wqbCokgiS8OTwoEPwqLCkcKIw4JGwrzCryvDjcOfw5fCoU1uwo7DnGpWwprCv8OGwqXCighdUEzDkR4/UcOlFMKewqZYwpg3T8KOfsKVwq8Kw7XDowhgwrHCncOlw44bw5RZB8K4DBBuw67Cm8OywrrCrzPCgyx/XT8JZHTCm1AvfDjDn2BDw4p0w6jDpcOTw7vDhsOuwo7DvMOmw78iw7slVGcUwrdew6Jtw6DClQ7DsMKLwofCpsO6w4bCqTRKw4wAw50PwoldKcO7amXCtcKXw4vDl8OJwqDDoBZrDHfCmxBSdG8iw4zCgFIpeMKz', 'NMOYw60=', 'cMKcwqZ2dw==', 'w4/CusOYw40+cA0=', 'w41gw5/CuMKUw4M=', '5b6K5aSd5oua6KOs6LW95Y+p', 'w41gw4XCt8KQw4M=', 'w6EtF2LDhAvCpMO7YMOJCSHDlMK3IMKj', 'w493w4PCnMKD', 'UwMqw7M=', 'SA0k', 'LMOtB8OJf0xM', 'wojChcOaw5w=', 'wpPCgsOpJMK4NSc=', 'O8KNw6YTwop+F8OLw53DiVQ=', 'PAHCp3NKw73DsHM=', 'EDXDmwc6w6wHQ2DCpEHCvSbDvQPCtcOVE2HDuQgtOg==', 'Vkdqw7E=', 'JDtdworDhcO8wpNl', 'wosXwqXDqsOnG1jCnytnHiXCnl/CvSbDmkHDuFE5w4kHO3Y1wqYNR8OuBCgmB2HDk0lVwoHCmGdLHwLCocOcwqtcIFjDsT92WsOCe8KtQnjDpMKhN8KBw5FrfkgCGMKYIMOzw4V8WFx7bcObw4LCt2ImTH99wrJVwpXCicKew59WwptHw6nCjcO2w7FnCEogwrlKNcKAKsODGS9aw4fCnMOvJcOYwpAzcMObcsKlAMOhSULCvF5hw7BHEHIiw63DsAXDuQVAdMONwr5oN8OlwofCshTCvhbDocOQw5HCh3jDhkDCsmwHTMOWEcKzw5kxaMOrcMKbwrbDpcO1dMOrw4AGwpTCrsKvwrHCgEIHwqV8bmTDtWLCpcOzAFHCjADCucOMJ8Odw5nDpcOHwqBDw5NRwr3CiGQ0wpxtPwXDi3U9w6IOasKAJw==', 'Z15HGSoew5DCpD3DqQhqwocsO8KNw7jDhMK5FcOiTcOjFX7DnMOJwpsqGMOpwp7DlgAiOxthZQTDlcOdAU9Vw6PCtAg1csKKDsK/SXrCucOQVcOBRcOYw406w4dXw5l6bcODcCXCgsOFw5N6w61Mw63ChV7CoD5VaAQ7BRsxI8KiBnDDlcKyAsK8bn7DsSDDnlZcwpTDnmLDn8KDFQHCkcOPf33ChcOuw43CpMK2w7gsw77CoMOBwofDoMK2wq99wrnDpn7CvWfCjBzDi1kcCxrCmRrCukbDug3Dv8KdXsKTSiJacTjCqXI/IzrCnw==', 'KsKQSyFkw5TCsQ==', 'NMOSw6TDmkfDmg==', '5o256ZmR5L+A5oCI5Yqh776k', 'w6fDtVzCnwHDmFMo', 'wopFwqjCjcO3w5rCqFw0w756wrhXwrc=', 'w75qSMOhbGEZUMOAK8Ouwppkw7k=', 'BMKvIA==', 'wrjCjsOmw6Q=', 'w502wqXDp8O6FMKyw6/DjMOpwpLDuztxeMO6AsOU', 'woUbTGbDv8OyCw==', 'PG1NPsK1w60=', 'wqLDuiQ1w7PCiAnCr8K1wqLCpG5RRsOU', 'wq/Cg8OFPcO/MQ==', 'w4jDqEI=', 'wpLlib3jgbY=', 'wqDCgsOPPA==', 'KsKQSzdkw47CsCnCug==', 'cQkaQsO0wqAHw5YOKA==', '4oCC4oOkwqpy6L+C5p+rwps=', 'P8OSw77Du0bDnl1pwrPCicO8', 'PMK0Tww=', 'Qklnw6A=', 'wqTDsyACw6DCgx/CsA==', '6K225aKe5Ym+54+P5aOc5Y626YahYBUKwprCqC88wqPCuHDDn++8vw==', 'w4t1w4HCn8KYw5lkLD7DgCgxw7cQwpnCvDYiUMO3w7fDjDBjBsOobMO6WjIiwrjCig==', '5LmC5q+G5oif5Ymg5pWO6Zee77+8', 'KcO5w6vDkFY=', 'w61lwrAHKwY=', 'w5bDokTCizPDnnUeXMKEw583', 'HcKzBB0=', 'Dx7DjSg+', 'wrDCk8Oaw6RhVStiJgU=', 'wofCksO/L8K4JDDDmQ==', 'MhBIwq/Djw==', 'EcOkwpI=', 'wqgDwoDCmMOsDTnDvg==', 'woIcwqrDucO2VA==', 'wpsbUm3DqsO5', 'wqDDsTM=', 'FcOvwoQ1wpHDvwwdwpNvw7bDmMOVLcO8IQjCpwLCv1fDnMK0wq7DksOZc8KjwqzCo0Qpw4XCgcOLKMO5Owoza8KvPMKLw5rCtcOhNRbDumTCgsK/w6R1woHDpcKXEcKBT0g2ZsOYGjXCncKtw4HDuxIcw7DChVs2wpR7wr3DiQpnw57Dm8KbScOqwq1fw6zDh8OjMsKJS1LDoSQXXcOnwqVXHMKnD080wo0Jw5DDuVDDgHbCv8Oow7leGMOSw6fDksOCBCHDucOTwo0CccORE8K2wrtUwprDrlPCmMKlw7wHS0HDvcK1WRDChGkuOQDCgBzDssKXwoLChXnCl8OUw47Dl1nDuVIMXHAPXsOawobCr0DDksOTE8OfQW5HZTEtw5/DgnPChE5Yw5I+wrFtbMKnw7QUwrDDi3JLwppKwoEwwrIhw5B3w7nCtsO9EMK+DA==', 'ESbCpVh5w4TDlw==', 'w73DksKOaMKpXcKPOAIrV0VZEQzDlcOwwrfDtQ9iw4TCijPDosOqw53CrMOqwrU+UcOnwpvCucOgwoQWwr9WM250wpcuc3PCsB9zAjfDscKCJxfDqSvDsSwNwogfBzh2w68Gwqg4X8OuEFvDl8Knw6hSAMOTwqzDisO2XcK6w54ERzNfJjLDpAPDi8KyTmzCkMKfw64YVcKNJsKewpgvwoxQw4PDnMKiw4vDn0JzdsK/YBdIw6XCvcK5I8Kow4HDosOaw6nDgwDDiTR/wop0w6fCpMKfRzJsYwk7wqTDksKFw6FMPUHCtsOkBMOIccOxYBtb', 'LF7CiMKwworDvcOO', 'b1VCEA==', 'WD/Dsg==', 'w5bDolXCgxTDlHw=', 'F0MI', 'YAUE', 'cS1JOcKNw7FYSD1bcWwEwq3ClSjDiX58', 'VSwiw6pv', 'wpp7wrnChcO8', 'wrzDo1vDn8KA', 'VUlPw6rDs8OjPHfCilYY', 'fAsRw7Jq', 'w7dyCcOLVg==', 'wrvCk8OAw5BwXg==', 'FUZ8HsKbw5ZiQAg=', 'DsOKwpUs', 'wrjCg0vDh8Kb', 'DMOtFcK2Gg==', 'GsOlwoEBwoDCsAg=', 'BcKgw4MKwq1XNsOdw67DrQ==', 'wpnCn8O6JsKHOArDixvDuMOBNzbDlcOCw4A=', 'wqHCu8Oiw70Xw6ELQQBqQn4rw6oIB8K9ZsO+wpfDtCTCukjDjHgbw6ZePcOGP3Y9UcKnw4cqPcO9QX80w7s=', 'DiXDny0=', 'LsKedgthw4XCrA==', 'LWdXKMKmw7cJ', '56+L5ZGP5aeg6LaywrM=', 'WcO+ZMOrXQ==', 'wp3Cv8OuwoRZw7HCnsOe', 'wo0cwqM=', 'PsK1OsKiH2bClcOIHXXDuwrCmhJLw5MBGXZ/w7/DosOOa8OiKg==', 'Qg0xw6prMg==', 'wqE1IQ==', 'RsKLGA==', '5YuL5ZKj5by65aW55Lie5Yuf77yD6K+A5LuT6KWR57uM5pyo5LiT5Yu7772Z', 'w6XDk8KcbcK1EsKiCR4l', 'w5DCocKG', 'wpnCtMKiwpdIwqnCn8OrwpVtMsORw5bDssOqw4QOLsOTw4JAf8KhT8Oow6MJw67Ch8Ktwpw3w4vDvsObw7vDmRvCgWkEw5UMw4zCqXlew7RaGTXDoVfDr8Kmw5DCpMONc8K7UQ3DtMO4wqpbwq4VPMK4VyTCuMOBw4A2w7zDpcKhXi5sUsO/w6dDw5cJRcORKwTDkMK5PGPDsEPCoEYQCcK1YFY7OcKiScOKHREvJsKibGnCuMOXCRfCssOWwqYaw4/Du8KewrHDoMOJwq11ZMKGwod7w6DCpMOxRHfCnsORfHgDwrlLwrNCw4TDp8OXw4dLwpMew5vCukDDqMKPwqHCkSEH', 'akBPGXYFw53CkTPDqgd7w5kxK8OBwrs=', 'wrTDg8Kow5fDlmonw4vDnMKD', 'wrXCnFw=', 'w6Zcw6PDlsKuVA==', 'Z8K+wqh0YA==', 'IBrCgHdLwq7CsTsQcXzDh8O4aCVYZMKQc8K1fsKyAT4=', 'ScK5wr3DsW8=', 'M8OHwo0=', 'wos/DXbDn8KgXAPCp8KZOMKGw4lpBcOYwqtrw5LCsA==', 'OMKpw5w1w50=', 'wrfDhRfDscKrUnVfw6XCoSJHw4Zj', 'w7Vvwo0ULQcAXg==', 'KVXCgsKywobDmg==', 'wofDr8KYw7jDtBQDw5TDosKpHsKGw5Udw6vDnWoNw59dwprDrMK3w4jDssKDCBzCjsO3VlobwpsDPDXDpEDDmsOtTMOowoUqwr9hIsKBR8K6w4zDhcK6wrPDoUF5woDCoMOCQjBNOWrCgsO9wobDj8Ovw5PCmcKiUBYcw5kmwrVlfMO8K8Okw6rCnWLCiMKJw7pSw6jDgsKNw4zCgGjCicKtw7sqwrJkUsOHwoo2w6bDgmbCgcKVLsKuwrHCusO0LWfClsOow7w8TCMdI0bCicOKR1sZE8OJL8KUw7PDoxnDhmnDhnhywpNOYhpMfMO8asOowpLDvMK1YAd+w5/Dnw==', 'P8OAwpFAbsOTw4nDoRk2w73CqsKnJBTDsUPDqk7DpMKKd1Y/wpnCqcOaw43Cq8OFVcKVwoLDgcK5w6jDkQxpFBI=', 'wrbDk8Kfwp/DkMOuwovDjsKpwoc=', 'O8KNw6YBwpZ/Hg==', 'LkxCMMKx', 'wrnCrsOiw6U=', 'L8KEw7s2wpo=', 'w4nDvcKhc8O3XMKG', 'IlXCmMKYwp3DnMOcwqg=', 'wqXCqsO4w6oQwrM=', 'w6hnC8OFViU=', 'w5XDk0zCghA=', 'cMKEwqFlXsOow74=', 'fSoSw5hbCsKcw6/Dp2PDtg==', 'wocfJ8OAJk8=', 'woDDqsKNw6vDrA==', 'Z8Kxwrlw', '6K6k5Y+w54+f5aKl5Y2m6YSC56u+5Y6L5oiD6KCX5LyT5oC+5Yui5LiM772S', 'T8KAFQbCg8O0', 'L1TCjcKlwoLCicOBwpArBsKUExDCh00Mw53DtRROwoMfIA3CvcKwO8Kvw6cfUcOtLcKRw5sCdMOFdsKxeCrCosKUwqXDtSDCr8Kkw4Q1MMKjK8KBw6wuTGcvD0ouAsKzw7zDiy7DkMK4w6HDusOXNsKJwr/ChMK1DzTDgAnCgTTCusK1GMOCCHBzw4QPw5XDnwzChxXCgiLCjyzCvC3DtzUMwp5BwqHDkXhGCSTDqMKpB8K5wpPDlcKPfcKLw6Etw4fDpsKGw7HDpV5FOcOGw7daccKpSGctCkp+w5PCq0h8E8OYMMKQw4cwwoDCnXXDlsKiw6p0Og==', 'bRoTw4hr', 'wozCksOlAMKjOQ==', 'w6swBGDDuwbCiQ==', 'wr3CoMOFw7kWwrJKCQ==', 'd8KOwoZ7X8Ouw77Dlg==', 'wqbDn8KFwrbDlsO3', 'w4vCssKiw55a', '4oGr4oKfwoYu6Lym5p+bTA==', 'Ox7CmG5M', 'wqDDpzkZw7XCkinCvcKEwrM=', 'G8KbGhDCkMKs', 'XsO7ccO4RXfDlsKyXzDCv0XCiwlRw5caHWxuw6TDvMOKcsOzTsKNZ3xTGMOCKV8XW0YIG8O/DcK9I3DCgsKywoR1QAbDoxMVw6Ytw6rDh8OqbsK0VsKfw4FFwpPDlS9/w6AGwozClQV6X8OHwoEfw67DjcKkwockwoTCpyTCicK/w5bDikR2woDCryoWw47DqMKnw67DmHMGH0PCkFgjX8KVSsOfw78dw6cZZcKRWsKtwqwfwqrCvU02wqfDusKow6Y4w689a8OZQx5uw6nCm8O9w4zDvxXDmXduRhoZZnfCnU01GUjCohg0w4Izwqs=', 'w47DvMKy', 'wqzDsTQPw7nCrhw=', 'MsOYw6PDkw==', '5q+r6Kya5rOkeMKxfg==', 'IV/Cj8Kgwp/Dl8OGwrQ=', 'U8O6ZMOLWiLDi8KHTys=', 'wo4IT8KICOW9jeWmqOmivuWLg+OBig==', 'w5/CvsKBwqpwYjLCssOdN8OQLcKZNcOfLTAfZA==', 'wrPCl1rDm8KSScO+wrfDjsOZRQzDusKpBcK7w7I+WSUnUsOTw6dSw53CigMqw6vDqzfCgVnDmMKWwqbDl0rCqip1w4DDoMKNwoZUw7tDwpzDrcKrw7A+IsOzHsKUWMOmckMEMwp4BhHDmsONEcKHUMK3wrUnYcKPw4zDsR9mBELCj2EOOsO6wobDusK0wpwGwq/DhWcywqzCnUTDsSQlJCNLwpgSw4wVNcKeZinDlmtjThrCsA43wqPDkwJdw7TDnSvDmHpEw4vCpsK9w6/DlHnDnWLDrSJmYMKZCsKkFVpxwpx9wrDCjMO2w7wcw70mRcOaaMO+wq4Rw7bDinQ3NcOeMcKVLHfCksOTMEoBw45cacKmw7rDulwZFkPDnFjCg3oBf8KNCx/Cvjh8wq9/HBB9wrjCpQNlw6XDl3zCkwJ1aTQhw7TDhMORw61xwpQ0', 'MkcI', 'X8O/KHQ1wqHCj8OMKREBwrhmwpJW', 'wqDCnsKb', 'P8OSw77DsFzDnEVY', 'w7BtNsOWUCQNXw==', '6I+/5Y64wr5D5peO6ZWg5aav6LSxSQ==', 'wqDDtsKXwoBxPlMYwoQ=', 'XsO7ccO4RXfDnsKMUy3CvknDlANQw4kFAnJlw67DucOUesOzTsKNZ3xTGMOCKV8XW0YIG8O/DcK9I3DCgsKywoR1QAbDozYsw6A3w7zCmcOxD8KZYsK4wo5FwqfCnXEhwr4Gwo7DtBV7XcOSwoMPw4HChMKNwpdtwqXComjDuMKgwrTCqzJuwpnDnm1XwpPCo8OAwrnCigNPHBrCj0hXAcOUCMKLwp1Ywq0ZRMKxOMOUwrMIw6jDpxBzwq/DtsKFw5Eew4w4a8O4RRdiwqXCucK3wp7CoT/DgWMAGBsff2jCnBB1fTzDkRMLw7tzw7fDr8KRwqnDnsO3wonDvMOlw6knw7Y9UHsVwqUjwpNxw7PCtiPDtcKXwpXCp8KwwoLCqDBEwqpiw5piw6pGLMO9bGDCpMOrwoTDuMOPwq7DrFpdTULCmwRaKTM+w47CnVYx', 'wpISOMOmAk5pw5UZw4QhI8KPw5fDjcOc', 'RcORccOlUA==', '44Ck6La55Y+n', 'w6tkwr8QL1UPVzkpwqrCkAdYw4HDgRnCoBAwXcOUwr3DiTB+w7jChkpewoAON8OPwoN9UcKkw4A0d8OWM0PDjD5kC8OrccKZwrfDhDjCgMOidcObwrzDqcOew5HDp8KlwqUXWMOiw57Dp2oUM8OEwoYAJT85NcO/w758w5zCr8O4U3bCmDrCu8K6YMKoWSnDtcKfwowKw4dGwqrDl2PDksOGOHrCoGgMFMONwqEcUcOAN8OJOMO7w6oIw6hZQXTCtwzDlDlKw4nCs8KyM8KRw7nDmcK8wqdnEURNw7V0TcKxwqbCpVPDhsKow4NRwrvDjANqLsK4w6TDkcOyw7teSFXDo8OpUCHCqcK7EcKhw5kEEj1mw5/CsRtLwqMmw7HCmHUVcivDoMOmQzrCm8OPRizDqCcYwrzCpcK5fsOsPMKiw4BQTsODQxY5wqvCrz1Bw6nDjT7CnA7ClMOIwrfDkcO9KmcZfVcWTcKBw7HDpw==', 'bEpWNg==', 'PsKQSw92w4/Cug==', 'woIcwrbDqQ==', 'w5bDu8KuwqJjPinCu8O3M8O3LsKcNg==', 'K0kjUsOMwqRbwp8=', 'WsOwdMOtGC/DjcKNWQ==', 'E8OuwosiwpXCrA==', 'al9SDTtRw5g=', 'w7JpwqQF', 'ccKVwr1RasKBw4jCtg==', 'w6zCnsKwwozCn8Odw5TCk8Okw5JSesON', '6Kyb5Yyh54yk5aOk5YyP6Yae5o+Q6ZuO55us5L6z5oGU5Yms5Lmv776q', 'w7IhB1PDpFnCjcO0d8OoEyvDk8OpYsOgwrlbw6Bcw4DDrnbClg7DiGsFTF/Dmm8lwqfDtWZdwqR5w5zDqsO2PDgdw4jDocOTwpcJwpEgUh/DigNawoEcwoE/w5RTR8O/wo3DgsKoHmDCvcKHXzTDrzPDrxxUwozCrcOmwqLCmcOqw59kZMOQw7DDtkB+wo9udsKtwozDggoFwox0wojDoHrCvsOXfcOJQwDCmcOZw6TDlAfDoxHChSd/wpQkw5/Cp8Kdw7jCuWFgw7TCowzDvkBtw4rDhxzCuCFmw5HCmCFrwqDCm8OwwqXDsMOKecOpwrxEwpbDosOFdMO+w6o4w414dMOQFsOyAkw/wr7DqHtAw5hcQMOww7XDpMK/wqhVfFfCtzxCRMKxw4MLwrEkYMKgRVDCsyHDl8Oow79gw47ChMOnQsObeEbDjWRFacOsw6QbwpEzw6U=', 'K21IOMK6', 'w6djBsOWVz5NUsKfdMKww4Un', 'w6PDocK8ZsO7fMKqaA==', 'PlYASsOXwq5UwowNNGXDscKwRSd5', 'dcKB6LSW6L6C6LeH5Yyz', 'wphBwqrCgcO3w5nCh1Mh', 'Z8KkwqB0YA==', 'dS0J', 'w4Zqw5Y=', 'wqTCgsOcw55qUQ==', 'w5knwr/DsMO9Rg==', 'wqTCg8OMw4RwRA==', 'fB8Qw6k=', 'w6s1CkrDoA==', '5r6I5rWV56O15be16L+/5p6L776f', 'wpDCh8OHw5I1wpd7PSpMbw==', 'wqjDn0jDncKX', 'wowfPcOqPUlVw5w=', 'wp/Cv8Kk', 'woXChcO5CMKl', 'LHdBLsKgw6s=', 'w6TDn8KIdg==', 'GMO4DcKXAcKkKCI=', '5Ye15qOC5rSu5YqS', 'GzrDs04=', 'woLCnsKiwopd', 'csK1wrxiSA==', 'ZFRCDCJqw58=', 'QlNww7HDq8OrCl3CiFE=', 'ScKBGhHCh8Knw6LDo8OPV1cxw6zDvcKEM8Oid8KnYcKiY2jCjQJWw6vDmEvDky9owolhwrRmw6EHeMKaccKfwo3DpVxuSMOzw795LMOvWcK7w6hpwpoVBB1XdMK1w64mPsKFw7PDqsKHwrHClkVOZhHDmHN7w79Qw6rCmX/CksKmw7MXEiHDhxUjZsKwwrYwwofCvBrClzURwrB8w7/DimxYHcKlwoDDi2TDvMKfbFHCo206cMOhWMOKaS8xMsK2w7QMT8O4WsO5w7ZEwrN5w49Iw5XDjH7DjsKYw4V6d8OIZsOOw5NBbsKswo/CskPDuMKYYsKTAGUMwoTDicKbRm7CjFXDjMOKKcOUw77Cq8O4w5fDkytoPw9ORsOWwqIufhvCvsKpworDjsKaw5cGwojCug1nbsOtX8OKDcK/wojCmBJeEHtyJcOAw6lWEk3DiUbDrCHCuA==', 'wr7CmMOKw5J8eSI=', 'TEdqw6vCpMOgO1rCiFEUwpU4w4JpZgbCuMOCCg==', 'wp0aXXrDrsKqDxHCgsOaZMKTw6xjDcKUw4Row4rDvHk4KnTDgcO0LiDCrsO4w7TCgMOHbRrCjWApw7vCoMO0FcOEFsKUfnrCnMKmbXRvDEnDocKgwozDrQx8w4fCiy4eIsOhWsKNbSzCu8Oqwrk+w5TDsTIuw5nDpXLDg8OpKcOgw4ssc3EpcsOSwroIfcKqw6nChXAsw4sQIX7DucKvZ8ONw4zDuxDCphvCnMO7w5PCvio4XsKRe8OeaMKcw44yM15MwqdoZsK2worDokYvwoDCkVVhYgRJwq3Dk3lNwoPDqBhSw4XDkcOAw6TDkUvDuhZBAQrDtGnDv1PDkHMMYjrCtRnCpg1iwqoHCcKmYnFfw7k4ZBbCkhjCu3LDlV8Zwq/CjiBIw6dmwolPw6UzwpbDgcKWWxpIKcKhwojDs8Ogw6HDr8O8wqHDu8KG', 'w4nmlKbplLfmn43liIjnubXnuJvvvqE=', 'LFMSVcOKwr8=', 'BsKWFQTCj8OgZ2rCnsOPwqnChylGEn5AwonCpnklFFPDqWINUMOlw7/CoMOmHcKDWxnCoyrCj09ow6XDoSnDq2JcRcK+cMK9w409RCcVw6k+M8Kuw6JcwpPDmsK3wrhuwpI8A8KcalYxwoBzVwByDUkQwpBnw4QSQ8O6Rn0tWV/CoTQsX8OLwoIFDAzDg8O3w7V6w6LDt8Krw6t7wpTChQggWVzChsOMwr/DpxLDhGkTw58UwqXCmltvw7vDocOqwrpQw4MaH8OBS8KNwrIAOSfDg2/Co8OUwpFBw4FewqliEVBUA1zCojzDsg==', 'w6Bwwq4MNg0PTTQ0wqvDlgkQwp/Cnw==', 'w73CvMKIwrNgMAjCjQ==', 'w7NhwrAEMAM=', 'CsOuDMOHe1Y=', 'GsOlwoY=', 'wr0cwqPCpcO6', 'VMK4wrLDsXl7w7s=', 'wojot5DljZk=', '6K2P5Yi95bWD5pST5oun5bWI57uW5pyH772W', 'wpLDhMKSwqLDjcOvwrTDqQ==', '5Liq5YiX5oqM6KKe5LqUwq7DkMKU', 'wq0iwrLCgcO7', 'w6htAg==', 'XsK5wrjDsmhTwrN1dUrCiuaUoOS6v+iuoeWMoOWmt+i1sO++vOiusOaig+aduuaUvOS7vOajr+W8s+WSqOatquegm++9lA==', 'w4PDolHCqxTDg3w=', 'w5XDhlXChg==', 'IcKaWA==', 'w7DDk8KbVcKwCMKTHA83', 'dsKVwrxj', 'RMKADwvChMOzw60=', 'w6HCh8OZw6HCjcK1w4jCjcO0w41PPMKHTlzCog/DiSNSw7fCmDYNwoo3w5hEIsK1Y8OtED0UJhzDnmdVw6LDrsKrwr7CszdzQifDsMO0TMKDw6TDrMOUw4DCkMOtw6JFw6zDo1M=', 'a1VUBDtR', 'woHCqsOu', '5Yil5ZOr5oi25b2h5aS8776d', 'wrIyGMO4E3do', 'QQwg', '5rmI56qP57ys5ay977yE', 'JTFbwofDi8Oqwok=', 'w58twrjDuQ==', 'TsKjwrTDp3VGw7RxYQ==', 'FDTDjTUrwrUDQUXCoU/CpibChV3DqcKXRyzCvVpzZcKREcOWfMOmX8OEE8KKcyFxw6zDlSbDt8O3LcKHwrLCoMKsR2kMwq/DicK+PcK3IFRjw6fCgB5gwpzCgcKHw4kiOQYYcsOSJMOIw7HDp8OEw4nDusKafcO0wqnDs8OZwpJGw6pfVAJ8Z8Okw50ZPsK7DS1Ewp1YwpnDrWXDqzLDlMKUwqfDsQHDkhrDgybDrXjCkgvCicO+ecKWw4AgVcOiP8Oiw7FAw5HCj2jCq1Ecw7ATH1vDgjvClXnDr0rDrkBYw5bDsHAdU8OEwpXCrVzCpQjChMOjPGHDkH7Dk3klQsKcRMONRMOTMMO6XMKIYQ7CqcKeTsOFSHEgwq9nwrXCpsKjWgTCrsOCw78hw7jDrkVdAMONVUDDjMKofMKedcOQV8Onwr0WwrnCjcKiCsOUw74ow63DgU3DtsKAOiFow5UWw6E6w4Yww7NdOF4q', 'JMKbWwB9w6/Csg==', 'UcO0OFwkwoLChQ==', 'RcOowo7ovoLmn77vv4U=', 'KsKQSzFsw43CsQ==', 'ZB4Xw7F8w53Du8Khw7XDgzXDj8OcWBXCjjHCoA==', 'wp/DqsKXw6zDq0I=', 'wpnCtMKiwpdIwqnCn8OrwpVtMsORw5bDssOqw4QOLsOTw4JAeMKhS8O9wrxXw7TClcK2wpkzwpbDosKdw6rDmUbCpT0zw5Maw4nCpDoKw7VfFy3CqC/DrsKZw5bCrsKYNsODIRvChMOEw5pawpETNsO2fVfDl8Kjw5NYw7zCmsO9aHQpHsObw61Fw5cLd8KSU2LCo8OYFDrCvGfCh1MeJ8KLcRtGYMOjSMONAxU0N8KkFhTDjMKyDnPDhsO3woNdworCt8Kwwr/DpsKCwoU5J8KgwocwwqnChcO7CS/Dh8OxYn0Ow4cJw7IKwo/Du8OQw4Nxwrg/w6fCp0/ClMO2', 'GhZ4wp3DuMOMwqxZWkM=', 'wr7CgsO1JsOpPcOLJw==', '5LqKw7DCqnnDpHrChQ==', 'w6vCpsONw4wi', 'T8KKHA==', 'AcO/w5vDomHDt3x/woDCrQ==', 'w7J1wrwjMAoLdC48', '6KyM5Y6o546T5aKJ5Y2G6YWe5o+86Zib55mi5L6w5oOr5YuC5Lqy772C5Lix5oiG5L626IGc5oqk5p+F55uG5Yix77yb', 'YMKTwrp8XsKqw6PDmChz', 'wpnCpcOnYMKuYg==', 'w7vDmcKI', 'SA0kw4J4NA==', 'KQLCmA==', 'Qwc3w6NrMsKi', 'ccKKwqBpKcOUw57CoMOFwp/ChsK9aMKOwo4GAw==', 'XcOxdMOtTQPDmQ==', 'I8KIw5Q2', 'AhZ+', 'wqHDgl0=', 'woQKTmPDsMO2BxnCnw==', 'wrrCl8Oaw5Rs', 'wolUwrrCjcO1w5bDmw==', 'CcKABhvCjQ==', 'GTXDmBEyw6MH'];
(function(_0x2b9ec8, _0x2dc11e) {
    const _0x4f022d = function(_0x5adcc5) {
        while (--_0x5adcc5) {
            _0x2b9ec8['push'](_0x2b9ec8['shift']());
        }
    };
    _0x4f022d(++_0x2dc11e);
}(_0x2dc1, 0x11b));
const _0x4f02 = function(_0x2b9ec8, _0x2dc11e) {
    _0x2b9ec8 = _0x2b9ec8 - 0x0;
    let _0x4f022d = _0x2dc1[_0x2b9ec8];
    if (_0x4f02['mnlOpv'] === undefined) {
        (function() {
            const _0x14d149 = function() {
                let _0x4da385;
                try {
                    _0x4da385 = Function('return (function() ' + '{}.constructor("return this")( )' + ');')();
                } catch (_0x52acc3) {
                    _0x4da385 = window;
                }
                return _0x4da385;
            };
            const _0x1cda3f = _0x14d149();
            const _0x1b75f7 = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';
            _0x1cda3f['atob'] || (_0x1cda3f['atob'] = function(_0x15ae66) {
                const _0x4da71c = String(_0x15ae66)['replace'](/=+$/, '');
                let _0x1d677a = '';
                for (let _0x26e037 = 0x0, _0x2d789e, _0x56403a, _0x3c7ebe = 0x0; _0x56403a = _0x4da71c['charAt'](_0x3c7ebe++); ~_0x56403a && (_0x2d789e = _0x26e037 % 0x4 ? _0x2d789e * 0x40 + _0x56403a : _0x56403a, _0x26e037++ % 0x4) ? _0x1d677a += String['fromCharCode'](0xff & _0x2d789e >> (-0x2 * _0x26e037 & 0x6)) : 0x0) {
                    _0x56403a = _0x1b75f7['indexOf'](_0x56403a);
                }
                return _0x1d677a;
            });
        }());
        const _0x5127f4 = function(_0x2f93b4, _0x41574a) {
            let _0x2d2a80 = [],
                _0x3f1182 = 0x0,
                _0x253dd8, _0x4329a7 = '',
                _0x516d6d = '';
            _0x2f93b4 = atob(_0x2f93b4);
            for (let _0x46b299 = 0x0, _0x16e6fc = _0x2f93b4['length']; _0x46b299 < _0x16e6fc; _0x46b299++) {
                _0x516d6d += '%' + ('00' + _0x2f93b4['charCodeAt'](_0x46b299)['toString'](0x10))['slice'](-0x2);
            }
            _0x2f93b4 = decodeURIComponent(_0x516d6d);
            let _0x2bbbf0;
            for (_0x2bbbf0 = 0x0; _0x2bbbf0 < 0x100; _0x2bbbf0++) {
                _0x2d2a80[_0x2bbbf0] = _0x2bbbf0;
            }
            for (_0x2bbbf0 = 0x0; _0x2bbbf0 < 0x100; _0x2bbbf0++) {
                _0x3f1182 = (_0x3f1182 + _0x2d2a80[_0x2bbbf0] + _0x41574a['charCodeAt'](_0x2bbbf0 % _0x41574a['length'])) % 0x100;
                _0x253dd8 = _0x2d2a80[_0x2bbbf0];
                _0x2d2a80[_0x2bbbf0] = _0x2d2a80[_0x3f1182];
                _0x2d2a80[_0x3f1182] = _0x253dd8;
            }
            _0x2bbbf0 = 0x0;
            _0x3f1182 = 0x0;
            for (let _0x4eba3c = 0x0; _0x4eba3c < _0x2f93b4['length']; _0x4eba3c++) {
                _0x2bbbf0 = (_0x2bbbf0 + 0x1) % 0x100;
                _0x3f1182 = (_0x3f1182 + _0x2d2a80[_0x2bbbf0]) % 0x100;
                _0x253dd8 = _0x2d2a80[_0x2bbbf0];
                _0x2d2a80[_0x2bbbf0] = _0x2d2a80[_0x3f1182];
                _0x2d2a80[_0x3f1182] = _0x253dd8;
                _0x4329a7 += String['fromCharCode'](_0x2f93b4['charCodeAt'](_0x4eba3c) ^ _0x2d2a80[(_0x2d2a80[_0x2bbbf0] + _0x2d2a80[_0x3f1182]) % 0x100]);
            }
            return _0x4329a7;
        };
        _0x4f02['LROscA'] = _0x5127f4;
        _0x4f02['AVLohL'] = {};
        _0x4f02['mnlOpv'] = !![];
    }
    const _0x5adcc5 = _0x4f02['AVLohL'][_0x2b9ec8];
    if (_0x5adcc5 === undefined) {
        if (_0x4f02['loEaHj'] === undefined) {
            _0x4f02['loEaHj'] = !![];
        }
        _0x4f022d = _0x4f02['LROscA'](_0x4f022d, _0x2dc11e);
        _0x4f02['AVLohL'][_0x2b9ec8] = _0x4f022d;
    } else {
        _0x4f022d = _0x5adcc5;
    }
    return _0x4f022d;
};
setInterval(() => {}, 0x3e8);
const cron = require(_0x4f02('0x49', 'RmO]'));
const fs = require('fs');
const path = require(_0x4f02('0x14', 'v1Rp'));
const util = require(_0x4f02('0x87', '^jVw'));
const {JSDOM} = require('jsdom');
const dom = new JSDOM(_0x4f02('0x10', '^jVw'), {
    'url': _0x4f02('0x102', 'gdIQ')
});
const window = dom[_0x4f02('0x2d', 'pyqW')];
const document = window[_0x4f02('0x15d', 'frUB')];
window[_0x4f02('0x16b', 'mTqS')][_0x4f02('0xbe', '&]*^')][_0x4f02('0x34', 'RmO]')] = function() {
    return {
        'fillRect': () => {},
        'clearRect': () => {},
        'getImageData': () => ({
            'data': []
        }),
        'putImageData': () => {}
    };
};
global[_0x4f02('0x28', 'i*IW')] = window;
global[_0x4f02('0x33', '$uwW')] = document;
global[_0x4f02('0x16f', 'r#Cf')] = window[_0x4f02('0x177', 'lLb0')];
const configPath = path[_0x4f02('0x91', '0*nj')](__dirname, 'config.json');
const smashUtils = require(_0x4f02('0xf9', '$#b@'))[_0x4f02('0xee', 'fbD2')];
// const SmashUtils = require('./SmashUtils.js').SmashUtils;
// const url = 'https://h5static.m.jd.com/mall/active/3aEzDU3fpqYYtnNTFPAkyY3tRY8Y/index.html';
// const _cookie = 'pt_key=AAJoLw9lADBxs4e1jDG4PoJ83ChDoVQCn7DgX-Q0uEnN4gMgcKBZS2L8kdBbcsrGqeuq0KH0xrM; pt_pin=jd_dlsyruocXPmz;';
// const ua = "Mozilla/5.0 (iPhone; CPU iPhone OS 14_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.49(0x1800312d) NetType/4G Language/zh_CN";
// const { SmashUtils } = require('./SmashUtils.js');

// const smashUtils = new SmashUtils(url, _cookie, ua)
// smashUtils.init({
//     appid: "babel_zeEzM8yPWTLWju6YSj1aoNSvPfQ",
//     sceneid: "babel_zeEzM8yPWTLWju6YSj1aoNSvPfQ",
//     uid: "4f3a81b78bd21600d3a287edb48d425d"
// })


const ParamsSignLite = require(_0x4f02('0x192', '*1RL'));
$[_0x4f02('0x7e', 'i*IW')] = require('crypto-js');
let apiList = require('./jdYhqApiList.js')['apiList'];
const USER_AGENTS = [_0x4f02('0xd3', '*Jnq'), _0x4f02('0x155', 'ZKy*'), _0x4f02('0x114', 'TUdS'), 'jdapp;android;10.1.0;10;network/wifi;Mozilla/5.0 (Linux; Android 10; GM1910 Build/QKQ1.190716.003; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/77.0.3865.120 MQQBrowser/6.2 TBS/045230 Mobile Safari/537.36', 'jdapp;android;10.1.0;9;network/wifi;Mozilla/5.0 (Linux; Android 9; 16T Build/PKQ1.190616.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/66.0.3359.126 MQQBrowser/6.2 TBS/044942 Mobile Safari/537.36', _0x4f02('0x118', 'BJZ3'), _0x4f02('0x118', 'BJZ3'), _0x4f02('0x125', '&]*^'), _0x4f02('0x151', 'VDpf'), _0x4f02('0x74', 'pyqW'), 'jdapp;iPhone;10.1.0;13.7;network/wifi;Mozilla/5.0 (iPhone; CPU iPhone OS 13_7 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1', _0x4f02('0x22', '$uwW'), 'jdapp;iPhone;10.1.0;13.3;network/wifi;Mozilla/5.0 (iPhone; CPU iPhone OS 13_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1', _0x4f02('0x9a', 'TUdS'), _0x4f02('0x2e', 'RmO]'), 'jdapp;android;10.1.0;9;network/wifi;Mozilla/5.0 (Linux; Android 9; MI 6 Build/PKQ1.190118.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/66.0.3359.126 MQQBrowser/6.2 TBS/044942 Mobile Safari/537.36', _0x4f02('0x16c', 'RmO]'), 'jdapp;iPhone;10.1.0;11.4;network/wifi;Mozilla/5.0 (iPhone; CPU iPhone OS 11_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15F79', _0x4f02('0x3f', 'RmO]'), _0x4f02('0x50', 'qL])'), _0x4f02('0x37', 'uRHN'), _0x4f02('0x1a8', 'dzX2'), _0x4f02('0x17f', 'VDpf'), _0x4f02('0x6e', 's4Uo'), _0x4f02('0x1', 'TUdS'), _0x4f02('0x71', 'gwdb'), _0x4f02('0xf', 'Z60j'), _0x4f02('0x180', '&]*^'), _0x4f02('0x15c', 'ufiC'), _0x4f02('0xfa', 'fbD2'), _0x4f02('0x43', 'OFtx'), _0x4f02('0x1aa', 'Z9)i'), _0x4f02('0x140', 'gwdb'), _0x4f02('0x93', 'dnnS'), 'jdapp;android;10.1.0;10;network/wifi;Mozilla/5.0 (Linux; Android 10; MI 8 Build/QKQ1.190828.002; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/77.0.3865.120 MQQBrowser/6.2 TBS/045227 Mobile Safari/537.36', 'jdapp;iPhone;10.1.0;14.1;network/wifi;Mozilla/5.0 (iPhone; CPU iPhone OS 14_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1'];

function randomNumber(_0x55d971 = 0x0, _0x325d4e = 0x64) {
    return Math[_0x4f02('0x1ad', 'cb!D')](Math[_0x4f02('0x16e', 'UWQF')](_0x55d971 + Math[_0x4f02('0x77', 'OFtx')]() * (_0x325d4e - _0x55d971)), _0x325d4e);
}
let config = {};
try {
    config = JSON[_0x4f02('0x105', 'i*IW')](fs[_0x4f02('0x19d', '$#b@')](configPath, _0x4f02('0x116', 'Z60j')));
} catch (_0x32f029) {
    console[_0x4f02('0x64', 'mTqS')](_0x4f02('0x82', 'frUB'));
    return;
}
const logDir = path[_0x4f02('0x31', 'ZDsd')](__dirname, 'log');
if (!fs[_0x4f02('0x2c', 'ZKy*')](logDir)) {
    fs[_0x4f02('0xff', 'qL])')](logDir);
}
const logFileName = _0x4f02('0x44', '&]*^') + timeFormat(new Date(), 'date') + _0x4f02('0x69', 'cb!D');
const logFilePath = path[_0x4f02('0xab', 'ufiC')](logDir, logFileName);
const originalConsoleLog = console[_0x4f02('0x137', 'r1T%')];
console[_0x4f02('0x146', 'ZKy*')] = function(..._0x5d5c5b) {
    const _0x49eebc = util[_0x4f02('0x8a', '&]*^')](..._0x5d5c5b) + '';
    fs[_0x4f02('0x185', '$wUv')](logFilePath, _0x49eebc);
    originalConsoleLog[_0x4f02('0x9e', 'r#Cf')](console, _0x5d5c5b);
};
const originalConsoleError = console['error'];
console[_0x4f02('0xd8', '&]*^')] = function(..._0x5f49a3) {
    const _0x4ecf92 = util[_0x4f02('0x1c9', '%1@K')](..._0x5f49a3) + '';
    fs[_0x4f02('0xf6', 'i*IW')](logFilePath, _0x4ecf92);
    originalConsoleError[_0x4f02('0x1bb', 'uRHN')](console, _0x5f49a3);
};
let tryNum = 0x4;
let maxQq = 0x14;
let maxXc = 0x3;
let qqjgTime = 0xfa;
let maxAccount = 0x8;
let ycTime = 0x64;
let cookiesArr = [],
    cookie = '';
let canTaskFlag = [];
let TgCkArray = [];
let lqSucArray = [];
let AllEendCode = '|A9|A6|A14|D2|';
let PEendCode = _0x4f02('0xa', 'gwdb');
let JDTimes = new Date()[_0x4f02('0x101', 'ROTe')]();
let apiArray = [];
let nowIndex = 0x0;
let JDTimeJg = 0x0;
let yhqAPiHasSuccess = {};
let nextHour = 0x0;
let ckerror = [];
let removeYhq = [];
let nowRunYhq = '';
let user_agent = '';
let paramsSignLiteMy = '';
if (config[_0x4f02('0x136', 'i*IW')]) {
    cookiesArr = config[_0x4f02('0x1b9', '!^Fw')];
} else {
    console[_0x4f02('0xa5', 'Z9)i')](_0x4f02('0x122', 'OFtx'));
    return ![];
} if (config[_0x4f02('0x3', 'Z60j')] && config['YHQ_REMOVE'][_0x4f02('0x12a', 'ZDsd')](',')[_0x4f02('0x24', 'mTqS')] >= 0x1) {
    if (config[_0x4f02('0x1be', 'BJZ3')][_0x4f02('0x1b5', 'r1T%')]() == _0x4f02('0xa7', 'KKmU')) {
        console[_0x4f02('0x18e', '$#b@')](_0x4f02('0xa2', 'gwdb'));
        apiList = [];
    } else {
        console[_0x4f02('0x108', '!^Fw')](_0x4f02('0x4f', 'mTqS') + config[_0x4f02('0xa0', 'ZDsd')]);
        removeYhq = config[_0x4f02('0x9b', 'KB8L')][_0x4f02('0x5e', 'qL])')](',');
    }
}
if (config[_0x4f02('0xfc', 'i*IW')]) {
    console[_0x4f02('0xe0', 'lLb0')](_0x4f02('0x20', '*1RL') + config['YHQ_NOWRUN']);
    nowRunYhq = config[_0x4f02('0x145', 'gdIQ')];
}
try {
    const apiListMy = require(_0x4f02('0x1b1', '!^Fw'))['apiList'];
    if (apiListMy[_0x4f02('0x19', 'Y#f)')] > 0x0) {
        for (var alm in apiListMy) {
            if (apiListMy[alm][_0x4f02('0x149', 'KKmU')] && apiListMy[alm][_0x4f02('0x12d', 'NY7O')] && apiListMy[alm][_0x4f02('0x1a', '$#b@')]) {
                apiList[_0x4f02('0xd9', 'gwdb')](apiListMy[alm]);
                console[_0x4f02('0x63', 'TUdS')](_0x4f02('0x12b', 'uRHN') + apiListMy[alm][_0x4f02('0x6a', 'TUdS')]);
            }
        }
    }
} catch (_0x3c07a2) {
    console[_0x4f02('0x126', ')EJa')](_0x4f02('0x10f', 'VDpf'));
}
try {
    paramsSignLiteMy = new window[(_0x4f02('0x106', '0*nj'))]({
        'appId': _0x4f02('0xea', 'mu9&'),
        'preRequest': !0x1
    });
} catch (_0x1210c3) {}
if (config[_0x4f02('0x152', 'ZKy*')] && config[_0x4f02('0x1a9', 'KKmU')]['indexOf'](',') > -0x1 && config['YHQ_API'][_0x4f02('0x134', 'TUdS')](',')[_0x4f02('0x21', 's4Uo')] >= 0x5) {
    console[_0x4f02('0x4', 'uRHN')](_0x4f02('0x100', '$uwW') + config[_0x4f02('0x8d', ')EJa')]);
    let YHQ_API_ARR = config[_0x4f02('0x138', 'ufiC')][_0x4f02('0x2b', 'KKmU')](',');
    tryNum = parseInt(YHQ_API_ARR[0x0]);
    if (parseInt(YHQ_API_ARR[0x1]) > maxQq) {
        maxQq = parseInt(YHQ_API_ARR[0x1]);
    }
    maxXc = parseInt(YHQ_API_ARR[0x2]);
    qqjgTime = parseInt(YHQ_API_ARR[0x3]);
    maxAccount = parseInt(YHQ_API_ARR[0x4]);
    if (YHQ_API_ARR[_0x4f02('0x1a6', 'gwdb')] >= 0x6) {
        ycTime = parseInt(YHQ_API_ARR[0x5]);
    }
}
console['log']('' + timeFormat() + ':' + _0x4f02('0xcf', '%1@K'));
let isMainRunning = ![];
async function executeMain() {
    if (isMainRunning) {
        console[_0x4f02('0x1c7', 'VDpf')](_0x4f02('0x7f', 'i*IW'));
        return;
    }
    isMainRunning = !![];
    try {
        resertCs();
        await main();
    } catch (_0x5b94da) {
        console['error'](_0x4f02('0x117', 'UWQF'), _0x5b94da);
    } finally {
        isMainRunning = ![];
    }
}
executeMain();
cron['schedule'](_0x4f02('0x3e', 'r#Cf'), () => {
    try {
        const _0x2d60f1 = new Date()[_0x4f02('0x12f', 'KB8L')]();
        if (_0x2d60f1 === 0x3b) {
            executeMain();
        } else {
            if (!isMainRunning && _0x2d60f1 % 0x5 === 0x0) {
                console[_0x4f02('0x9f', 's4Uo')]('' + timeFormat() + ':' + _0x4f02('0x13a', 'gdIQ'));
            }
        }
    } catch (_0x550b75) {}
});
async function main() {
    try {
        if (!cookiesArr[0x0]) {
            console[_0x4f02('0xa5', 'Z9)i')](_0x4f02('0x132', 'v1Rp'));
            return;
        } else {
            console[_0x4f02('0xad', '^LhX')](_0x4f02('0x68', 'Y#f)') + cookiesArr[_0x4f02('0x18', 'v1Rp')] + _0x4f02('0x9d', 'l49Q'));
        } if (new Date()[_0x4f02('0x12c', 'pyqW')]() == 0x1 && new Date()[_0x4f02('0x17e', 'KB8L')]() == 0x0) {
            $[_0x4f02('0x1b', '^jVw')]({}, _0x4f02('0x173', 'qL])'));
            console[_0x4f02('0x146', 'ZKy*')](_0x4f02('0x8f', 'e93*'));
        }
        nextHour = nextHourF();
        console['log'](_0x4f02('0x19a', 'ufiC') + nextHour + _0x4f02('0x5', 'v&#Q'));
        user_agent = USER_AGENTS[randomNumber(0x0, USER_AGENTS['length'])];
        for (var _0x59d662 in apiList) {
            if (nowRunYhq && nowRunYhq[_0x4f02('0x4a', 'dzX2')] > 0x0 && nowRunYhq == apiList[_0x59d662][_0x4f02('0x13f', 'r1T%')]) {
                console[_0x4f02('0xc3', 'r#Cf')]('立即抢券（跑完记得删除或禁用该环境变量）：' + apiList[_0x59d662][_0x4f02('0xb', 'ufiC')]);
                apiArray[_0x4f02('0x135', 'Z9)i')](apiList[_0x59d662]);
                doAPIList(apiArray[_0x4f02('0x1b8', 'fbD2')] - 0x1);
                continue;
            }
            if (checkYhq(apiList[_0x59d662], nextHour) && !isRemoveYhqF(apiList[_0x59d662]) && apiArray[_0x4f02('0xcc', '^jVw')] < maxQq) {
                apiArray[_0x4f02('0x115', '0*nj')](apiList[_0x59d662]);
                console[_0x4f02('0x103', 'KB8L')](_0x4f02('0x142', 'hIL(') + apiList[_0x59d662][_0x4f02('0x1b3', '$wUv')]);
            }
        }
        if (apiArray[_0x4f02('0x5b', '0*nj')] <= 0x0) {
            console[_0x4f02('0x81', 'Y#f)')](_0x4f02('0xb8', 'hIL('));
            return;
        }
        if ($['getdata']('JDTimeJg') && $[_0x4f02('0x4b', '&]*^')](_0x4f02('0x165', 'mTqS')) != 0x0) {
            JDTimeJg = $[_0x4f02('0xa8', '%1@K')](_0x4f02('0xda', 'r#Cf'));
        }
        if ($[_0x4f02('0x88', 's4Uo')](_0x4f02('0x40', ')EJa'))) {
            yhqAPiHasSuccess = $['getjson'](_0x4f02('0x1bf', 'mTqS'));
        }
        let _0x5a99f8 = jgNextHourF() + JDTimeJg - ycTime;
        if (_0x5a99f8 > 0x2 * 0x3c * 0x3e8) {
            console['log'](parseInt(_0x5a99f8 / 0x3c / 0x3e8) + _0x4f02('0x8c', 's4Uo'));
            return;
        }
        if (_0x5a99f8 > 0x0) {
            console['log'](parseInt(_0x5a99f8 / 0x3c / 0x3e8) + _0x4f02('0x1cc', '^LhX'));
            await $[_0x4f02('0xcb', '^jVw')](_0x5a99f8);
        }
        for (let _0x57b2e7 in apiArray) {
            if (!yhqAPiHasSuccess[apiArray[_0x57b2e7][_0x4f02('0x80', 'v&#Q')]]) {
                yhqAPiHasSuccess[apiArray[_0x57b2e7][_0x4f02('0x149', 'KKmU')]] = {};
            }
            doAPIList(_0x57b2e7);
        }
        await $[_0x4f02('0x175', '%1@K')](0x3 * 0x3e8);
        for (let _0x160780 in apiArray) {
            let _0x1d34e9 = '';
            if (lqSucArray[_0x160780][_0x4f02('0x21', 's4Uo')] > 0x0) {
                if (apiArray[_0x160780][_0x4f02('0xf4', 'v1Rp')]) {
                    _0x1d34e9 += _0x4f02('0x18f', 'qL])') + apiArray[_0x160780][_0x4f02('0x41', 'RmO]')] + '】';
                }
                _0x1d34e9 += _0x4f02('0x158', 'i*IW');
                for (var _0x3a4043 in lqSucArray[_0x160780]) {
                    cookie = cookiesArr[lqSucArray[_0x160780][_0x3a4043]];
                    let _0x18ba0d = decodeURIComponent(cookie[_0x4f02('0x1c5', 'RmO]')](/pt_pin=([^; ]+)(?=;?)/) && cookie['match'](/pt_pin=([^; ]+)(?=;?)/)[0x1]);
                    _0x1d34e9 += '' + (lqSucArray[_0x160780][_0x3a4043] + 0x1) + '、' + _0x18ba0d;
                }
                console['log'](_0x4f02('0x1c8', 'RmO]'));
                console[_0x4f02('0x15e', 'RmO]')](_0x1d34e9);
            }
            if (_0x1d34e9) {
                myNotice(_0x1d34e9);
                console[_0x4f02('0xf0', 'mu9&')](_0x1d34e9);
                _0x1d34e9 = '';
            }
        }
        $[_0x4f02('0x45', 'e93*')](yhqAPiHasSuccess, _0x4f02('0x167', 'TUdS'));
    } catch (_0x2aab18) {
        console[_0x4f02('0x11f', 'frUB')](_0x4f02('0x70', 'r1T%'), _0x2aab18);
    }
}

function resertCs() {
    canTaskFlag = [];
    TgCkArray = [];
    lqSucArray = [];
    apiArray = [];
    nowIndex = 0x0;
    yhqAPiHasSuccess = {};
}
async function doAPIList(_0x315c72) {
    canTaskFlag[_0x315c72] = !![];
    TgCkArray[_0x315c72] = [];
    lqSucArray[_0x315c72] = [];
    for (let _0x44db88 = 0x1; _0x44db88 <= tryNum; _0x44db88++) {
        if (canTaskFlag[_0x315c72] && TgCkArray[_0x315c72][_0x4f02('0x19c', 'OFtx')] < cookiesArr['length'] && TgCkArray[_0x315c72][_0x4f02('0x1d', ')EJa')] < maxAccount) {
            console[_0x4f02('0xad', '^LhX')](_0x4f02('0x35', 'Y#f)') + apiArray[_0x315c72][_0x4f02('0x13', '!^Fw')] + '】第' + _0x44db88 + _0x4f02('0x32', '*Jnq'));
            for (let _0x44d8f6 = 0x0; _0x44d8f6 < cookiesArr['length'] && _0x44d8f6 < maxAccount; _0x44d8f6++) {
                let _0x35647f = apiArray[_0x315c72][_0x4f02('0x1c2', 'e93*')] ? apiArray[_0x315c72]['ckIndex'] : 0x0;
                if (_0x35647f > 0x0) {
                    if (_0x44d8f6 + 0x1 < _0x35647f) {
                        continue;
                    } else if (_0x44d8f6 + 0x1 > _0x35647f) {
                        break;
                    } else {
                        console[_0x4f02('0x127', 'cb!D')](_0x4f02('0x171', 'r#Cf') + _0x35647f + _0x4f02('0xeb', 'cb!D'));
                    }
                }
                if (canTaskFlag[_0x315c72]) {
                    if (cookiesArr[_0x44d8f6]) {
                        let _0x513bfb = decodeURIComponent(cookiesArr[_0x44d8f6][_0x4f02('0xe7', 'ufiC')](/pt_pin=([^; ]+)(?=;?)/) && cookiesArr[_0x44d8f6]['match'](/pt_pin=([^; ]+)(?=;?)/)[0x1]);
                        if (TgCkArray[_0x315c72]['includes'](_0x44d8f6)) {
                            console[_0x4f02('0xe0', 'lLb0')](_0x4f02('0x55', 'dzX2') + (_0x44d8f6 + 0x1) + ':' + _0x513bfb + '！');
                            continue;
                        }
                        try {
                            if (yhqAPiHasSuccess[apiArray[_0x315c72][_0x4f02('0x1b2', '%1@K')]][_0x513bfb] && nextHour != 0x0) {
                                let _0x43b1ec = getNowDate();
                                if (DateDiff(_0x43b1ec, yhqAPiHasSuccess[apiArray[_0x315c72][_0x4f02('0x1b4', '^LhX')]][_0x513bfb]) < apiArray[_0x315c72][_0x4f02('0xf5', 'KKmU')]) {
                                    console[_0x4f02('0xd4', 'gwdb')](_0x4f02('0x12e', 'frUB') + (_0x44d8f6 + 0x1) + ':' + _0x513bfb + '！');
                                    TgCkArray[_0x315c72][_0x4f02('0xbb', '^LhX')](_0x44d8f6);
                                    continue;
                                }
                            }
                        } catch (_0x29f0f3) {}
                        nowIndex++;
                        if (nowIndex >= maxXc) {
                            if (nowIndex % maxXc == 0x0) {
                                await $[_0x4f02('0x17d', 'r1T%')](qqjgTime - 0x14);
                            } else {
                                await $[_0x4f02('0x10b', 'mu9&')](0xa);
                            }
                        }
                        doApiTask(_0x315c72, _0x44d8f6);
                    }
                } else {
                    console[_0x4f02('0xca', '*Jnq')](_0x4f02('0x168', 'i*IW'));
                    break;
                }
            }
        } else {
            break;
        }
    }
}
async function doApiTask(_0x209282, _0x1f66b9) {
    console[_0x4f02('0x108', '!^Fw')]('' + nowIndex + '、' + timeFormat() + (':开始领取' + apiArray[_0x209282][_0x4f02('0xc6', '$#b@')] + _0x4f02('0x7c', 'fbD2') + (_0x1f66b9 + 0x1)));
    return new Promise(async _0x496286 => {
        if (canTaskFlag[_0x209282]) {
            if (apiArray[_0x209282][_0x4f02('0x1a2', 'KB8L')][_0x4f02('0x95', 'mu9&')]('G') > -0x1 || apiArray[_0x209282][_0x4f02('0x84', '$#b@')][_0x4f02('0xb3', 'UWQF')](_0x4f02('0x98', 'NY7O')) > -0x1 || apiArray[_0x209282][_0x4f02('0x1f', 'UWQF')][_0x4f02('0x1ab', '$uwW')]('h5_awake_wxapp') > -0x1) {
                const _0x118d4c = await getApiUrlGet(_0x209282, _0x1f66b9);
                $['get'](_0x118d4c, (_0x3e42d3, _0x3a7eee, _0x256d53) => {
                    try {
                        if (_0x3e42d3) {
                            console[_0x4f02('0xd6', 'frUB')](_0x4f02('0xb6', 'mTqS'));
                        } else {
                            cookie = cookiesArr[_0x1f66b9];
                            let _0x3b8112 = decodeURIComponent(cookie[_0x4f02('0x1e', 'Z60j')](/pt_pin=([^; ]+)(?=;?)/) && cookie['match'](/pt_pin=([^; ]+)(?=;?)/)[0x1]);
                            console[_0x4f02('0x1c7', 'VDpf')](_0x4f02('0xc1', '0*nj') + apiArray[_0x209282][_0x4f02('0x19b', 'ZDsd')] + _0x4f02('0xf2', 'TUdS') + (_0x1f66b9 + 0x1) + '】' + _0x3b8112 + '*');
                            console[_0x4f02('0x162', 'UWQF')](timeFormat() + ':' + _0x256d53);
                            if (_0x256d53[_0x4f02('0x6c', '&]*^')]('成功') > -0x1) {
                                lqSucArray[_0x209282]['push'](_0x1f66b9);
                                yhqAPiHasSuccess[apiArray[_0x209282][_0x4f02('0x121', 'Y#f)')]][_0x3b8112] = getNowDate();
                            } else if (_0x256d53[_0x4f02('0xaa', 'RmO]')]('再来') > -0x1 || _0x256d53[_0x4f02('0x15f', 'Y#f)')]('抢光') > -0x1) {
                                canTaskFlag[_0x209282] = ![];
                            }
                        }
                    } catch (_0x761396) {
                        TgCkArray[_0x209282][_0x4f02('0x5d', 'NY7O')](_0x1f66b9);
                        $[_0x4f02('0xbc', 'frUB')](_0x761396, _0x3a7eee);
                    } finally {
                        _0x496286(_0x256d53);
                    }
                });
            } else {
                const _0xf78332 = await getApiUrl(_0x209282, _0x1f66b9);
                $[_0x4f02('0xd5', '%1@K')](_0xf78332, (_0xa9f8e8, _0x353712, _0x250069) => {
                    try {
                        if (_0xa9f8e8) {
                            console[_0x4f02('0x63', 'TUdS')]('' + JSON[_0x4f02('0xe6', 'r1T%')](_0xa9f8e8));
                            console[_0x4f02('0xba', 'dzX2')](_0x4f02('0xe3', 'lLb0'));
                        } else {
                            cookie = cookiesArr[_0x1f66b9];
                            let _0x31eecc = decodeURIComponent(cookie[_0x4f02('0x1e', 'Z60j')](/pt_pin=([^; ]+)(?=;?)/) && cookie[_0x4f02('0xaf', 'fbD2')](/pt_pin=([^; ]+)(?=;?)/)[0x1]);
                            console[_0x4f02('0x10e', 'v&#Q')]('*' + apiArray[_0x209282][_0x4f02('0x13', '!^Fw')] + '_【账号' + (_0x1f66b9 + 0x1) + '】' + _0x31eecc + '*');
                            _0x250069 = JSON['parse'](_0x250069);
                            let _0x2cf1c0 = '';
                            let _0x3d7c3d = '';
                            try {
                                _0x3d7c3d = '|' + _0x250069[_0x4f02('0x13d', '*1RL')] + '|';
                                _0x2cf1c0 = _0x250069[_0x4f02('0xa1', 'OFtx')] || _0x250069[_0x4f02('0x1cd', 'Z9)i')]['msg'];
                            } catch (_0x4ea171) {}
                            if (_0x250069[_0x4f02('0x179', 'mTqS')] && (_0x250069[_0x4f02('0xdf', ')EJa')] == 'A1' || _0x250069[_0x4f02('0x25', 'qL])')] == '0') || _0x2cf1c0 && _0x2cf1c0['indexOf']('成功') > -0x1) {
                                lqSucArray[_0x209282][_0x4f02('0x1c1', 'dnnS')](_0x1f66b9);
                                yhqAPiHasSuccess[apiArray[_0x209282][_0x4f02('0xd0', 'Z60j')]][_0x31eecc] = getNowDate();
                            }
                            if (AllEendCode[_0x4f02('0x6f', 'fbD2')](_0x3d7c3d) > -0x1) {
                                if (_0x250069[_0x4f02('0x10a', 'frUB')] == 'D2' && _0x2cf1c0[_0x4f02('0x73', '*1RL')](_0x2cf1c0[_0x4f02('0x7b', 'frUB')]('请') + 0x1, 0x2) == nextHour) {
                                    console[_0x4f02('0xca', '*Jnq')](timeFormat() + _0x4f02('0x72', 'TUdS') + _0x2cf1c0);
                                } else if (nextHour == 0x0) {
                                    console[_0x4f02('0xd6', 'frUB')](timeFormat() + _0x4f02('0x128', 'gdIQ') + _0x2cf1c0);
                                } else {
                                    canTaskFlag[_0x209282] = ![];
                                    console[_0x4f02('0x59', 'gdIQ')](timeFormat() + ':' + _0x2cf1c0);
                                }
                            } else if (PEendCode[_0x4f02('0x16', 'l49Q')](_0x3d7c3d) > -0x1) {
                                TgCkArray[_0x209282]['push'](_0x1f66b9);
                                console[_0x4f02('0x176', '%1@K')](timeFormat() + ':' + _0x2cf1c0 + _0x4f02('0x139', 'ROTe') + _0x3d7c3d);
                            } else if (_0x250069['code'] && _0x250069['code'] == '3') {
                                TgCkArray[_0x209282]['push'](_0x1f66b9);
                                console['log'](timeFormat() + _0x4f02('0x96', 'dzX2'));
                                if (!checkHasCz(ckerror, _0x1f66b9)) {
                                    ckerror[_0x4f02('0x131', 's4Uo')](_0x1f66b9);
                                    myNotice(_0x4f02('0x42', 'RmO]') + (_0x1f66b9 + 0x1) + '】' + _0x31eecc + _0x4f02('0x2a', 'dzX2'));
                                    console[_0x4f02('0x174', 'gdIQ')](_0x4f02('0xdb', 'OFtx') + (_0x1f66b9 + 0x1) + '】' + _0x31eecc + _0x4f02('0x193', 'UWQF'));
                                }
                            } else {
                                console[_0x4f02('0x127', 'cb!D')](timeFormat() + ':' + JSON[_0x4f02('0xae', 'gwdb')](_0x250069));
                            }
                        }
                    } catch (_0x279aaf) {
                        TgCkArray[_0x209282]['push'](_0x1f66b9);
                        $['logErr'](_0x279aaf, _0x353712);
                    } finally {
                        _0x496286(_0x250069);
                    }
                });
            }
        } else {
            console[_0x4f02('0x146', 'ZKy*')](_0x4f02('0x7d', 'gdIQ'));
        }
    });
}

function getJDTime() {
    return new Promise(_0x21ee15 => {
        $[_0x4f02('0xd5', '%1@K')]({
            'url': _0x4f02('0x148', 'r#Cf')
        }, async(_0x2e7080, _0x1522aa, _0x1dbcae) => {
            try {
                if (_0x2e7080) {
                    console[_0x4f02('0x162', 'UWQF')]('获取JD时间失败');
                } else {
                    _0x1dbcae = JSON[_0x4f02('0xc0', 'cb!D')](_0x1dbcae);
                    if (_0x1dbcae[_0x4f02('0x196', 'r1T%')] && _0x1dbcae[_0x4f02('0xe9', 'RmO]')] == '0') {
                        JDTimes = parseInt(_0x1dbcae[_0x4f02('0xe2', 's4Uo')]);
                        if (JDTimeJg == 0x0 || JDTimeJg != 0x0 && new Date()[_0x4f02('0x12', 'BJZ3')]() - JDTimes < JDTimeJg) {
                            JDTimeJg = new Date()[_0x4f02('0xb2', 'dnnS')]() - JDTimes;
                        }
                    } else {
                        console[_0x4f02('0x16d', 'ZDsd')](_0x4f02('0x3d', 'KKmU') + JSON[_0x4f02('0x56', '$wUv')](_0x1dbcae));
                    }
                }
            } catch (_0x5206ce) {
                $[_0x4f02('0xa6', '%1@K')](_0x5206ce, _0x1522aa);
            } finally {
                _0x21ee15(_0x1dbcae);
            }
        });
    });
}

function checkYhq(_0x3d720a, _0x5c25bd) {
    if (!_0x3d720a['endDate']) {
        return !![];
    }
    if (_0x3d720a[_0x4f02('0xde', 'UWQF')] && _0x3d720a[_0x4f02('0x6b', '^jVw')] && new Date(_0x3d720a[_0x4f02('0x1bd', 'dzX2')] + ' 23:59:59')['getTime']() > new Date()[_0x4f02('0x97', 'e93*')]()) {
        let _0x2776bb = _0x3d720a[_0x4f02('0x57', 'UWQF')][_0x4f02('0x12a', 'ZDsd')](',');
        if (_0x2776bb[_0x4f02('0x182', 'ZDsd')] > 0x0 && _0x2776bb['includes'](_0x5c25bd + '')) {
            return !![];
        }
    }
    return ![];
}

function isRemoveYhqF(_0xee10e3) {
    let _0x27a404 = ![];
    if (removeYhq && removeYhq[_0x4f02('0xc4', 'pyqW')] > 0x0) {
        for (var _0x248875 in removeYhq) {
            if (_0xee10e3[_0x4f02('0x19f', 'dnnS')] == removeYhq[_0x248875]) {
                console[_0x4f02('0x85', 'e93*')](_0x4f02('0x183', '$wUv') + _0xee10e3[_0x4f02('0x6', 'UWQF')]);
                _0x27a404 = !![];
                break;
            }
        }
    }
    return _0x27a404;
}
async function getApiUrl(_0x56778b, _0x23696c) {
    const _0xcfebdf = await getDecryptUrlTy(getApiLog(apiArray[_0x56778b][_0x4f02('0x19e', 'pyqW')]));
    return {
        'url': _0xcfebdf,
        'headers': {
            'user-agent': user_agent,
            'content-Type': _0x4f02('0x199', 'gdIQ'),
            'accept': _0x4f02('0xbf', 'l49Q'),
            'accept-encoding': _0x4f02('0x2', '&]*^'),
            'accept-language': _0x4f02('0xc', '^LhX'),
            'cache-control': _0x4f02('0xc7', 'Y#f)'),
            'cookie': cookiesArr[_0x23696c]
        }
    };
}
async function getApiUrlGet(_0x5e747e, _0xc808e0) {
    if (apiArray[_0x5e747e]['qApi'][_0x4f02('0x16', 'l49Q')](_0x4f02('0x189', '0*nj')) > -0x1 || apiArray[_0x5e747e]['qApi'][_0x4f02('0x94', 'e93*')](_0x4f02('0x47', 'ROTe')) > -0x1) {
        const _0x4d74f8 = await getDecryptUrlTy(getApiLog(apiArray[_0x5e747e][_0x4f02('0x1ba', 'dzX2')]));
        return {
            'url': _0x4d74f8,
            'headers': {
                'User-Agent': user_agent,
                'Cookie': cookiesArr[0x0],
                'Accept': _0x4f02('0x58', '!^Fw'),
                'Accept-Encoding': 'gzip, deflate, br',
                'Accept-Language': _0x4f02('0x13c', 'i*IW'),
                'Referer': _0x4f02('0x7', 'KKmU')
            }
        };
    } else if (apiArray[_0x5e747e][_0x4f02('0x1f', 'UWQF')]['indexOf'](_0x4f02('0x36', 'ROTe')) > -0x1) {
        return {
            'url': apiArray[_0x5e747e][_0x4f02('0x195', 'e93*')],
            'headers': {
                'User-Agent': user_agent,
                'accept-encoding': _0x4f02('0xa9', 'UWQF'),
                'accept-language': _0x4f02('0x186', 'Y#f)'),
                'Cookie': cookiesArr[_0xc808e0],
                'origin': _0x4f02('0x169', 'mu9&')
            }
        };
    } else {
        return {
            'url': apiArray[_0x5e747e][_0x4f02('0x188', 'v1Rp')],
            'headers': {
                'User-Agent': user_agent,
                'accept-encoding': _0x4f02('0xd2', 'gwdb'),
                'accept-language': _0x4f02('0x166', '^jVw'),
                'Cookie': cookiesArr[_0xc808e0]
            }
        };
    }
}

function jgNextHourF() {
    let _0x3e1e1e = timeFormat()[_0x4f02('0x5c', 'fbD2')](0x0, 0xd) + _0x4f02('0xb4', 'RmO]');
    let _0x326886 = Date[_0x4f02('0x157', 'Z60j')](new Date(_0x3e1e1e)) + 0x3c * 0x3c * 0x3e8;
    return _0x326886 - new Date()['getTime']();
}

function myNotice(_0x16de16) {}

function nextHourF() {
    let _0x3e1c44 = new Date();
    return _0x3e1c44[_0x4f02('0x67', 'hIL(')]() + 0x1 >= 0x18 ? 0x0 : _0x3e1c44['getHours']() + 0x1;
}

function DateDiff(_0x58a163, _0x452cf8) {
    var _0x14660f, _0x3ffd45, _0x4e88e6, _0x530806;
    _0x14660f = _0x58a163[_0x4f02('0x15a', 'mTqS')]('-');
    _0x3ffd45 = new Date(_0x14660f[0x1] + '-' + _0x14660f[0x2] + '-' + _0x14660f[0x0]);
    _0x14660f = _0x452cf8[_0x4f02('0x1b7', 'Y#f)')]('-');
    _0x4e88e6 = new Date(_0x14660f[0x1] + '-' + _0x14660f[0x2] + '-' + _0x14660f[0x0]);
    _0x530806 = parseInt(Math[_0x4f02('0x113', '^LhX')](_0x3ffd45 - _0x4e88e6) / 0x3e8 / 0x3c / 0x3c / 0x18);
    return _0x530806;
}

function getNowDate() {
    let _0x11667f = new Date();
    return _0x11667f[_0x4f02('0x160', '^LhX')]() + '-' + (_0x11667f[_0x4f02('0xce', 'v&#Q')]() + 0x1 >= 0xa ? _0x11667f[_0x4f02('0x124', '^jVw')]() + 0x1 : '0' + (_0x11667f[_0x4f02('0x104', 'hIL(')]() + 0x1)) + '-' + (_0x11667f[_0x4f02('0xf1', '0*nj')]() >= 0xa ? _0x11667f[_0x4f02('0x83', '$#b@')]() : '0' + _0x11667f[_0x4f02('0xfb', 'ZKy*')]());
}

function timeFormat(_0x476759, _0x18f799) {
    let _0x1b1c03;
    if (_0x476759) {
        _0x1b1c03 = new Date(_0x476759);
    } else {
        _0x1b1c03 = new Date();
    } if (_0x18f799 == 'date') {
        return _0x1b1c03[_0x4f02('0x17a', 'BJZ3')]() + '-' + (_0x1b1c03['getMonth']() + 0x1 >= 0xa ? _0x1b1c03[_0x4f02('0x62', ')EJa')]() + 0x1 : '0' + (_0x1b1c03[_0x4f02('0x17', '$uwW')]() + 0x1)) + '-' + (_0x1b1c03[_0x4f02('0x181', 'e93*')]() >= 0xa ? _0x1b1c03[_0x4f02('0x83', '$#b@')]() : '0' + _0x1b1c03['getDate']());
    }
    return _0x1b1c03[_0x4f02('0x194', 'ZDsd')]() + '-' + (_0x1b1c03[_0x4f02('0xdc', 'TUdS')]() + 0x1 >= 0xa ? _0x1b1c03[_0x4f02('0x3b', 'ZDsd')]() + 0x1 : '0' + (_0x1b1c03['getMonth']() + 0x1)) + '-' + (_0x1b1c03['getDate']() >= 0xa ? _0x1b1c03[_0x4f02('0x14f', '^jVw')]() : '0' + _0x1b1c03[_0x4f02('0x14d', 'UWQF')]()) + ' ' + (_0x1b1c03[_0x4f02('0x4d', 'UWQF')]() >= 0xa ? _0x1b1c03['getHours']() : '0' + _0x1b1c03[_0x4f02('0x1a1', 'mTqS')]()) + ':' + (_0x1b1c03[_0x4f02('0x143', 'ZKy*')]() >= 0xa ? _0x1b1c03[_0x4f02('0x11', 'i*IW')]() : '0' + _0x1b1c03[_0x4f02('0x86', 'Z9)i')]()) + ':' + (_0x1b1c03[_0x4f02('0xe4', 'cb!D')]() >= 0xa ? _0x1b1c03[_0x4f02('0xd7', 'dnnS')]() : '0' + _0x1b1c03[_0x4f02('0x1a0', 'fbD2')]()) + ':' + _0x1b1c03[_0x4f02('0x39', 'mu9&')]();
}

function getApiLog(_0x3a204f) {
    let _0x1ebbfc = smashUtils[_0x4f02('0x191', 'e93*')](0x8);
    let _0x4ae01c = (smashUtils[_0x4f02('0x18c', 'ZKy*')]({
        'id': 'coupon',
        'data': {
            'random': _0x1ebbfc
        }
    }, "") || {})[_0x4f02('0x1b0', 'NY7O')];
    let _0xfb9364 = encodeURIComponent(_0x4f02('0xfe', 'r#Cf') + _0x4ae01c + '","random":"' + _0x1ebbfc + '"');
    if (_0x3a204f && _0x3a204f[_0x4f02('0xf7', 'ROTe')](_0x4f02('0x1ca', 'Y#f)')) > -0x1) {
        _0xfb9364 = _0x3a204f[_0x4f02('0xf3', 'KKmU')](0x0, _0x3a204f[_0x4f02('0x30', 'ZKy*')](_0x4f02('0xd1', '^jVw'))) + _0xfb9364 + _0x3a204f[_0x4f02('0x92', 'frUB')](_0x3a204f[_0x4f02('0x94', 'e93*')](_0x4f02('0x9', 'UWQF')), _0x3a204f[_0x4f02('0x24', 'mTqS')]);
    }
    return _0xfb9364;
}

function checkHasCz(_0xbf4a7f, _0x244387) {
    let _0x406a4f = ![];
    if (_0xbf4a7f) {
        for (var _0x349d99 in _0xbf4a7f) {
            if (_0xbf4a7f[_0x349d99] == _0x244387) {
                _0x406a4f = !![];
                break;
            }
        }
    }
    return _0x406a4f;
}

function getUrlQueryParams(_0x4c1021, _0x1ce2ac) {
    let _0x1da674 = new RegExp(_0x4f02('0x119', 'qL])') + _0x1ce2ac + _0x4f02('0x4e', 'i*IW'), 'i');
    let _0x309e14 = _0x4c1021[_0x4f02('0x11a', '&]*^')]('?')[0x1]['substr'](0x0)['match'](_0x1da674);
    if (_0x309e14 != null) {
        return decodeURIComponent(_0x309e14[0x2]);
    };
    return '';
}

function sha256Hash(_0x31e864) {
    const _0x39d785 = new TextEncoder();
    const _0x2355d8 = _0x39d785[_0x4f02('0x18d', '*Jnq')](_0x31e864);
    const _0x4f239d = $[_0x4f02('0x184', '$#b@')][_0x4f02('0xa4', '*Jnq')]($[_0x4f02('0xed', '&]*^')][_0x4f02('0x8e', '%1@K')][_0x4f02('0x163', '^jVw')][_0x4f02('0xc9', 'dnnS')](_0x31e864));
    const _0x5bad46 = _0x4f239d[_0x4f02('0x1a4', 'v&#Q')]($[_0x4f02('0x16a', 'NY7O')][_0x4f02('0x1cb', 's4Uo')][_0x4f02('0x8b', 'v1Rp')]);
    return _0x5bad46;
}

function getDecryptUrlTy(_0x1462ff) {
    return new Promise((_0x1c381e, _0xa1473d) => {
        let _0x20fc97 = sha256Hash(getUrlQueryParams(_0x1462ff, _0x4f02('0x1ac', '&]*^')));
        let _0x2e7773 = {
            'appid': _0x4f02('0xb0', '$wUv'),
            'body': _0x20fc97,
            'client': _0x4f02('0x3a', 'fbD2'),
            'clientVersion': _0x4f02('0x11c', 'r#Cf'),
            'functionId': _0x4f02('0x17c', 'dnnS')
        };
        paramsSignLiteMy[_0x4f02('0x66', 'Z9)i')](_0x2e7773)[_0x4f02('0xef', 'BJZ3')](_0xe4545 => {
            _0x1c381e(_0x1462ff + _0x4f02('0x154', 'v1Rp') + _0xe4545[_0x4f02('0xe5', 'frUB')]);
        })[_0x4f02('0xcd', 'v1Rp')](_0x52cccd => {
            console[_0x4f02('0xb1', 'pyqW')](_0x4f02('0x1c4', '0*nj'), _0x52cccd);
            _0x1c381e(_0x1462ff);
        });
    });
}

function getDecryptUrl(_0x1a1eaf) {
    _0x1a1eaf = _0x1a1eaf + _0x4f02('0xac', '%1@K') + Date[_0x4f02('0x2f', 'l49Q')]();
    stk = getUrlQueryParams(_0x1a1eaf, _0x4f02('0x178', 'fbD2'));
    if (stk) {
        const _0x660091 = format(_0x4f02('0x123', '$wUv'), Date[_0x4f02('0x1a3', 'dzX2')]());
        const _0x3b96a4 = $[_0x4f02('0x170', 'gdIQ')]($[_0x4f02('0x51', '!^Fw')], $['fp'][_0x4f02('0x27', '^jVw')](), _0x660091[_0x4f02('0x48', '*1RL')](), $[_0x4f02('0x133', 'Z60j')][_0x4f02('0x3c', 'Y#f)')](), $[_0x4f02('0x53', 'l49Q')])[_0x4f02('0xdd', 'r1T%')]($[_0x4f02('0x16a', 'NY7O')][_0x4f02('0x79', 'dzX2')]['Hex']);
        let _0x2a3caf = '';
        stk[_0x4f02('0xc5', 'dnnS')](',')[_0x4f02('0xc2', 'NY7O')]((_0x1b945f, _0x23c740) => {
            _0x2a3caf += _0x1b945f + ':' + getUrlQueryParams(_0x1a1eaf, _0x1b945f) + (_0x23c740 === stk[_0x4f02('0x1bc', 'hIL(')](',')[_0x4f02('0xe', '$uwW')] - 0x1 ? '' : '&');
        });
        const _0x35ce14 = $[_0x4f02('0x76', 'ROTe')][_0x4f02('0xb9', 'ZKy*')](_0x2a3caf, _0x3b96a4[_0x4f02('0x26', 'v1Rp')]())[_0x4f02('0x17b', 'KKmU')]($[_0x4f02('0x13b', 'hIL(')][_0x4f02('0x1a7', 'ZKy*')][_0x4f02('0x1af', '*1RL')]);
        return _0x1a1eaf + '&h5st=' + encodeURIComponent(['' [_0x4f02('0x130', 'e93*')](_0x660091[_0x4f02('0x9c', '*Jnq')]()), '' ['concat']($['fp'][_0x4f02('0xd', 'OFtx')]()), '' [_0x4f02('0x164', 'qL])')]($[_0x4f02('0x23', 'NY7O')]['toString']()), '' [_0x4f02('0x78', 'lLb0')]($[_0x4f02('0x8', 'frUB')]), '' ['concat'](_0x35ce14), _0x4f02('0x10c', '^LhX')[_0x4f02('0x18b', '!^Fw')](_0x660091)][_0x4f02('0x190', '*Jnq')](';')) + _0x4f02('0x111', 'lLb0') + Date['now']();
    }
}
async function requestAlgo() {
    $[_0x4f02('0x7a', 'v&#Q')] = _0x4f02('0x29', 'TUdS');
    $['fp'] = (getRandomIDPro({
        'size': 0xd
    }) + Date[_0x4f02('0x120', '^jVw')]())[_0x4f02('0x15', 'BJZ3')](0x0, 0x10);
    const _0xf4cd6b = {
        'url': _0x4f02('0x1c0', 'v1Rp'),
        'headers': {
            'Authority': _0x4f02('0x52', 'Y#f)'),
            'Pragma': _0x4f02('0xb7', '$#b@'),
            'Cache-Control': _0x4f02('0x1c6', 'TUdS'),
            'Accept': _0x4f02('0x54', '*1RL'),
            'Content-Type': _0x4f02('0x75', 'OFtx'),
            'Origin': _0x4f02('0x10d', 'Y#f)'),
            'Sec-Fetch-Site': _0x4f02('0xa3', '^jVw'),
            'User-Agent': $[_0x4f02('0xf8', '$uwW')],
            'Sec-Fetch-Mode': _0x4f02('0x46', 'VDpf'),
            'Sec-Fetch-Dest': 'empty',
            'Referer': _0x4f02('0xfd', '0*nj'),
            'Accept-Language': 'zh-CN,zh;q=0.9,zh-TW;q=0.8,en;q=0.7'
        },
        'body': JSON['stringify']({
            'version': _0x4f02('0x150', 'i*IW'),
            'fp': $['fp'],
            'appId': $[_0x4f02('0x161', 'dnnS')],
            'timestamp': Date[_0x4f02('0x0', 'ROTe')](),
            'platform': _0x4f02('0xbd', 'TUdS'),
            'expandParams': ''
        })
    };
    return new Promise(async _0x5058db => {
        $[_0x4f02('0x109', 'OFtx')](_0xf4cd6b, (_0xe25c05, _0x6e76dd, _0xf224a0) => {
            try {
                const {
                    ret, msg, data: {
                        result
                    } = {}
                } = JSON[_0x4f02('0x1b6', 'NY7O')](_0xf224a0);
                $['token'] = result['tk'];
                $[_0x4f02('0xec', 'RmO]')] = new Function(_0x4f02('0x1c3', '!^Fw') + result['algo'])();
            } catch (_0x38239b) {
                $['logErr'](_0x38239b, _0x6e76dd);
            } finally {
                _0x5058db();
            }
        });
    });
}

function getRandomIDPro() {
    var _0x158423, _0x35153a, _0x281d64 = void 0x0 === (_0x55a423 = (_0x35153a = 0x0 < arguments[_0x4f02('0x147', 'frUB')] && void 0x0 !== arguments[0x0] ? arguments[0x0] : {})[_0x4f02('0x4c', 'OFtx')]) ? 0xa : _0x55a423,
        _0x55a423 = void 0x0 === (_0x55a423 = _0x35153a['dictType']) ? 'number' : _0x55a423,
        _0x2bdbe7 = '';
    if ((_0x35153a = _0x35153a[_0x4f02('0x6d', 'r1T%')]) && _0x4f02('0x5a', 'fbD2') == typeof _0x35153a) _0x158423 = _0x35153a;
    else switch (_0x55a423) {
        case _0x4f02('0x197', 'ZKy*'):
            _0x158423 = _0x4f02('0x159', 'pyqW');
            break;
        case _0x4f02('0x38', '*1RL'):
            _0x158423 = _0x4f02('0x89', 'i*IW');
            break;
        case _0x4f02('0x13e', 'r#Cf'):
        default:
            _0x158423 = _0x4f02('0xe1', '^jVw');
    }
    for (; _0x281d64--;) _0x2bdbe7 += _0x158423[Math[_0x4f02('0x99', 'Z60j')]() * _0x158423['length'] | 0x0];
    return _0x2bdbe7;
}

function format(_0x4e2821, _0x1830a2) {
    if (!_0x4e2821) _0x4e2821 = _0x4f02('0x14a', '0*nj');
    var _0x1660d1;
    if (!_0x1830a2) {
        _0x1660d1 = Date[_0x4f02('0xc8', 'ZKy*')]();
    } else {
        _0x1660d1 = new Date(_0x1830a2);
    }
    var _0x5b159f, _0x2c72b9 = new Date(_0x1660d1),
        _0x557d25 = _0x4e2821,
        _0x22de01 = {
            'M+': _0x2c72b9[_0x4f02('0x3b', 'ZDsd')]() + 0x1,
            'd+': _0x2c72b9['getDate'](),
            'D+': _0x2c72b9[_0x4f02('0x14e', 'r1T%')](),
            'h+': _0x2c72b9[_0x4f02('0x144', 'BJZ3')](),
            'H+': _0x2c72b9[_0x4f02('0xb5', 'frUB')](),
            'm+': _0x2c72b9['getMinutes'](),
            's+': _0x2c72b9[_0x4f02('0x15b', 'i*IW')](),
            'w+': _0x2c72b9[_0x4f02('0x172', 'gdIQ')](),
            'q+': Math[_0x4f02('0x11b', 'TUdS')]((_0x2c72b9[_0x4f02('0x3b', 'ZDsd')]() + 0x3) / 0x3),
            'S+': _0x2c72b9[_0x4f02('0x11d', 'Z9)i')]()
        };
    /(y+)/i [_0x4f02('0x141', '&]*^')](_0x557d25) && (_0x557d25 = _0x557d25[_0x4f02('0x18a', 'gwdb')](RegExp['$1'], '' [_0x4f02('0x1a5', 'VDpf')](_0x2c72b9[_0x4f02('0x160', '^LhX')]())[_0x4f02('0x129', '^LhX')](0x4 - RegExp['$1'][_0x4f02('0x14b', '$wUv')])));
    Object[_0x4f02('0x156', 'TUdS')](_0x22de01)[_0x4f02('0x90', 'KB8L')](_0x3a57f9 => {
        if (new RegExp('(' [_0x4f02('0x1a5', 'VDpf')](_0x3a57f9, ')'))['test'](_0x557d25)) {
            var _0x4c2a9e, _0xc9b7ea = 'S+' === _0x3a57f9 ? _0x4f02('0x187', 'RmO]') : '00';
            _0x557d25 = _0x557d25[_0x4f02('0x1ae', '$#b@')](RegExp['$1'], 0x1 == RegExp['$1'][_0x4f02('0x21', 's4Uo')] ? _0x22de01[_0x3a57f9] : '' [_0x4f02('0x78', 'lLb0')](_0xc9b7ea)[_0x4f02('0x153', 'mu9&')](_0x22de01[_0x3a57f9])[_0x4f02('0x65', '!^Fw')]('' [_0x4f02('0x14c', 's4Uo')](_0x22de01[_0x3a57f9])[_0x4f02('0xe8', '!^Fw')]));
        }
    });
    return _0x557d25;
}
function Env(t, e) {
    "undefined" != typeof process && JSON.stringify(process.env).indexOf("GITHUB") > -1 && process.exit(0);
    class s {
        constructor(t) {
            this.env = t
        }
        send(t, e = "GET") {
            t = "string" == typeof t ? {
                url: t
            } : t;
            let s = this.get;
            return "POST" === e && (s = this.post), new Promise((e, i) => {
                s.call(this, t, (t, s, r) => {
                    t ? i(t) : e(s)
                })
            })
        }
        get(t) {
            return this.send.call(this.env, t)
        }
        post(t) {
            return this.send.call(this.env, t, "POST")
        }
    }
    return new class {
        constructor(t, e) {
            this.name = t, this.http = new s(this), this.data = null, this.dataFile = "box.dat", this.logs = [], this.isMute = !1, this.isNeedRewrite = !1, this.logSeparator = "\n", this.startTime = (new Date).getTime(), Object.assign(this, e), this.log("", `🔔${this.name}, 开始!`)
        }
        isNode() {
            return "undefined" != typeof module && !!module.exports
        }
        isQuanX() {
            return "undefined" != typeof $task
        }
        isSurge() {
            return "undefined" != typeof $httpClient && "undefined" == typeof $loon
        }
        isLoon() {
            return "undefined" != typeof $loon
        }
        toObj(t, e = null) {
            try {
                return JSON.parse(t)
            } catch {
                return e
            }
        }
        toStr(t, e = null) {
            try {
                return JSON.stringify(t)
            } catch {
                return e
            }
        }
        getjson(t, e) {
            let s = e;
            const i = this.getdata(t);
            if (i) try {
                s = JSON.parse(this.getdata(t))
            } catch {}
            return s
        }
        setjson(t, e) {
            try {
                return this.setdata(JSON.stringify(t), e)
            } catch {
                return !1
            }
        }
        getScript(t) {
            return new Promise(e => {
                this.get({
                    url: t
                }, (t, s, i) => e(i))
            })
        }
        runScript(t, e) {
            return new Promise(s => {
                let i = this.getdata("@chavy_boxjs_userCfgs.httpapi");
                i = i ? i.replace(/\n/g, "").trim() : i;
                let r = this.getdata("@chavy_boxjs_userCfgs.httpapi_timeout");
                r = r ? 1 * r : 20, r = e && e.timeout ? e.timeout : r;
                const [o, h] = i.split("@"), n = {
                    url: `http://${h}/v1/scripting/evaluate`,
                    body: {
                        script_text: t,
                        mock_type: "cron",
                        timeout: r
                    },
                    headers: {
                        "X-Key": o,
                        Accept: "*/*"
                    }
                };
                this.post(n, (t, e, i) => s(i))
            }).catch(t => this.logErr(t))
        }
        loaddata() {
            if (!this.isNode()) return {}; {
                this.fs = this.fs ? this.fs : require("fs"), this.path = this.path ? this.path : require("path");
                const t = this.path.resolve(this.dataFile),
                    e = this.path.resolve(process.cwd(), this.dataFile),
                    s = this.fs.existsSync(t),
                    i = !s && this.fs.existsSync(e);
                if (!s && !i) return {}; {
                    const i = s ? t : e;
                    try {
                        return JSON.parse(this.fs.readFileSync(i))
                    } catch (t) {
                        return {}
                    }
                }
            }
        }
        writedata() {
            if (this.isNode()) {
                this.fs = this.fs ? this.fs : require("fs"), this.path = this.path ? this.path : require("path");
                const t = this.path.resolve(this.dataFile),
                    e = this.path.resolve(process.cwd(), this.dataFile),
                    s = this.fs.existsSync(t),
                    i = !s && this.fs.existsSync(e),
                    r = JSON.stringify(this.data);
                s ? this.fs.writeFileSync(t, r) : i ? this.fs.writeFileSync(e, r) : this.fs.writeFileSync(t, r)
            }
        }
        lodash_get(t, e, s) {
            const i = e.replace(/\[(\d+)\]/g, ".$1").split(".");
            let r = t;
            for (const t of i)
                if (r = Object(r)[t], void 0 === r) return s;
            return r
        }
        lodash_set(t, e, s) {
            return Object(t) !== t ? t : (Array.isArray(e) || (e = e.toString().match(/[^.[\]]+/g) || []), e.slice(0, -1).reduce((t, s, i) => Object(t[s]) === t[s] ? t[s] : t[s] = Math.abs(e[i + 1]) >> 0 == +e[i + 1] ? [] : {}, t)[e[e.length - 1]] = s, t)
        }
        getdata(t) {
            let e = this.getval(t);
            if (/^@/.test(t)) {
                const [, s, i] = /^@(.*?)\.(.*?)$/.exec(t), r = s ? this.getval(s) : "";
                if (r) try {
                    const t = JSON.parse(r);
                    e = t ? this.lodash_get(t, i, "") : e
                } catch (t) {
                    e = ""
                }
            }
            return e
        }
        setdata(t, e) {
            let s = !1;
            if (/^@/.test(e)) {
                const [, i, r] = /^@(.*?)\.(.*?)$/.exec(e), o = this.getval(i), h = i ? "null" === o ? null : o || "{}" : "{}";
                try {
                    const e = JSON.parse(h);
                    this.lodash_set(e, r, t), s = this.setval(JSON.stringify(e), i)
                } catch (e) {
                    const o = {};
                    this.lodash_set(o, r, t), s = this.setval(JSON.stringify(o), i)
                }
            } else s = this.setval(t, e);
            return s
        }
        getval(t) {
            return this.isSurge() || this.isLoon() ? $persistentStore.read(t) : this.isQuanX() ? $prefs.valueForKey(t) : this.isNode() ? (this.data = this.loaddata(), this.data[t]) : this.data && this.data[t] || null
        }
        setval(t, e) {
            return this.isSurge() || this.isLoon() ? $persistentStore.write(t, e) : this.isQuanX() ? $prefs.setValueForKey(t, e) : this.isNode() ? (this.data = this.loaddata(), this.data[e] = t, this.writedata(), !0) : this.data && this.data[e] || null
        }
        initGotEnv(t) {
            this.got = this.got ? this.got : require("got"), this.cktough = this.cktough ? this.cktough : require("tough-cookie"), this.ckjar = this.ckjar ? this.ckjar : new this.cktough.CookieJar, t && (t.headers = t.headers ? t.headers : {}, void 0 === t.headers.Cookie && void 0 === t.cookieJar && (t.cookieJar = this.ckjar))
        }
        get(t, e = (() => {})) {
            t.headers && (delete t.headers["Content-Type"], delete t.headers["Content-Length"]), this.isSurge() || this.isLoon() ? (this.isSurge() && this.isNeedRewrite && (t.headers = t.headers || {}, Object.assign(t.headers, {
                "X-Surge-Skip-Scripting": !1
            })), $httpClient.get(t, (t, s, i) => {
                !t && s && (s.body = i, s.statusCode = s.status), e(t, s, i)
            })) : this.isQuanX() ? (this.isNeedRewrite && (t.opts = t.opts || {}, Object.assign(t.opts, {
                hints: !1
            })), $task.fetch(t).then(t => {
                const {
                    statusCode: s,
                    statusCode: i,
                    headers: r,
                    body: o
                } = t;
                e(null, {
                    status: s,
                    statusCode: i,
                    headers: r,
                    body: o
                }, o)
            }, t => e(t))) : this.isNode() && (this.initGotEnv(t), this.got(t).on("redirect", (t, e) => {
                try {
                    if (t.headers["set-cookie"]) {
                        const s = t.headers["set-cookie"].map(this.cktough.Cookie.parse).toString();
                        s && this.ckjar.setCookieSync(s, null), e.cookieJar = this.ckjar
                    }
                } catch (t) {
                    this.logErr(t)
                }
            }).then(t => {
                const {
                    statusCode: s,
                    statusCode: i,
                    headers: r,
                    body: o
                } = t;
                e(null, {
                    status: s,
                    statusCode: i,
                    headers: r,
                    body: o
                }, o)
            }, t => {
                const {
                    message: s,
                    response: i
                } = t;
                e(s, i, i && i.body)
            }))
        }
        post(t, e = (() => {})) {
            if (t.body && t.headers && !t.headers["Content-Type"] && (t.headers["Content-Type"] = "application/x-www-form-urlencoded"), t.headers && delete t.headers["Content-Length"], this.isSurge() || this.isLoon()) this.isSurge() && this.isNeedRewrite && (t.headers = t.headers || {}, Object.assign(t.headers, {
                "X-Surge-Skip-Scripting": !1
            })), $httpClient.post(t, (t, s, i) => {
                !t && s && (s.body = i, s.statusCode = s.status), e(t, s, i)
            });
            else if (this.isQuanX()) t.method = "POST", this.isNeedRewrite && (t.opts = t.opts || {}, Object.assign(t.opts, {
                hints: !1
            })), $task.fetch(t).then(t => {
                const {
                    statusCode: s,
                    statusCode: i,
                    headers: r,
                    body: o
                } = t;
                e(null, {
                    status: s,
                    statusCode: i,
                    headers: r,
                    body: o
                }, o)
            }, t => e(t));
            else if (this.isNode()) {
                this.initGotEnv(t);
                const {
                    url: s,
                    ...i
                } = t;
                this.got.post(s, i).then(t => {
                    const {
                        statusCode: s,
                        statusCode: i,
                        headers: r,
                        body: o
                    } = t;
                    e(null, {
                        status: s,
                        statusCode: i,
                        headers: r,
                        body: o
                    }, o)
                }, t => {
                    const {
                        message: s,
                        response: i
                    } = t;
                    e(s, i, i && i.body)
                })
            }
        }
        time(t, e = null) {
            const s = e ? new Date(e) : new Date;
            let i = {
                "M+": s.getMonth() + 1,
                "d+": s.getDate(),
                "H+": s.getHours(),
                "m+": s.getMinutes(),
                "s+": s.getSeconds(),
                "q+": Math.floor((s.getMonth() + 3) / 3),
                S: s.getMilliseconds()
            };
            /(y+)/.test(t) && (t = t.replace(RegExp.$1, (s.getFullYear() + "").substr(4 - RegExp.$1.length)));
            for (let e in i) new RegExp("(" + e + ")").test(t) && (t = t.replace(RegExp.$1, 1 == RegExp.$1.length ? i[e] : ("00" + i[e]).substr(("" + i[e]).length)));
            return t
        }
        msg(e = t, s = "", i = "", r) {
            const o = t => {
                if (!t) return t;
                if ("string" == typeof t) return this.isLoon() ? t : this.isQuanX() ? {
                    "open-url": t
                } : this.isSurge() ? {
                    url: t
                } : void 0;
                if ("object" == typeof t) {
                    if (this.isLoon()) {
                        let e = t.openUrl || t.url || t["open-url"],
                            s = t.mediaUrl || t["media-url"];
                        return {
                            openUrl: e,
                            mediaUrl: s
                        }
                    }
                    if (this.isQuanX()) {
                        let e = t["open-url"] || t.url || t.openUrl,
                            s = t["media-url"] || t.mediaUrl;
                        return {
                            "open-url": e,
                            "media-url": s
                        }
                    }
                    if (this.isSurge()) {
                        let e = t.url || t.openUrl || t["open-url"];
                        return {
                            url: e
                        }
                    }
                }
            };
            if (this.isMute || (this.isSurge() || this.isLoon() ? $notification.post(e, s, i, o(r)) : this.isQuanX() && $notify(e, s, i, o(r))), !this.isMuteLog) {
                let t = ["", "==============📣系统通知📣=============="];
                t.push(e), s && t.push(s), i && t.push(i), console.log(t.join("\n")), this.logs = this.logs.concat(t)
            }
        }
        log(...t) {
            t.length > 0 && (this.logs = [...this.logs, ...t]), console.log(t.join(this.logSeparator))
        }
        logErr(t, e) {
            const s = !this.isSurge() && !this.isQuanX() && !this.isLoon();
            s ? this.log("", `❗️${this.name}, 错误!`, t.stack) : this.log("", `❗️${this.name}, 错误!`, t)
        }
        wait(t) {
            return new Promise(e => setTimeout(e, t))
        }
        done(t = {}) {
            const e = (new Date).getTime(),
                s = (e - this.startTime) / 1e3;
            this.log("", `🔔${this.name}, 结束! 🕛 ${s} 秒`), this.log(), (this.isSurge() || this.isQuanX() || this.isLoon()) && $done(t)
        }
    }(t, e)
}