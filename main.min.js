_0xod2 = 'jsjiami.com.v6';
const $ = new Env('领取优惠券');
const nowVersion = "20250526";
console.log("当前版本" + nowVersion);
const cron = require('node-cron');
const fs = require('fs');
const path = require('path');
const util = require('util');
const {JSDOM} = require('jsdom');
const dom = new JSDOM('<!DOCTYPE html><html><body></body></html>', {
    'url': 'https://www.jd.com'
});
const window = dom.window;
const document = window.document;
global.window = window;
global.document = document;
global.Element = window.Element;
const configPath = path.join(__dirname, 'config.json');
// const smashUtils = require('./jdJm.js')['smashUtils'];
const { SmashUtils } = require('./SmashUtils.js');
let smashUtils = null;
async function initSmashUtils() {
    if (!smashUtils && cookiesArr && cookiesArr[0]) {
        const url = 'https://h5static.m.jd.com/mall/active/3aEzDU3fpqYYtnNTFPAkyY3tRY8Y/index.html';
        const cookie = cookiesArr[0];
        // const cookie = 'pt_key=AAJoLw9lADBxs4e1jDG4PoJ83ChDoVQCn7DgX-Q0uEnN4gMgcKBZS2L8kdBbcsrGqeuq0KH0xrM; pt_pin=jd_dlsyruocXPmz;';
        const ua = user_agent || "Mozilla/5.0 (iPhone; CPU iPhone OS 14_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.49(1800312d) NetType/4G Language/zh_CN";
        smashUtils = new SmashUtils(url, cookie, ua);
        await smashUtils.init({
            appid: "babel_zeEzM8yPWTLWju6YSj1aoNSvPfQ",
            sceneid: "babel_zeEzM8yPWTLWju6YSj1aoNSvPfQ",
            uid: "4f3a81b78bd21600d3a287edb48d425d"
        });
    }
}
const ParamsSignLite = require('./jdJm2.js');
$.CryptoJS = require('crypto-js');
let apiList = require('./jdYhqApiList.js').apiList;

// 尝试导入 jd-algorithm 包用于 H5ST 签名
let H5st = null;
try {
    const jdAlgorithm = require('jd-algorithm');
    H5st = jdAlgorithm.H5st;
    console.log('✅ 成功加载 jd-algorithm 包');
} catch (error) {
    console.log('⚠️  未找到 jd-algorithm 包，将使用备用签名方法');
    console.log('💡 如需使用 jd-algorithm，请运行: npm install jd-algorithm');
}
const USER_AGENTS = ['jdapp;android;10.1.0;10;network/wifi;Mozilla/5.0 (Linux; Android 10; ONEPLUS A5010 Build/QKQ1.191014.012; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/77.0.3865.120 MQQBrowser/6.2 TBS/045230 Mobile Safari/537.36', 'jdapp;iPhone;10.1.0;14.3;network/4g;Mozilla/5.0 (iPhone; CPU iPhone OS 14_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1', 'jdapp;android;10.1.0;9;network/4g;Mozilla/5.0 (Linux; Android 9; Mi Note 3 Build/PKQ1.181007.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/66.0.3359.126 MQQBrowser/6.2 TBS/045131 Mobile Safari/537.36', 'jdapp;android;10.1.0;10;network/wifi;Mozilla/5.0 (Linux; Android 10; GM1910 Build/QKQ1.190716.003; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/77.0.3865.120 MQQBrowser/6.2 TBS/045230 Mobile Safari/537.36', 'jdapp;android;10.1.0;9;network/wifi;Mozilla/5.0 (Linux; Android 9; 16T Build/PKQ1.190616.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/66.0.3359.126 MQQBrowser/6.2 TBS/044942 Mobile Safari/537.36', 'jdapp;iPhone;10.1.0;13.6;network/wifi;Mozilla/5.0 (iPhone; CPU iPhone OS 13_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1', 'jdapp;iPhone;10.1.0;13.6;network/wifi;Mozilla/5.0 (iPhone; CPU iPhone OS 13_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1', 'jdapp;iPhone;10.1.0;13.5;network/wifi;Mozilla/5.0 (iPhone; CPU iPhone OS 13_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1', 'jdapp;iPhone;10.1.0;14.1;network/wifi;Mozilla/5.0 (iPhone; CPU iPhone OS 14_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1', 'jdapp;iPhone;10.1.0;13.3;network/wifi;Mozilla/5.0 (iPhone; CPU iPhone OS 13_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1', 'jdapp;iPhone;10.1.0;13.7;network/wifi;Mozilla/5.0 (iPhone; CPU iPhone OS 13_7 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1', 'jdapp;iPhone;10.1.0;14.1;network/wifi;Mozilla/5.0 (iPhone; CPU iPhone OS 14_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1', 'jdapp;iPhone;10.1.0;13.3;network/wifi;Mozilla/5.0 (iPhone; CPU iPhone OS 13_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1', 'jdapp;iPhone;10.1.0;13.4;network/wifi;Mozilla/5.0 (iPhone; CPU iPhone OS 13_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1', 'jdapp;iPhone;10.1.0;14.3;network/wifi;Mozilla/5.0 (iPhone; CPU iPhone OS 14_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1', 'jdapp;android;10.1.0;9;network/wifi;Mozilla/5.0 (Linux; Android 9; MI 6 Build/PKQ1.190118.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/66.0.3359.126 MQQBrowser/6.2 TBS/044942 Mobile Safari/537.36', 'jdapp;android;10.1.0;11;network/wifi;Mozilla/5.0 (Linux; Android 11; Redmi K30 5G Build/RKQ1.200826.002; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/77.0.3865.120 MQQBrowser/6.2 TBS/045511 Mobile Safari/537.36', 'jdapp;iPhone;10.1.0;11.4;network/wifi;Mozilla/5.0 (iPhone; CPU iPhone OS 11_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15F79', 'jdapp;android;10.1.0;10;;network/wifi;Mozilla/5.0 (Linux; Android 10; M2006J10C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/77.0.3865.120 MQQBrowser/6.2 TBS/045230 Mobile Safari/537.36', 'jdapp;android;10.1.0;10;network/wifi;Mozilla/5.0 (Linux; Android 10; M2006J10C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/77.0.3865.120 MQQBrowser/6.2 TBS/045230 Mobile Safari/537.36', 'jdapp;android;10.1.0;10;network/wifi;Mozilla/5.0 (Linux; Android 10; ONEPLUS A6000 Build/QKQ1.190716.003; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/77.0.3865.120 MQQBrowser/6.2 TBS/045224 Mobile Safari/537.36', 'jdapp;android;10.1.0;9;network/wifi;Mozilla/5.0 (Linux; Android 9; MHA-AL00 Build/HUAWEIMHA-AL00; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/66.0.3359.126 MQQBrowser/6.2 TBS/044942 Mobile Safari/537.36', 'jdapp;android;10.1.0;8.1.0;network/wifi;Mozilla/5.0 (Linux; Android 8.1.0; 16 X Build/OPM1.171019.026; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/66.0.3359.126 MQQBrowser/6.2 TBS/044942 Mobile Safari/537.36', 'jdapp;android;10.1.0;8.0.0;network/wifi;Mozilla/5.0 (Linux; Android 8.0.0; HTC U-3w Build/OPR6.170623.013; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/66.0.3359.126 MQQBrowser/6.2 TBS/044942 Mobile Safari/537.36', 'jdapp;iPhone;10.1.0;14.0.1;network/wifi;Mozilla/5.0 (iPhone; CPU iPhone OS 14_0_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1', 'jdapp;android;10.1.0;10;network/wifi;Mozilla/5.0 (Linux; Android 10; LYA-AL00 Build/HUAWEILYA-AL00L; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/77.0.3865.120 MQQBrowser/6.2 TBS/045230 Mobile Safari/537.36', 'jdapp;iPhone;10.1.0;14.2;network/wifi;Mozilla/5.0 (iPhone; CPU iPhone OS 14_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1', 'jdapp;iPhone;10.1.0;14.3;network/wifi;Mozilla/5.0 (iPhone; CPU iPhone OS 14_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1', 'jdapp;iPhone;10.1.0;14.2;network/wifi;Mozilla/5.0 (iPhone; CPU iPhone OS 14_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1', 'jdapp;android;10.1.0;8.1.0;network/wifi;Mozilla/5.0 (Linux; Android 8.1.0; MI 8 Build/OPM1.171019.026; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/66.0.3359.126 MQQBrowser/6.2 TBS/045131 Mobile Safari/537.36', 'jdapp;android;10.1.0;10;network/wifi;Mozilla/5.0 (Linux; Android 10; Redmi K20 Pro Premium Edition Build/QKQ1.190825.002; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/77.0.3865.120 MQQBrowser/6.2 TBS/045227 Mobile Safari/537.36', 'jdapp;iPhone;10.1.0;14.3;network/wifi;Mozilla/5.0 (iPhone; CPU iPhone OS 14_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1', 'jdapp;iPhone;10.1.0;14.3;network/4g;Mozilla/5.0 (iPhone; CPU iPhone OS 14_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1', 'jdapp;android;10.1.0;11;network/wifi;Mozilla/5.0 (Linux; Android 11; Redmi K20 Pro Premium Edition Build/RKQ1.200826.002; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/77.0.3865.120 MQQBrowser/6.2 TBS/045513 Mobile Safari/537.36', 'jdapp;android;10.1.0;10;network/wifi;Mozilla/5.0 (Linux; Android 10; MI 8 Build/QKQ1.190828.002; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/77.0.3865.120 MQQBrowser/6.2 TBS/045227 Mobile Safari/537.36', 'jdapp;iPhone;10.1.0;14.1;network/wifi;Mozilla/5.0 (iPhone; CPU iPhone OS 14_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1'];
function randomNumber(min = 0, max = 64) {
    return Math.min(Math.floor(min + Math.random() * (max - min)), max);
}
let config = {};
try {
    config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
} catch (error) {
    console.error('config.json文件读取失败，请检查文件格式否正确！');
    return;
}
const logDir = path.join(__dirname, 'log');
if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir);
}
const logFileName = 'app_' + timeFormat(new Date(), 'date') + '.log';
const logFilePath = path.join(logDir, logFileName);
const originalConsoleLog = console.log;
console.log = function(...args) {
    const formattedMessage = util.format(...args) + '';
    fs.appendFileSync(logFilePath, formattedMessage);
    originalConsoleLog.apply(console, args);
};
const originalConsoleError = console.error;
console.error = function(...args) {
    const formattedMessage = util.format(...args) + '';
    fs.appendFileSync(logFilePath, formattedMessage);
    originalConsoleError.apply(console, args);
};
let tryNum = 4; // 重试次数
let maxQq = 14; // 最大优惠券数量?
let maxXc = 3; // 最大并发数?
let qqjgTime = 250; // 请求间隔时间 毫秒
let maxAccount = 8; // 最大账号数?
let ycTime = 64; // 延迟时间?
let cookiesArr = [], // Cookies
    cookie = '';
let canTaskFlag = []; // 任务标志?
let TgCkArray = []; // 跳过的Cookie索引?
let lqSucArray = []; // 领取成功的用户?
let AllEendCode = '|A9|A6|A14|D2|'; // 全部结束代码?
let PEendCode = '|A1|A12|A13|A19|A26|'; // 部分结束代码?
let JDTimes = new Date().getTime(); // 服务器时间戳
let JDTimeJg = 0; // 本地、服务器时间差 毫秒
let nextHour = 0; // 下一个整点小时 用于判断抢券时间

let apiArray = []; // 当前需要执行的优惠券API列表
let nowIndex = 0;
let yhqAPiHasSuccess = {}; // 领取成功记录

let ckerror = []; // cookie过期的账号
let removeYhq = []; // 排除的优惠券
let nowRunYhq = ''; // 马上执行的优惠券名称
let user_agent = ''; // UA
let paramsSignLiteMy = '';
if (config.JD_COOKIE) {
    cookiesArr = config.JD_COOKIE;
} else {
    console.log('【提示】请先添加JD_COOKIE');
    return false;
} if (config.YHQ_REMOVE && config.YHQ_REMOVE.split(',').length >= 1) {
    if (config.YHQ_REMOVE.toLowerCase() == 'all') {
        console.log('读取环境变量排除的优惠券为：不抢作者所有的券！');
        apiList = [];
    } else {
        console.log('读取环境变量排除的优惠券为：' + config.YHQ_REMOVE);
        removeYhq = config.YHQ_REMOVE.split(',');
    }
}
if (config.YHQ_NOWRUN) {
    console.log('读取环境变量立即执行优惠券为：' + config.YHQ_NOWRUN);
    nowRunYhq = config.YHQ_NOWRUN;
}
try {
    const apiListMy = require('./jdYhqApiListMy.js').apiList;
    if (apiListMy.length > 0) {
        for (var alm in apiListMy) {
            if (apiListMy[alm].qName && apiListMy[alm].qApi && apiListMy[alm].qTime) {
                apiList.push(apiListMy[alm]);
                console.log('加载自定义API:' + apiListMy[alm].qName);
            }
        }
    }
} catch (_6007a2) {
    console.log('未配置自定义API！');
}
try {
    paramsSignLiteMy = new window[('ParamsSignLite')]({
        'appId': '35fa0',
        'preRequest': !1
    });
} catch (error) {}
if (config.YHQ_API && config.YHQ_API.indexOf(',') > -1 && config.YHQ_API.split(',').length >= 5) {
    console.log('读取环境变量成功：' + config.YHQ_API);
    let YHQ_API_ARR = config.YHQ_API.split(',');
    tryNum = parseInt(YHQ_API_ARR[0]);
    if (parseInt(YHQ_API_ARR[1]) > maxQq) {
        maxQq = parseInt(YHQ_API_ARR[1]);
    }
    maxXc = parseInt(YHQ_API_ARR[2]);
    qqjgTime = parseInt(YHQ_API_ARR[3]);
    maxAccount = parseInt(YHQ_API_ARR[4]);
    if (YHQ_API_ARR.length >= 6) {
        ycTime = parseInt(YHQ_API_ARR[5]);
    }
}
console.log('' + timeFormat() + ':' + '----脚本运行成功，请不要关闭窗口----');
let isMainRunning = false;
async function executeMain() {
    if (isMainRunning) {
        console.log('任务执行中...');
        return;
    }
    isMainRunning = true;
    try {
        resertCs();
        await main();
    } catch (error) {
        console.error('main 函数执行失败:', error);
    } finally {
        isMainRunning = false;
    }
}
executeMain();
cron.schedule('* * * * *', () => {
    try {
        const currentMinute = new Date().getMinutes();
        if (currentMinute === 59) {
            executeMain();
        } else {
            if (!isMainRunning && currentMinute % 5 === 0) {
                console.log('' + timeFormat() + ':' + '未到任务执行时间，跳过执行......');
            }
        }
    } catch (error) {}
});
async function main() {
    try {
        if (!cookiesArr[0]) {
            console.log('【提示】请先增加JD账号一cookie');
            return;
        } else {
            console.log('共检测到' + cookiesArr.length + '个cookie');
        }
        // 初始化 SmashUtils
        await initSmashUtils();
        if (new Date().getDate() == 1 && new Date().getHours() == 0) {
            $.setjson({}, 'yhqAPiHasSuccess');
            console.log('清空缓存！');
        }
        nextHour = nextHourF();
        console.log('下次抢券时间：' + nextHour + ':00:00');
        user_agent = USER_AGENTS[randomNumber(0, USER_AGENTS.length)];
        for (var apiIndex in apiList) {
            if (nowRunYhq && nowRunYhq.length > 0 && nowRunYhq == apiList[apiIndex].qName) {
                console.log('立即抢券（跑完记得删除或禁用该环境变量）：' + apiList[apiIndex].qName);
                apiArray.push(apiList[apiIndex]);
                doAPIList(apiArray.length - 1);
                continue;
            }
            if (checkYhq(apiList[apiIndex], nextHour) && !isRemoveYhqF(apiList[apiIndex]) && apiArray.length < maxQq) {
                apiArray.push(apiList[apiIndex]);
                console.log('名称：' + apiList[apiIndex].qName);
            }
        }
        if (apiArray.length <= 0) {
            console.log('当前时间段没有优惠券需要领取！');
            return;
        }
        if ($.getdata('JDTimeJg') && $.getdata('JDTimeJg') != 0) {
            JDTimeJg = $.getdata('JDTimeJg');
        }
        if ($.getjson('yhqAPiHasSuccess')) {
            yhqAPiHasSuccess = $.getjson('yhqAPiHasSuccess');
        }
        let waitTime = jgNextHourF() + JDTimeJg - ycTime;
        if (waitTime > 2 * 60 * 1000) {
            console.log(parseInt(waitTime / 60 / 1000) + '分后才开始！');
            return;
        }
        if (waitTime > 0) {
            console.log(parseInt(waitTime / 60 / 1000) + '分后开始任务，请不要结束任务！');
            await $.wait(waitTime);
        }
        for (let apiIndex in apiArray) {
            if (!yhqAPiHasSuccess[apiArray[apiIndex].qName]) {
                yhqAPiHasSuccess[apiArray[apiIndex].qName] = {};
            }
            doAPIList(apiIndex);
        }
        await $.wait(3 * 1000);
        for (let apiIndex in apiArray) {
            let successMessage = '';
            if (lqSucArray[apiIndex].length > 0) {
                if (apiArray[apiIndex].qName) {
                    successMessage += '券【' + apiArray[apiIndex].qName + '】';
                }
                successMessage += '成功领取的用户有：';
                for (var successIndex in lqSucArray[apiIndex]) {
                    cookie = cookiesArr[lqSucArray[apiIndex][successIndex]];
                    let username = decodeURIComponent(cookie.match(/pt_pin=([^; ]+)(?=;?)/) && cookie.match(/pt_pin=([^; ]+)(?=;?)/)[1]);
                    successMessage += '' + (lqSucArray[apiIndex][successIndex] + 1) + '、' + username;
                }
                console.log('************************');
                console.log(successMessage);
            }
            if (successMessage) {
                myNotice(successMessage);
                console.log(successMessage);
                successMessage = '';
            }
        }
        $.setjson(yhqAPiHasSuccess, 'yhqAPiHasSuccess');
    } catch (error) {
        console.error('main function error:', error);
    }
}
function resertCs() {
    canTaskFlag = [];
    TgCkArray = [];
    lqSucArray = [];
    apiArray = [];
    nowIndex = 0;
    yhqAPiHasSuccess = {};
}
async function doAPIList(apiIndex) {
    canTaskFlag[apiIndex] = true;
    TgCkArray[apiIndex] = [];
    lqSucArray[apiIndex] = [];

    for (let tryCount = 1; tryCount <= tryNum; tryCount++) {
        if (
            canTaskFlag[apiIndex] &&
            TgCkArray[apiIndex].length < cookiesArr.length &&
            TgCkArray[apiIndex].length < maxAccount
        ) {
            console.log('***开始领券【' + apiArray[apiIndex].name + '】第' + tryCount + '次请求***');

            for (let cookieIndex = 0; cookieIndex < cookiesArr.length && cookieIndex < maxAccount; cookieIndex++) {
                let targetCookieIndex = apiArray[apiIndex].ckIndex ? apiArray[apiIndex].ckIndex : 0;

                // 如果设置了 ckIndex，则只运行特定账号
                if (targetCookieIndex > 0) {
                    if (cookieIndex + 1 < targetCookieIndex) {
                        continue;
                    } else if (cookieIndex + 1 > targetCookieIndex) {
                        break;
                    } else {
                        console.log('开始执行账号' + targetCookieIndex + '专属ck:');
                    }
                }

                if (canTaskFlag[apiIndex]) {
                    if (cookiesArr[cookieIndex]) {
                        const usernameMatch = cookiesArr[cookieIndex].match(/pt_pin=([^; ]+)(?=;?)/);
                        const username = usernameMatch ? decodeURIComponent(usernameMatch[1]) : '未知用户';

                        if (TgCkArray[apiIndex].includes(cookieIndex)) {
                            console.log('跳过账号' + (cookieIndex + 1) + ':' + username + '！');
                            continue;
                        }

                        try {
                            if (yhqAPiHasSuccess[apiArray[apiIndex].name][username] && nextHour !== '0') {
                                const nowDate = getNowDate();
                                const lastSuccessTime = yhqAPiHasSuccess[apiArray[apiIndex].name][username];

                                if (DateDiff(nowDate, lastSuccessTime) < apiArray[apiIndex].lqSpace) {
                                    console.log('其他时间领取成功，跳过账号' + (cookieIndex + 1) + ':' + username + '！');
                                    TgCkArray[apiIndex].push(cookieIndex);
                                    continue;
                                }
                            }
                        } catch (error) {}

                        nowIndex++;

                        if (nowIndex >= maxXc) {
                            if (nowIndex % maxXc === 0) {
                                await $.wait(qqjgTime - 14);
                            } else {
                                await $.wait(10);
                            }
                        }

                        doApiTask(apiIndex, cookieIndex);
                    }
                } else {
                    console.log('该券已无或者无账号需要请求！');
                    break;
                }
            }
        } else {
            break;
        }
    }
}
async function doApiTask(apiIndex, cookieIndex) {
    console.log(nowIndex + '、' + timeFormat() + ':开始领取' + apiArray[apiIndex].qName + '_账号' + (cookieIndex + 1));
    return new Promise(async resolve => {
        if (canTaskFlag[apiIndex]) {
            // 保留原始qName和qApi的判断逻辑
            if (apiArray[apiIndex].qName.indexOf('G') > -1
                || apiArray[apiIndex].qApi.indexOf('https://s.m.jd.com') > -1
                || apiArray[apiIndex].qApi.indexOf('h5_awake_wxapp') > -1) {

                // GET请求分支（保留原始getApiUrlGet调用）
                const getApiConfig = await getApiUrlGet(apiIndex, cookieIndex);
                $.get(getApiConfig, (getError, getResponse, getBody) => {
                    try {
                        if (getError) {
                            console.log('API请求失败，请检查网络重试');
                        } else {
                            const cookie = cookiesArr[cookieIndex];
                            const ptPin = decodeURIComponent(cookie.match(/pt_pin=([^; ]+)(?=;?)/)?.[1] || '');
                            console.log(`*${apiArray[apiIndex].qName}_【账号${cookieIndex + 1}】${ptPin}*`);
                            console.log(timeFormat() + ':' + getBody);

                            // 保留原始成功判断逻辑
                            if (getBody.indexOf('成功') > -1) {
                                lqSucArray[apiIndex].push(cookieIndex);
                                yhqAPiHasSuccess[apiArray[apiIndex].qName][ptPin] = getNowDate();
                            } else if (getBody.indexOf('再来') > -1 || getBody.indexOf('抢光') > -1) {
                                canTaskFlag[apiIndex] = false;
                            }
                        }
                    } catch (error) {
                        TgCkArray[apiIndex].push(cookieIndex);
                        $.logErr(error, getResponse);
                    } finally {
                        resolve(getBody);
                    }
                });

            } else {
                // POST请求分支（保留原始getApiUrl调用）
                const postApiConfig = await getApiUrl(apiIndex, cookieIndex);
                $.post(postApiConfig, (postError, postResponse, postBody) => {
                    try {
                        if (postError) {
                            console.log(JSON.stringify(postError));
                            console.log('API请求失败，请检查网络重试');
                        } else {
                            const cookie = cookiesArr[cookieIndex];
                            const ptPin = decodeURIComponent(cookie.match(/pt_pin=([^; ]+)(?=;?)/)?.[1] || '');
                            console.log(`*${apiArray[apiIndex].qName}_【账号${cookieIndex + 1}】${ptPin}*`);

                            // 保留原始响应解析逻辑
                            const parsedBody = JSON.parse(postBody);
                            let subCodeWithPipes = '';
                            let message = '';
                            try {
                                subCodeWithPipes = '|' + parsedBody.subCode + '|';
                                message = parsedBody.subCodeMsg || parsedBody.resultData?.msg;
                            } catch (_) {}

                            // 保留原始成功判断逻辑
                            if ((parsedBody.subCode === 'A1' || parsedBody.subCode === '0')
                                || message?.includes('成功')) {
                                lqSucArray[apiIndex].push(cookieIndex);
                                yhqAPiHasSuccess[apiArray[apiIndex].qName][ptPin] = getNowDate();
                            }

                            // 保留原始状态码处理逻辑
                            if (AllEendCode.includes(subCodeWithPipes)) {
                                if (parsedBody.subCode === 'D2' && message?.substr(message.indexOf('请') + 1, 2) === nextHour) {
                                    console.log(timeFormat() + ':时间未到继续：' + message);
                                } else if (nextHour == 0) {
                                    console.log(timeFormat() + ':继续：' + message);
                                } else {
                                    canTaskFlag[apiIndex] = false;
                                    console.log(timeFormat() + ':' + message);
                                }
                            } else if (PEendCode.includes(subCodeWithPipes)) {
                                TgCkArray[apiIndex].push(cookieIndex);
                                console.log(timeFormat() + ':' + message + ',subCode2_' + subCodeWithPipes);
                            } else if (parsedBody.code === '3') {
                                TgCkArray[apiIndex].push(cookieIndex);
                                console.log(timeFormat() + ':ck过期！');
                                if (!checkHasCz(ckerror, cookieIndex)) {
                                    ckerror.push(cookieIndex);
                                    myNotice('【账号' + (cookieIndex + 1) + '】' + ptPin + '——ck过期!');
                                    console.error('【账号' + (cookieIndex + 1) + '】' + ptPin + '——ck过期!');
                                }
                            } else {
                                console.log(timeFormat() + ':' + JSON.stringify(parsedBody));
                            }
                        }
                    } catch (error) {
                        TgCkArray[apiIndex].push(cookieIndex);
                        $.logErr(error, postResponse);
                    } finally {
                        resolve(postBody);
                    }
                });
            }
        } else {
            console.log('该券已无或已结束！');
        }
    });
}
function getJDTime() {
    return new Promise(resolve => {
        $.post({
            'url': 'https://api.m.jd.com/client.action?functionId=queryMaterialProducts&client=wh5'
        }, async(error, response, body) => {
            try {
                if (error) {
                    console.log('获取JD时间失败');
                } else {
                    body = JSON.parse(body);
                    if (body.code && body.code == '0') {
                        JDTimes = parseInt(body.currentTime2);
                        if (JDTimeJg == 0 || JDTimeJg != 0 && new Date().getTime() - JDTimes < JDTimeJg) {
                            JDTimeJg = new Date().getTime() - JDTimes;
                        }
                    } else {
                        console.log('获取JD时间失败:' + JSON.stringify(body));
                    }
                }
            } catch (error) {
                $.logErr(error, response);
            } finally {
                resolve(body);
            }
        });
    });
}
function checkYhq(couponInfo, targetHour) {
    if (!couponInfo.endDate) {
        return true;
    }
    if (couponInfo.endDate && couponInfo.qTime && new Date(couponInfo.endDate + ' 23:59:59').getTime() > new Date().getTime()) {
        let timeArray = couponInfo.qTime.split(',');
        if (timeArray.length > 0 && timeArray.includes(targetHour + '')) {
            return true;
        }
    }
    return false;
}
function isRemoveYhqF(couponInfo) {
    let shouldRemove = false;
    if (removeYhq && removeYhq.length > 0) {
        for (var removeIndex in removeYhq) {
            if (couponInfo.qName == removeYhq[removeIndex]) {
                console.log('排除优惠券：' + couponInfo.qName);
                shouldRemove = true;
                break;
            }
        }
    }
    return shouldRemove;
}
async function getApiUrl(apiIndex, cookieIndex) {
    const apiLogResult = await getApiLog(apiArray[apiIndex].qApi);
    const decryptedUrl = await getDecryptUrlTy(apiLogResult);
    return {
        'url': decryptedUrl,
        'headers': {
            'user-agent': user_agent,
            'content-Type': 'application/x-www-form-urlencoded',
            'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
            'accept-encoding': 'gzip, deflate, br',
            'accept-language': 'zh-CN,zh;q=0.9',
            'cache-control': 'max-age=0',
            'cookie': cookiesArr[cookieIndex]
        }
    };
}
async function getApiUrlGet(apiIndex, cookieIndex) {
    if (apiArray[apiIndex].qApi.indexOf('https://s.m.jd.com') > -1 || apiArray[apiIndex].qApi.indexOf('h5_awake_wxapp') > -1) {
        const apiLogResult = await getApiLog(apiArray[apiIndex].qApi);
        const decryptedUrl = await getDecryptUrlTy(apiLogResult);
        return {
            'url': decryptedUrl,
            'headers': {
                'User-Agent': user_agent,
                'Cookie': cookiesArr[0],
                'Accept': '*/*',
                'Accept-Encoding': 'gzip, deflate, br',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Referer': 'https://coupon.m.jd.com/'
            }
        };
    } else if (apiArray[apiIndex].qApi.indexOf('appid=plus_business') > -1) {
        return {
            'url': apiArray[apiIndex].qApi,
            'headers': {
                'User-Agent': user_agent,
                'accept-encoding': 'gzip, deflate, br',
                'accept-language': 'zh-CN,zh;q=0.9',
                'Cookie': cookiesArr[cookieIndex],
                'origin': 'https://plus.m.jd.com'
            }
        };
    } else {
        return {
            'url': apiArray[apiIndex].qApi,
            'headers': {
                'User-Agent': user_agent,
                'accept-encoding': 'gzip, deflate, br',
                'accept-language': 'zh-CN,zh;q=0.9',
                'Cookie': cookiesArr[cookieIndex]
            }
        };
    }
}
function jgNextHourF() {
    // 获取当前时间的格式化字符串（例如 "2025-04-05 12"），截取前13个字符
    const currentHourStr = timeFormat().substr(0, 13); // 0x0d == 13

    // 构造下一个整点的时间字符串："YYYY-MM-DD HH:00:00"
    const nextHourStr = currentHourStr + ':00:00';

    // 解析为时间戳，并加上一小时（当前小时的整点时间）
    const nextHourTimestamp = Date.parse(new Date(nextHourStr)) + 60 * 60 * 1000;

    // 返回距离下一整点还有多少毫秒
    return nextHourTimestamp - new Date().getTime();
}
function myNotice(_16de16) {}
function nextHourF() {
    const currentDate = new Date();
    return currentDate.getHours() + 1 >= 18 ? 0 : currentDate.getHours() + 1;
}
function DateDiff(dateString1, dateString2) {
    var parts, date1, date2, diffInHalfDays;

    // 分割字符串并重组为 "MM-DD-YYYY" 格式创建日期对象
    parts = dateString1.split('-');
    date1 = new Date(parts[1] + '-' + parts[2] + '-' + parts[0]);

    parts = dateString2.split('-');
    date2 = new Date(parts[1] + '-' + parts[2] + '-' + parts[0]);

    // 计算两个日期之间的毫秒差，转换为“每18小时”的单位
    diffInHalfDays = parseInt(Math.abs(date1 - date2) / 1000 / 60 / 60 / 18);

    return diffInHalfDays;
}
function getNowDate() {
    const currentDate = new Date();
    const year = currentDate.getFullYear();
    const month = (currentDate.getMonth() + 1).toString().padStart(2, '0');
    const day = currentDate.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
}

function timeFormat(timestampOrDate, formatType) {
    let dateObject;

    // 处理输入时间
    if (timestampOrDate) {
        dateObject = new Date(timestampOrDate);
    } else {
        dateObject = new Date(); // 默认当前时间
    }

    // 生成日期部分字符串
    const year = dateObject.getFullYear();
    const month = dateObject.getMonth() + 1; // 月份从0开始需+1
    const day = dateObject.getDate();

    // 日期格式：YYYY-MM-DD
    if (formatType === 'date') {
        return `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
    }

    // 生成时间部分
    const hours = dateObject.getHours();
    const minutes = dateObject.getMinutes();
    const seconds = dateObject.getSeconds();
    const milliseconds = dateObject.getMilliseconds();

    // 完整格式：YYYY-MM-DD HH:mm:ss:ms
    return `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')} ` +
           `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:` +
           `${seconds.toString().padStart(2, '0')}:${milliseconds}`;
}


async function getApiLog(originalParams) {
    const randomStr = smashUtils.getRandom(8);

    try {
        const riskResult = await smashUtils.get_risk_result({
            id: 'coupon',
            data: { random: randomStr }
        }, "") || {};

        const logValue = riskResult.log || '';
        const encodedLog = encodeURIComponent(`,"log":"${logValue}","random":"${randomStr}"`);

        // 处理URL尾部的%7D（即编码后的'}'）
        if (originalParams && originalParams.includes('%7D')) {
            const endIndex = originalParams.indexOf('%7D');
            return originalParams.substring(0, endIndex) + encodedLog + originalParams.substring(endIndex);
        }
        return encodedLog;
    } catch (error) {
        console.error('生成API日志失败:', error);
        return originalParams || '';
    }
}

function checkHasCz(targetArray, searchValue) {
    if (!targetArray) return false;

    for (const item of targetArray) {
        if (item === searchValue) {
            return true;
        }
    }
    return false;
}
/**
 * 从URL中提取指定查询参数的值
 * @param {string} url - 完整的URL字符串
 * @param {string} paramName - 要提取的参数名称
 * @returns {string} 参数值（已URI解码）
 */
function getUrlQueryParams(url, paramName) {
    // 构造匹配参数的正则表达式
    const paramRegex = new RegExp(`(^|&)${paramName}=([^&]*)(&|$)`, 'i');

    // 分割URL基础路径和查询参数部分
    const urlParts = url.split('?');

    // 如果没有查询参数部分，直接返回空字符串
    if (urlParts.length < 2) return '';

    // 在查询参数部分匹配目标参数
    const paramMatch = urlParts[1].substr(0).match(paramRegex);

    return paramMatch ? decodeURIComponent(paramMatch[2]) : '';
}

/**
 * 计算字符串的SHA256哈希值（十六进制格式）
 * @param {string} inputString - 输入字符串
 * @returns {string} 64字符的十六进制哈希值
 */
function sha256Hash(inputString) {
    // 文本编码器实例（实际未使用，可删除）
    const textEncoder = new TextEncoder();

    // 使用CryptoJS计算SHA256哈希
    const hashResult = $.CryptoJS.SHA256(
        $.CryptoJS.enc.Utf8.parse(inputString)
    );

    // 转换为十六进制字符串
    const hexHash = hashResult.toString($.CryptoJS.enc.Hex);

    return hexHash;
}
function getDecryptUrlTy(originalUrl) {
    return new Promise(async (resolve, reject) => {
        try {
            // 对URL中的body参数进行SHA256哈希
            const hashedBody = sha256Hash(getUrlQueryParams(originalUrl, 'body'));

            // 构造签名参数对象
            const signParams = {
                'appid': 'babelh5',
                'body': hashedBody,
                'client': 'wh5',
                'clientVersion': '1.0.0',
                'functionId': 'newBabelAwardCollection'
            };

            // 优先使用 jd-algorithm 包的 h5st 算法
            if (H5st) {
                try {
                    console.log('🔧 使用 jd-algorithm 进行签名...');
                    const h5st = new H5st({
                        appId: signParams.appid,
                        appname: 'com.jingdong.app.mall',
                        clientVersion: signParams.clientVersion,
                        client: signParams.client,
                        pin: '', // 可以为空
                        ua: '', // 可以为空
                    });

                    // 生成 h5st 签名
                    const signatureResult = await h5st.genAlgo(signParams.functionId, signParams.body);

                    if (signatureResult && signatureResult.h5st) {
                        // 拼接原始URL和签名后的h5st参数
                        const signedUrl = `${originalUrl}&h5st=${signatureResult.h5st}`;
                        console.log('✅ jd-algorithm 签名成功');
                        resolve(signedUrl);
                        return;
                    } else {
                        console.log('⚠️  jd-algorithm 签名失败，回退到备用方法');
                    }
                } catch (error) {
                    console.log('⚠️  jd-algorithm 签名出错，回退到备用方法:', error.message);
                }
            }

            // 回退到原来的 paramsSignLiteMy 方法
            if (paramsSignLiteMy && typeof paramsSignLiteMy.sign === 'function') {
                console.log('🔧 使用备用签名方法...');
                paramsSignLiteMy.sign(signParams)
                    .then(signatureResult => {
                        const signedUrl = `${originalUrl}&h5st=${signatureResult.h5st}`;
                        console.log('✅ 备用签名成功');
                        resolve(signedUrl);
                    })
                    .catch(error => {
                        console.error('❌ 备用签名失败:', error);
                        resolve(originalUrl); // 失败时返回原始URL
                    });
            } else {
                console.log('⚠️  没有可用的签名方法，返回原始URL');
                resolve(originalUrl);
            }
        } catch (error) {
            console.error('❌ 签名过程出错:', error);
            resolve(originalUrl); // 失败时返回原始URL
        }
    });
}
/**
 * 获取解密后的URL（添加签名参数）
 * @param {string} originalUrl - 原始URL
 * @returns {string} 添加了签名参数的URL
 */
function getDecryptUrl(originalUrl) {
    // 添加时间戳参数
    originalUrl = originalUrl + '&t=' + Date.now();

    // 从URL中提取_stk参数（签名字段列表）
    stk = getUrlQueryParams(originalUrl, '_stk');

    if (stk) {
        // 生成时间戳字符串（格式：yyyyMMddhhmmssSSS）
        const timestampStr = format('yyyyMMddhhmmssSSS', Date.now());

        // 生成签名密钥
        const signKey = $.genKey($.token, $.fp.toString(), timestampStr.toString(), $.appId.toString(), $.CryptoJS).toString($.CryptoJS.enc.Hex);

        // 构建签名字符串
        let signatureString = '';
        stk.split(',').map((fieldName, index) => {
            // 格式：字段名:字段值&字段名:字段值...
            signatureString += fieldName + ':' + getUrlQueryParams(originalUrl, fieldName) + (index === stk.split(',').length - 1 ? '' : '&');
        });

        // 使用HmacSHA256生成最终签名
        const finalSignature = $.CryptoJS.HmacSHA256(signatureString, signKey.toString()).toString($.CryptoJS.enc.Hex);

        // 返回带有h5st签名参数的完整URL
        return originalUrl + '&h5st=' + encodeURIComponent([
            ''.concat(timestampStr.toString()),     // 时间戳
            ''.concat($.fp.toString()),             // 指纹
            ''.concat($.appId.toString()),          // 应用ID
            ''.concat($.token),                     // 令牌
            ''.concat(finalSignature),              // 签名
            '3.0;'.concat(timestampStr)             // 版本号和时间戳
        ].join(';')) + '&__t=' + Date.now();
    }
}
/**
 * 请求算法接口，获取签名所需的token和genKey函数
 * @returns {Promise} 返回Promise对象
 */
async function requestAlgo() {
    // 设置应用ID
    $.appId = '8ba9b';

    // 生成指纹：13位随机数字 + 当前时间戳，取前10位
    $.fp = (getRandomIDPro({
        'size': 13  // 0xd = 13
    }) + Date.now()).slice(0, 10);

    // 构建请求配置
    const requestConfig = {
        'url': 'https://cactus.jd.com/request_algo?g_ty=ajax',
        'headers': {
            'Authority': 'cactus.jd.com',
            'Pragma': 'no-cache',
            'Cache-Control': 'no-cache',
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'Origin': 'https://st.jingxi.com',
            'Sec-Fetch-Site': 'cross-site',
            'User-Agent': $.user_agent,
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'Referer': 'https://st.jingxi.com/',
            'Accept-Language': 'zh-CN,zh;q=0.9,zh-TW;q=0.8,en;q=0.7'
        },
        'body': JSON.stringify({
            'version': '1.0',
            'fp': $.fp,
            'appId': $.appId,
            'timestamp': Date.now(),
            'platform': 'web',
            'expandParams': ''
        })
    };

    return new Promise(async resolve => {
        $.post(requestConfig, (error, response, body) => {
            try {
                // 解析响应数据
                const {
                    ret, msg, data: {
                        result
                    } = {}
                } = JSON.parse(body);

                // 保存token和生成密钥的函数
                $.token = result.tk;
                $.genKey = new Function('return ' + result.algo)();
            } catch (parseError) {
                $.logErr(parseError, response);
            } finally {
                resolve();
            }
        });
    });
}
/**
 * 生成随机ID的专业版函数
 * @param {Object} options - 配置选项
 * @param {number} options.size - 生成ID的长度，默认10
 * @param {string} options.dictType - 字典类型：'number'(数字)、'alphabet'(字母)、'max'(最大字符集)
 * @param {string} options.customDict - 自定义字符字典
 * @returns {string} 生成的随机ID
 */
function getRandomIDPro() {
    var characterSet, options,
        // 解析参数：获取size参数，默认为10
        idLength = void 0 === (tempSize = (options = 0 < arguments.length && void 0 !== arguments[0] ? arguments[0] : {})['size']) ? 10 : tempSize,
        // 解析参数：获取dictType参数，默认为'number'
        tempSize = void 0 === (tempSize = options['dictType']) ? 'number' : tempSize,
        // 结果字符串
        resultId = '';

    // 如果提供了自定义字典且为字符串类型，直接使用
    if ((options = options['customDict']) && 'string' == typeof options) {
        characterSet = options;
    } else {
        // 根据字典类型选择字符集
        switch (tempSize) {
            case 'alphabet':
                // 纯字母字符集（大小写）
                characterSet = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
                break;
            case 'max':
                // 最大字符集（数字+字母+特殊字符）
                characterSet = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ_-';
                break;
            case 'number':
            default:
                // 纯数字字符集（默认）
                characterSet = '0123456789';
        }
    }

    // 循环生成指定长度的随机字符串
    for (; idLength--;) {
        resultId += characterSet[Math.random() * characterSet.length | 0];
    }

    return resultId;
}
/**
 * 时间格式化函数
 * @param {string} formatPattern - 格式化模式，如 'yyyy-MM-dd HH:mm:ss'
 * @param {Date|string|number} dateInput - 要格式化的日期，可以是Date对象、时间戳或日期字符串
 * @returns {string} 格式化后的时间字符串
 */
function format(formatPattern, dateInput) {
    // 如果没有提供格式模式，默认使用 'yyyy-MM-dd'
    if (!formatPattern) formatPattern = 'yyyy-MM-dd';

    var timestamp;
    if (!dateInput) {
        // 如果没有提供日期输入，使用当前时间
        timestamp = Date.now();
    } else {
        // 将输入转换为Date对象
        timestamp = new Date(dateInput);
    }

    // 创建Date对象和相关变量
    var dateObj = new Date(timestamp),
        resultFormat = formatPattern,
        // 定义各种时间单位的替换映射
        timeUnits = {
            'M+': dateObj.getMonth() + 1,        // 月份 (1-12)
            'd+': dateObj.getDate(),             // 日期 (1-31)
            'D+': dateObj.getDate(),             // 日期 (1-31) - 别名
            'h+': dateObj.getHours(),            // 小时 (0-23)
            'H+': dateObj.getHours(),            // 小时 (0-23) - 别名
            'm+': dateObj.getMinutes(),          // 分钟 (0-59)
            's+': dateObj.getSeconds(),          // 秒数 (0-59)
            'w+': dateObj.getDay(),              // 星期 (0-6, 0=周日)
            'q+': Math.floor((dateObj.getMonth() + 3) / 3), // 季度 (1-4)
            'S+': dateObj.getMilliseconds()      // 毫秒 (0-999)
        };

    // 处理年份格式化 (yyyy, yy等)
    /(y+)/i.test(resultFormat) && (resultFormat = resultFormat.replace(RegExp.$1,
        ('').concat(dateObj.getFullYear()).substr(4 - RegExp.$1.length)));

    // 遍历所有时间单位进行替换
    Object.keys(timeUnits).forEach(unitPattern => {
        if (new RegExp('('.concat(unitPattern, ')')).test(resultFormat)) {
            // 根据单位类型确定填充字符：毫秒用'000'，其他用'00'
            var paddingChar = 'S+' === unitPattern ? '000' : '00';

            // 替换格式字符串中的占位符
            resultFormat = resultFormat.replace(RegExp.$1,
                1 == RegExp.$1.length ?
                    timeUnits[unitPattern] : // 单个字符不补零
                    ('').concat(paddingChar).concat(timeUnits[unitPattern])
                        .substr(('').concat(timeUnits[unitPattern]).length) // 多个字符需要补零
            );
        }
    });

    return resultFormat;
}
function Env(t, e) {
    "undefined" != typeof process && JSON.stringify(process.env).indexOf("GITHUB") > -1 && process.exit(0);
    class s {
        constructor(t) {
            this.env = t
        }
        send(t, e = "GET") {
            t = "string" == typeof t ? {
                url: t
            } : t;
            let s = this.get;
            return "POST" === e && (s = this.post), new Promise((e, i) => {
                s.call(this, t, (t, s, r) => {
                    t ? i(t) : e(s)
                })
            })
        }
        get(t) {
            return this.send.call(this.env, t)
        }
        post(t) {
            return this.send.call(this.env, t, "POST")
        }
    }
    return new class {
        constructor(t, e) {
            this.name = t, this.http = new s(this), this.data = null, this.dataFile = "box.dat", this.logs = [], this.isMute = !1, this.isNeedRewrite = !1, this.logSeparator = "\n", this.startTime = (new Date).getTime(), Object.assign(this, e), this.log("", `🔔${this.name}, 开始!`)
        }
        isNode() {
            return "undefined" != typeof module && !!module.exports
        }
        isQuanX() {
            return "undefined" != typeof $task
        }
        isSurge() {
            return "undefined" != typeof $httpClient && "undefined" == typeof $loon
        }
        isLoon() {
            return "undefined" != typeof $loon
        }
        toObj(t, e = null) {
            try {
                return JSON.parse(t)
            } catch {
                return e
            }
        }
        toStr(t, e = null) {
            try {
                return JSON.stringify(t)
            } catch {
                return e
            }
        }
        getjson(t, e) {
            let s = e;
            const i = this.getdata(t);
            if (i) try {
                s = JSON.parse(this.getdata(t))
            } catch {}
            return s
        }
        setjson(t, e) {
            try {
                return this.setdata(JSON.stringify(t), e)
            } catch {
                return !1
            }
        }
        getScript(t) {
            return new Promise(e => {
                this.get({
                    url: t
                }, (t, s, i) => e(i))
            })
        }
        runScript(t, e) {
            return new Promise(s => {
                let i = this.getdata("@chavy_boxjs_userCfgs.httpapi");
                i = i ? i.replace(/\n/g, "").trim() : i;
                let r = this.getdata("@chavy_boxjs_userCfgs.httpapi_timeout");
                r = r ? 1 * r : 20, r = e && e.timeout ? e.timeout : r;
                const [o, h] = i.split("@"), n = {
                    url: `http://${h}/v1/scripting/evaluate`,
                    body: {
                        script_text: t,
                        mock_type: "cron",
                        timeout: r
                    },
                    headers: {
                        "X-Key": o,
                        Accept: "*/*"
                    }
                };
                this.post(n, (t, e, i) => s(i))
            }).catch(t => this.logErr(t))
        }
        loaddata() {
            if (!this.isNode()) return {}; {
                this.fs = this.fs ? this.fs : require("fs"), this.path = this.path ? this.path : require("path");
                const t = this.path.resolve(this.dataFile),
                    e = this.path.resolve(process.cwd(), this.dataFile),
                    s = this.fs.existsSync(t),
                    i = !s && this.fs.existsSync(e);
                if (!s && !i) return {}; {
                    const i = s ? t : e;
                    try {
                        return JSON.parse(this.fs.readFileSync(i))
                    } catch (t) {
                        return {}
                    }
                }
            }
        }
        writedata() {
            if (this.isNode()) {
                this.fs = this.fs ? this.fs : require("fs"), this.path = this.path ? this.path : require("path");
                const t = this.path.resolve(this.dataFile),
                    e = this.path.resolve(process.cwd(), this.dataFile),
                    s = this.fs.existsSync(t),
                    i = !s && this.fs.existsSync(e),
                    r = JSON.stringify(this.data);
                s ? this.fs.writeFileSync(t, r) : i ? this.fs.writeFileSync(e, r) : this.fs.writeFileSync(t, r)
            }
        }
        lodash_get(t, e, s) {
            const i = e.replace(/\[(\d+)\]/g, ".$1").split(".");
            let r = t;
            for (const t of i)
                if (r = Object(r)[t], void 0 === r) return s;
            return r
        }
        lodash_set(t, e, s) {
            return Object(t) !== t ? t : (Array.isArray(e) || (e = e.toString().match(/[^.[\]]+/g) || []), e.slice(0, -1).reduce((t, s, i) => Object(t[s]) === t[s] ? t[s] : t[s] = Math.abs(e[i + 1]) >> 0 == +e[i + 1] ? [] : {}, t)[e[e.length - 1]] = s, t)
        }
        getdata(t) {
            let e = this.getval(t);
            if (/^@/.test(t)) {
                const [, s, i] = /^@(.*?)\.(.*?)$/.exec(t), r = s ? this.getval(s) : "";
                if (r) try {
                    const t = JSON.parse(r);
                    e = t ? this.lodash_get(t, i, "") : e
                } catch (t) {
                    e = ""
                }
            }
            return e
        }
        setdata(t, e) {
            let s = !1;
            if (/^@/.test(e)) {
                const [, i, r] = /^@(.*?)\.(.*?)$/.exec(e), o = this.getval(i), h = i ? "null" === o ? null : o || "{}" : "{}";
                try {
                    const e = JSON.parse(h);
                    this.lodash_set(e, r, t), s = this.setval(JSON.stringify(e), i)
                } catch (e) {
                    const o = {};
                    this.lodash_set(o, r, t), s = this.setval(JSON.stringify(o), i)
                }
            } else s = this.setval(t, e);
            return s
        }
        getval(t) {
            return this.isSurge() || this.isLoon() ? $persistentStore.read(t) : this.isQuanX() ? $prefs.valueForKey(t) : this.isNode() ? (this.data = this.loaddata(), this.data[t]) : this.data && this.data[t] || null
        }
        setval(t, e) {
            return this.isSurge() || this.isLoon() ? $persistentStore.write(t, e) : this.isQuanX() ? $prefs.setValueForKey(t, e) : this.isNode() ? (this.data = this.loaddata(), this.data[e] = t, this.writedata(), !0) : this.data && this.data[e] || null
        }
        initGotEnv(t) {
            this.got = this.got ? this.got : require("got"), this.cktough = this.cktough ? this.cktough : require("tough-cookie"), this.ckjar = this.ckjar ? this.ckjar : new this.cktough.CookieJar, t && (t.headers = t.headers ? t.headers : {}, void 0 === t.headers.Cookie && void 0 === t.cookieJar && (t.cookieJar = this.ckjar))
        }
        get(t, e = (() => {})) {
            t.headers && (delete t.headers["Content-Type"], delete t.headers["Content-Length"]), this.isSurge() || this.isLoon() ? (this.isSurge() && this.isNeedRewrite && (t.headers = t.headers || {}, Object.assign(t.headers, {
                "X-Surge-Skip-Scripting": !1
            })), $httpClient.get(t, (t, s, i) => {
                !t && s && (s.body = i, s.statusCode = s.status), e(t, s, i)
            })) : this.isQuanX() ? (this.isNeedRewrite && (t.opts = t.opts || {}, Object.assign(t.opts, {
                hints: !1
            })), $task.fetch(t).then(t => {
                const {
                    statusCode: s,
                    statusCode: i,
                    headers: r,
                    body: o
                } = t;
                e(null, {
                    status: s,
                    statusCode: i,
                    headers: r,
                    body: o
                }, o)
            }, t => e(t))) : this.isNode() && (this.initGotEnv(t), this.got(t).on("redirect", (t, e) => {
                try {
                    if (t.headers["set-cookie"]) {
                        const s = t.headers["set-cookie"].map(this.cktough.Cookie.parse).toString();
                        s && this.ckjar.setCookieSync(s, null), e.cookieJar = this.ckjar
                    }
                } catch (t) {
                    this.logErr(t)
                }
            }).then(t => {
                const {
                    statusCode: s,
                    statusCode: i,
                    headers: r,
                    body: o
                } = t;
                e(null, {
                    status: s,
                    statusCode: i,
                    headers: r,
                    body: o
                }, o)
            }, t => {
                const {
                    message: s,
                    response: i
                } = t;
                e(s, i, i && i.body)
            }))
        }
        post(t, e = (() => {})) {
            if (t.body && t.headers && !t.headers["Content-Type"] && (t.headers["Content-Type"] = "application/x-www-form-urlencoded"), t.headers && delete t.headers["Content-Length"], this.isSurge() || this.isLoon()) this.isSurge() && this.isNeedRewrite && (t.headers = t.headers || {}, Object.assign(t.headers, {
                "X-Surge-Skip-Scripting": !1
            })), $httpClient.post(t, (t, s, i) => {
                !t && s && (s.body = i, s.statusCode = s.status), e(t, s, i)
            });
            else if (this.isQuanX()) t.method = "POST", this.isNeedRewrite && (t.opts = t.opts || {}, Object.assign(t.opts, {
                hints: !1
            })), $task.fetch(t).then(t => {
                const {
                    statusCode: s,
                    statusCode: i,
                    headers: r,
                    body: o
                } = t;
                e(null, {
                    status: s,
                    statusCode: i,
                    headers: r,
                    body: o
                }, o)
            }, t => e(t));
            else if (this.isNode()) {
                this.initGotEnv(t);
                const {
                    url: s,
                    ...i
                } = t;
                this.got.post(s, i).then(t => {
                    const {
                        statusCode: s,
                        statusCode: i,
                        headers: r,
                        body: o
                    } = t;
                    e(null, {
                        status: s,
                        statusCode: i,
                        headers: r,
                        body: o
                    }, o)
                }, t => {
                    const {
                        message: s,
                        response: i
                    } = t;
                    e(s, i, i && i.body)
                })
            }
        }
        time(t, e = null) {
            const s = e ? new Date(e) : new Date;
            let i = {
                "M+": s.getMonth() + 1,
                "d+": s.getDate(),
                "H+": s.getHours(),
                "m+": s.getMinutes(),
                "s+": s.getSeconds(),
                "q+": Math.floor((s.getMonth() + 3) / 3),
                S: s.getMilliseconds()
            };
            /(y+)/.test(t) && (t = t.replace(RegExp.$1, (s.getFullYear() + "").substr(4 - RegExp.$1.length)));
            for (let e in i) new RegExp("(" + e + ")").test(t) && (t = t.replace(RegExp.$1, 1 == RegExp.$1.length ? i[e] : ("00" + i[e]).substr(("" + i[e]).length)));
            return t
        }
        msg(e = t, s = "", i = "", r) {
            const o = t => {
                if (!t) return t;
                if ("string" == typeof t) return this.isLoon() ? t : this.isQuanX() ? {
                    "open-url": t
                } : this.isSurge() ? {
                    url: t
                } : void 0;
                if ("object" == typeof t) {
                    if (this.isLoon()) {
                        let e = t.openUrl || t.url || t["open-url"],
                            s = t.mediaUrl || t["media-url"];
                        return {
                            openUrl: e,
                            mediaUrl: s
                        }
                    }
                    if (this.isQuanX()) {
                        let e = t["open-url"] || t.url || t.openUrl,
                            s = t["media-url"] || t.mediaUrl;
                        return {
                            "open-url": e,
                            "media-url": s
                        }
                    }
                    if (this.isSurge()) {
                        let e = t.url || t.openUrl || t["open-url"];
                        return {
                            url: e
                        }
                    }
                }
            };
            if (this.isMute || (this.isSurge() || this.isLoon() ? $notification.post(e, s, i, o(r)) : this.isQuanX() && $notify(e, s, i, o(r))), !this.isMuteLog) {
                let t = ["", "==============📣系统通知📣=============="];
                t.push(e), s && t.push(s), i && t.push(i), console.log(t.join("\n")), this.logs = this.logs.concat(t)
            }
        }
        log(...t) {
            t.length > 0 && (this.logs = [...this.logs, ...t]), console.log(t.join(this.logSeparator))
        }
        logErr(t, e) {
            const s = !this.isSurge() && !this.isQuanX() && !this.isLoon();
            s ? this.log("", `❗️${this.name}, 错误!`, t.stack) : this.log("", `❗️${this.name}, 错误!`, t)
        }
        wait(t) {
            return new Promise(e => setTimeout(e, t))
        }
        done(t = {}) {
            const e = (new Date).getTime(),
                s = (e - this.startTime) / 1e3;
            this.log("", `🔔${this.name}, 结束! 🕛 ${s} 秒`), this.log(), (this.isSurge() || this.isQuanX() || this.isLoon()) && $done(t)
        }
    }(t, e)
}