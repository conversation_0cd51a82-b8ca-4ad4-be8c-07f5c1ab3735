/**
 * 简单测试文件
 * 验证H5st和Sign算法的基本功能
 */

const { generateH5st, generateSign, generateUUID } = require('./index');

async function testH5st() {
  console.log('测试H5st算法...');
  
  const params = {
    version: '5.0.6',
    appId: '27004',
    body: {
      functionId: 'test',
      appid: 'test_app',
      body: JSON.stringify({ test: 'data' })
    }
  };

  try {
    const result = await generateH5st(params);
    console.log('✅ H5st生成成功');
    console.log('H5st长度:', result.h5st.h5st.length);
    console.log('包含必要字段:', result.h5st.h5st.includes(';'));
    return true;
  } catch (error) {
    console.log('❌ H5st生成失败:', error.message);
    return false;
  }
}

function testSign() {
  console.log('\n测试Sign算法...');
  
  const params = {
    functionId: 'test',
    body: JSON.stringify({ test: 'data' })
  };

  try {
    const result = generateSign(params);
    console.log('✅ Sign生成成功');
    console.log('Sign长度:', result.body.sign.length);
    console.log('包含必要字段:', result.body.uuid && result.body.st && result.body.sv);
    return true;
  } catch (error) {
    console.log('❌ Sign生成失败:', error.message);
    return false;
  }
}

function testUUID() {
  console.log('\n测试UUID生成...');
  
  try {
    const uuid1 = generateUUID();
    const uuid2 = generateUUID(32);
    const uuid3 = generateUUID(8, '0123456789');
    
    console.log('✅ UUID生成成功');
    console.log('16位UUID:', uuid1, '长度:', uuid1.length);
    console.log('32位UUID:', uuid2, '长度:', uuid2.length);
    console.log('8位数字UUID:', uuid3, '长度:', uuid3.length);
    
    return uuid1 !== uuid2; // 确保生成的UUID不同
  } catch (error) {
    console.log('❌ UUID生成失败:', error.message);
    return false;
  }
}

async function runTests() {
  console.log('=== 京东算法测试 ===\n');
  
  const results = [];
  
  results.push(await testH5st());
  results.push(testSign());
  results.push(testUUID());
  
  const passed = results.filter(r => r).length;
  const total = results.length;
  
  console.log(`\n=== 测试结果 ===`);
  console.log(`通过: ${passed}/${total}`);
  
  if (passed === total) {
    console.log('🎉 所有测试通过！算法工作正常。');
  } else {
    console.log('⚠️  部分测试失败，请检查代码。');
  }
}

// 运行测试
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { runTests };
