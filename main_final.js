// 京东优惠券自动领取脚本 - 已解码清理
_0xod2 = 'jsjiami.com.v6';
const $ = new Env('领取优惠券');
const nowVersion = "20250526";
console.log("当前版本" + nowVersion);
const cron = require('node-cron');
const fs = require('fs');
const path = require('path');
const util = require('util');
const {JSDOM} = require('jsdom');
const dom = new JSDOM('<!DOCTYPE html><html><body></body></html>', {
    'url': 'https://www.jd.com'
});
const window = dom.window;
const document = window.document;
global.window = window;
global.document = document;
global.Element = window.Element;
const configPath = path.join(__dirname, 'config.json');
// const smashUtils = require('./jdJm.js')['smashUtils'];
const { SmashUtils } = require('./SmashUtils.js');
let smashUtils = null;
async function initSmashUtils() {
    if (!smashUtils && cookiesArr && cookiesArr[0]) {
        const url = 'https://h5static.m.jd.com/mall/active/3aEzDU3fpqYYtnNTFPAkyY3tRY8Y/index.html';
        const cookie = cookiesArr[0];
        // const cookie = 'pt_key=AAJoLw9lADBxs4e1jDG4PoJ83ChDoVQCn7DgX-Q0uEnN4gMgcKBZS2L8kdBbcsrGqeuq0KH0xrM; pt_pin=jd_dlsyruocXPmz;';
        const ua = user_agent || "Mozilla/5.0 (iPhone; CPU iPhone OS 14_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.49(1800312d) NetType/4G Language/zh_CN";
        smashUtils = new SmashUtils(url, cookie, ua);
        await smashUtils.init({
            appid: "babel_zeEzM8yPWTLWju6YSj1aoNSvPfQ",
            sceneid: "babel_zeEzM8yPWTLWju6YSj1aoNSvPfQ",
            uid: "4f3a81b78bd21600d3a287edb48d425d"
        });
    }
}
const ParamsSignLite = require('./jdJm2.js');
$.CryptoJS = require('crypto-js');
let apiList = require('./jdYhqApiList.js').apiList;
const USER_AGENTS = ['jdapp;android;10.1.0;10;network/wifi;Mozilla/5.0 (Linux; Android 10; ONEPLUS A5010 Build/QKQ1.191014.012; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/77.0.3865.120 MQQBrowser/6.2 TBS/045230 Mobile Safari/537.36', 'jdapp;iPhone;10.1.0;14.3;network/4g;Mozilla/5.0 (iPhone; CPU iPhone OS 14_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1', 'jdapp;android;10.1.0;9;network/4g;Mozilla/5.0 (Linux; Android 9; Mi Note 3 Build/PKQ1.181007.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/66.0.3359.126 MQQBrowser/6.2 TBS/045131 Mobile Safari/537.36', 'jdapp;android;10.1.0;10;network/wifi;Mozilla/5.0 (Linux; Android 10; GM1910 Build/QKQ1.190716.003; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/77.0.3865.120 MQQBrowser/6.2 TBS/045230 Mobile Safari/537.36', 'jdapp;android;10.1.0;9;network/wifi;Mozilla/5.0 (Linux; Android 9; 16T Build/PKQ1.190616.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/66.0.3359.126 MQQBrowser/6.2 TBS/044942 Mobile Safari/537.36', 'jdapp;iPhone;10.1.0;13.6;network/wifi;Mozilla/5.0 (iPhone; CPU iPhone OS 13_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1', 'jdapp;iPhone;10.1.0;13.6;network/wifi;Mozilla/5.0 (iPhone; CPU iPhone OS 13_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1', 'jdapp;iPhone;10.1.0;13.5;network/wifi;Mozilla/5.0 (iPhone; CPU iPhone OS 13_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1', 'jdapp;iPhone;10.1.0;14.1;network/wifi;Mozilla/5.0 (iPhone; CPU iPhone OS 14_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1', 'jdapp;iPhone;10.1.0;13.3;network/wifi;Mozilla/5.0 (iPhone; CPU iPhone OS 13_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1', 'jdapp;iPhone;10.1.0;13.7;network/wifi;Mozilla/5.0 (iPhone; CPU iPhone OS 13_7 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1', 'jdapp;iPhone;10.1.0;14.1;network/wifi;Mozilla/5.0 (iPhone; CPU iPhone OS 14_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1', 'jdapp;iPhone;10.1.0;13.3;network/wifi;Mozilla/5.0 (iPhone; CPU iPhone OS 13_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1', 'jdapp;iPhone;10.1.0;13.4;network/wifi;Mozilla/5.0 (iPhone; CPU iPhone OS 13_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1', 'jdapp;iPhone;10.1.0;14.3;network/wifi;Mozilla/5.0 (iPhone; CPU iPhone OS 14_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1', 'jdapp;android;10.1.0;9;network/wifi;Mozilla/5.0 (Linux; Android 9; MI 6 Build/PKQ1.190118.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/66.0.3359.126 MQQBrowser/6.2 TBS/044942 Mobile Safari/537.36', 'jdapp;android;10.1.0;11;network/wifi;Mozilla/5.0 (Linux; Android 11; Redmi K30 5G Build/RKQ1.200826.002; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/77.0.3865.120 MQQBrowser/6.2 TBS/045511 Mobile Safari/537.36', 'jdapp;iPhone;10.1.0;11.4;network/wifi;Mozilla/5.0 (iPhone; CPU iPhone OS 11_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15F79', 'jdapp;android;10.1.0;10;;network/wifi;Mozilla/5.0 (Linux; Android 10; M2006J10C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/77.0.3865.120 MQQBrowser/6.2 TBS/045230 Mobile Safari/537.36', 'jdapp;android;10.1.0;10;network/wifi;Mozilla/5.0 (Linux; Android 10; M2006J10C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/77.0.3865.120 MQQBrowser/6.2 TBS/045230 Mobile Safari/537.36', 'jdapp;android;10.1.0;10;network/wifi;Mozilla/5.0 (Linux; Android 10; ONEPLUS A6000 Build/QKQ1.190716.003; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/77.0.3865.120 MQQBrowser/6.2 TBS/045224 Mobile Safari/537.36', 'jdapp;android;10.1.0;9;network/wifi;Mozilla/5.0 (Linux; Android 9; MHA-AL00 Build/HUAWEIMHA-AL00; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/66.0.3359.126 MQQBrowser/6.2 TBS/044942 Mobile Safari/537.36', 'jdapp;android;10.1.0;8.1.0;network/wifi;Mozilla/5.0 (Linux; Android 8.1.0; 16 X Build/OPM1.171019.026; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/66.0.3359.126 MQQBrowser/6.2 TBS/044942 Mobile Safari/537.36', 'jdapp;android;10.1.0;8.0.0;network/wifi;Mozilla/5.0 (Linux; Android 8.0.0; HTC U-3w Build/OPR6.170623.013; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/66.0.3359.126 MQQBrowser/6.2 TBS/044942 Mobile Safari/537.36', 'jdapp;iPhone;10.1.0;14.0.1;network/wifi;Mozilla/5.0 (iPhone; CPU iPhone OS 14_0_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1', 'jdapp;android;10.1.0;10;network/wifi;Mozilla/5.0 (Linux; Android 10; LYA-AL00 Build/HUAWEILYA-AL00L; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/77.0.3865.120 MQQBrowser/6.2 TBS/045230 Mobile Safari/537.36', 'jdapp;iPhone;10.1.0;14.2;network/wifi;Mozilla/5.0 (iPhone; CPU iPhone OS 14_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1', 'jdapp;iPhone;10.1.0;14.3;network/wifi;Mozilla/5.0 (iPhone; CPU iPhone OS 14_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1', 'jdapp;iPhone;10.1.0;14.2;network/wifi;Mozilla/5.0 (iPhone; CPU iPhone OS 14_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1', 'jdapp;android;10.1.0;8.1.0;network/wifi;Mozilla/5.0 (Linux; Android 8.1.0; MI 8 Build/OPM1.171019.026; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/66.0.3359.126 MQQBrowser/6.2 TBS/045131 Mobile Safari/537.36', 'jdapp;android;10.1.0;10;network/wifi;Mozilla/5.0 (Linux; Android 10; Redmi K20 Pro Premium Edition Build/QKQ1.190825.002; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/77.0.3865.120 MQQBrowser/6.2 TBS/045227 Mobile Safari/537.36', 'jdapp;iPhone;10.1.0;14.3;network/wifi;Mozilla/5.0 (iPhone; CPU iPhone OS 14_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1', 'jdapp;iPhone;10.1.0;14.3;network/4g;Mozilla/5.0 (iPhone; CPU iPhone OS 14_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1', 'jdapp;android;10.1.0;11;network/wifi;Mozilla/5.0 (Linux; Android 11; Redmi K20 Pro Premium Edition Build/RKQ1.200826.002; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/77.0.3865.120 MQQBrowser/6.2 TBS/045513 Mobile Safari/537.36', 'jdapp;android;10.1.0;10;network/wifi;Mozilla/5.0 (Linux; Android 10; MI 8 Build/QKQ1.190828.002; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/77.0.3865.120 MQQBrowser/6.2 TBS/045227 Mobile Safari/537.36', 'jdapp;iPhone;10.1.0;14.1;network/wifi;Mozilla/5.0 (iPhone; CPU iPhone OS 14_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1'];
function randomNumber(min = 0, max = 64) {
    return Math.min(Math.floor(min + Math.random() * (max - min)), max);
}
let config = {};
try {
    config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
} catch (error) {
    console.error('config.json文件读取失败，请检查文件格式否正确！');
    return;
}
const logDir = path.join(__dirname, 'log');
if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir);
}
const logFileName = 'app_' + timeFormat(new Date(), 'date') + '.log';
const logFilePath = path.join(logDir, logFileName);
const originalConsoleLog = console.log;
console.log = function(...args) {
    const formattedMessage = util.format(...args) + '';
    fs.appendFileSync(logFilePath, formattedMessage);
    originalConsoleLog.apply(console, args);
};
const originalConsoleError = console.error;
console.error = function(...args) {
    const formattedMessage = util.format(...args) + '';
    fs.appendFileSync(logFilePath, formattedMessage);
    originalConsoleError.apply(console, args);
};
// 配置参数
let tryNum = 4; // 重试次数
let maxQq = 14; // 最大优惠券数量
let maxXc = 3; // 最大并发数
let qqjgTime = 250; // 请求间隔时间(ms)
let maxAccount = 8; // 最大账号数
let ycTime = 64; // 延迟时间
// 全局变量
let cookiesArr = [], // Cookie数组
    cookie = '';
let canTaskFlag = []; // 任务标志
let TgCkArray = []; // 跳过的Cookie索引
let lqSucArray = []; // 领取成功的用户
let AllEendCode = '|A9|A6|A14|D2|'; // 全部结束代码
let PEendCode = '|A1|A12|A13|A19|A26|'; // 部分结束代码
let JDTimes = new Date().getTime();
let apiArray = [];
let nowIndex = 0;
let JDTimeJg = 0;
let yhqAPiHasSuccess = {};
let nextHour = 0;
let ckerror = [];
let removeYhq = [];
let nowRunYhq = '';
let user_agent = '';
let paramsSignLiteMy = '';
if (config.JD_COOKIE) {
    cookiesArr = config.JD_COOKIE;
} else {
    console.log('【提示】请先添加JD_COOKIE');
    return false;
} if (config.YHQ_REMOVE && config.YHQ_REMOVE.split(',').length >= 1) {
    if (config.YHQ_REMOVE.toLowerCase() == 'all') {
        console.log('读取环境变量排除的优惠券为：不抢作者所有的券！');
        apiList = [];
    } else {
        console.log('读取环境变量排除的优惠券为：' + config.YHQ_REMOVE);
        removeYhq = config.YHQ_REMOVE.split(',');
    }
}
if (config.YHQ_NOWRUN) {
    console.log('读取环境变量立即执行优惠券为：' + config.YHQ_NOWRUN);
    nowRunYhq = config.YHQ_NOWRUN;
}
try {
    const apiListMy = require('./jdYhqApiListMy.js').apiList;
    if (apiListMy.length > 0) {
        for (var alm in apiListMy) {
            if (apiListMy[alm].qName && apiListMy[alm].qApi && apiListMy[alm].qTime) {
                apiList.push(apiListMy[alm]);
                console.log('加载自定义API:' + apiListMy[alm].qName);
            }
        }
    }
} catch (_6007a2) {
    console.log('未配置自定义API！');
}
try {
    paramsSignLiteMy = new window[('ParamsSignLite')]({
        'appId': '35fa0',
        'preRequest': !1
    });
} catch (error) {}
if (config.YHQ_API && config.YHQ_API.indexOf(',') > -1 && config.YHQ_API.split(',').length >= 5) {
    console.log('读取环境变量成功：' + config.YHQ_API);
    let YHQ_API_ARR = config.YHQ_API.split(',');
    tryNum = parseInt(YHQ_API_ARR[0]);
    if (parseInt(YHQ_API_ARR[1]) > maxQq) {
        maxQq = parseInt(YHQ_API_ARR[1]);
    }
    maxXc = parseInt(YHQ_API_ARR[2]);
    qqjgTime = parseInt(YHQ_API_ARR[3]);
    maxAccount = parseInt(YHQ_API_ARR[4]);
    if (YHQ_API_ARR.length >= 6) {
        ycTime = parseInt(YHQ_API_ARR[5]);
    }
}
console.log('' + timeFormat() + ':' + '----脚本运行成功，请不要关闭窗口----');
let isMainRunning = false;
async function executeMain() {
    if (isMainRunning) {
        console.log('任务执行中...');
        return;
    }
    isMainRunning = true;
    try {
        resertCs();
        await main();
    } catch (error) {
        console.error('main 函数执行失败:', error);
    } finally {
        isMainRunning = false;
    }
}
executeMain();
cron.schedule('* * * * *', () => {
    try {
        const currentMinute = new Date().getMinutes();
        if (currentMinute === 59) {
            executeMain();
        } else {
            if (!isMainRunning && currentMinute % 5 === 0) {
                console.log('' + timeFormat() + ':' + '未到任务执行时间，跳过执行......');
            }
        }
    } catch (error) {}
});
async function main() {
    try {
        if (!cookiesArr[0]) {
            console.log('【提示】请先增加JD账号一cookie');
            return;
        } else {
            console.log('共检测到' + cookiesArr.length + '个cookie');
        }
        // 初始化 SmashUtils
        await initSmashUtils();
        if (new Date().getDate() == 1 && new Date().getHours() == 0) {
            $.setjson({}, 'yhqAPiHasSuccess');
            console.log('清空缓存！');
        }
        nextHour = nextHourF();
        console.log('下次抢券时间：' + nextHour + ':00:00');
        user_agent = USER_AGENTS[randomNumber(0, USER_AGENTS.length)];
        for (var apiIndex in apiList) {
            if (nowRunYhq && nowRunYhq.length > 0 && nowRunYhq == apiList[apiIndex].qName) {
                console.log('立即抢券（跑完记得删除或禁用该环境变量）：' + apiList[apiIndex].qName);
                apiArray.push(apiList[apiIndex]);
                doAPIList(apiArray.length - 1);
                continue;
            }
            if (checkYhq(apiList[apiIndex], nextHour) && !isRemoveYhqF(apiList[apiIndex]) && apiArray.length < maxQq) {
                apiArray.push(apiList[apiIndex]);
                console.log('名称：' + apiList[apiIndex].qName);
            }
        }
        if (apiArray.length <= 0) {
            console.log('当前时间段没有优惠券需要领取！');
            return;
        }
        if ($.getdata('JDTimeJg') && $.getdata('JDTimeJg') != 0) {
            JDTimeJg = $.getdata('JDTimeJg');
        }
        if ($.getjson('yhqAPiHasSuccess')) {
            yhqAPiHasSuccess = $.getjson('yhqAPiHasSuccess');
        }
        let waitTime = jgNextHourF() + JDTimeJg - ycTime;
        if (waitTime > 2 * 60 * 1000) {
            console.log(parseInt(waitTime / 60 / 1000) + '分后才开始！');
            return;
        }
        if (waitTime > 0) {
            console.log(parseInt(waitTime / 60 / 1000) + '分后开始任务，请不要结束任务！');
            await $.wait(waitTime);
        }
        for (let apiIndex in apiArray) {
            if (!yhqAPiHasSuccess[apiArray[apiIndex].qName]) {
                yhqAPiHasSuccess[apiArray[apiIndex].qName] = {};
            }
            doAPIList(apiIndex);
        }
        await $.wait(3 * 1000);
        for (let apiIndex in apiArray) {
            let successMessage = '';
            if (lqSucArray[apiIndex].length > 0) {
                if (apiArray[apiIndex].qName) {
                    successMessage += '券【' + apiArray[apiIndex].qName + '】';
                }
                successMessage += '成功领取的用户有：';
                for (var successIndex in lqSucArray[apiIndex]) {
                    cookie = cookiesArr[lqSucArray[apiIndex][successIndex]];
                    let username = decodeURIComponent(cookie.match(/pt_pin=([^; ]+)(?=;?)/) && cookie.match(/pt_pin=([^; ]+)(?=;?)/)[1]);
                    successMessage += '' + (lqSucArray[apiIndex][successIndex] + 1) + '、' + username;
                }
                console.log('************************');
                console.log(successMessage);
            }
            if (successMessage) {
                myNotice(successMessage);
                console.log(successMessage);
                successMessage = '';
            }
        }
        $.setjson(yhqAPiHasSuccess, 'yhqAPiHasSuccess');
    } catch (error) {
        console.error('main function error:', error);
    }
}
function resertCs() {
    canTaskFlag = [];
    TgCkArray = [];
    lqSucArray = [];
    apiArray = [];
    nowIndex = 0;
    yhqAPiHasSuccess = {};
}
async function doAPIList(apiIndex) {
    canTaskFlag[apiIndex] = true;
    TgCkArray[apiIndex] = [];
    lqSucArray[apiIndex] = [];
    for (let tryCount = 1; tryCount <= tryNum; tryCount++) {
        if (canTaskFlag[apiIndex] && TgCkArray[apiIndex].length < cookiesArr.length && TgCkArray[apiIndex].length < maxAccount) {
            console.log('***开始领券【' + apiArray[apiIndex].qName + '】第' + tryCount + '次请求***');
            for (let cookieIndex = 0; cookieIndex < cookiesArr.length && cookieIndex < maxAccount; cookieIndex++) {
                let targetCookieIndex = apiArray[apiIndex].ckIndex ? apiArray[apiIndex].ckIndex : 0;
                if (targetCookieIndex > 0) {
                    if (cookieIndex + 1 < targetCookieIndex) {
                        continue;
                    } else if (cookieIndex + 1 > targetCookieIndex) {
                        break;
                    } else {
                        console.log('开始执行账号' + targetCookieIndex + '专属ck:');
                    }
                }
                if (canTaskFlag[apiIndex]) {
                    if (cookiesArr[cookieIndex]) {
                        let _5159fb = decodeURIComponent(cookiesArr[cookieIndex].match(/pt_pin=([^; ]+)(?=;?)/) && cookiesArr[cookieIndex].match(/pt_pin=([^; ]+)(?=;?)/)[1]);
                        if (TgCkArray[apiIndex].includes(cookieIndex)) {
                            console.log('跳过账号' + (cookieIndex + 1) + ':' + _5159fb + '！');
                            continue;
                        }
                        try {
                            if (yhqAPiHasSuccess[apiArray[apiIndex].qName][_5159fb] && nextHour != 0) {
                                let _4591ec = getNowDate();
                                if (DateDiff(_4591ec, yhqAPiHasSuccess[apiArray[apiIndex].qName][_5159fb]) < apiArray[apiIndex].lqSpace) {
                                    console.log('其他时间领取成功跳过账号' + (cookieIndex + 1) + ':' + _5159fb + '！');
                                    TgCkArray[apiIndex].push(cookieIndex);
                                    continue;
                                }
                            }
                        } catch (error) {}
                        nowIndex++;
                        if (nowIndex >= maxXc) {
                            if (nowIndex % maxXc == 0) {
                                await $.wait(qqjgTime - 14);
                            } else {
                                await $.wait(10);
                            }
                        }
                        doApiTask(apiIndex, cookieIndex);
                    }
                } else {
                    console.log('该券已无或者无账号需要请求！');
                    break;
                }
            }
        } else {
            break;
        }
    }
}
async function doApiTask(apiIndex, cookieIndex) {
    console.log('' + nowIndex + '、' + timeFormat() + (':开始领取' + apiArray[apiIndex].qName + '_账号' + (cookieIndex + 1)));
    return new Promise(async resolve => {
        if (canTaskFlag[apiIndex]) {
            if (apiArray[apiIndex].qName.indexOf('G') > -1 || apiArray[apiIndex].qApi.indexOf('https://s.m.jd.com') > -1 || apiArray[apiIndex].qApi.indexOf('h5_awake_wxapp') > -1) {
                const getApiConfig = await getApiUrlGet(apiIndex, cookieIndex);
                $.get(getApiConfig, (getError, getResponse, getBody) => {
                    try {
                        if (getError) {
                            console.log('API请求失败，请检查网络重试');
                        } else {
                            cookie = cookiesArr[cookieIndex];
                            let _598112 = decodeURIComponent(cookie.match(/pt_pin=([^; ]+)(?=;?)/) && cookie.match(/pt_pin=([^; ]+)(?=;?)/)[1]);
                            console.log('*' + apiArray[apiIndex].qName + '_【账号' + (cookieIndex + 1) + '】' + _598112 + '*');
                            console.log(timeFormat() + ':' + getBody);
                            if (getBody.indexOf('成功') > -1) {
                                lqSucArray[apiIndex].push(cookieIndex);
                                yhqAPiHasSuccess[apiArray[apiIndex].qName][_598112] = getNowDate();
                            } else if (getBody.indexOf('再来') > -1 || getBody.indexOf('抢光') > -1) {
                                canTaskFlag[apiIndex] = false;
                            }
                        }
                    } catch (error) {
                        TgCkArray[apiIndex].push(cookieIndex);
                        $.logErr(error, getResponse);
                    } finally {
                        resolve(getBody);
                    }
                });
            } else {
                const postApiConfig = await getApiUrl(apiIndex, cookieIndex);
                $.post(postApiConfig, (postError, postResponse, postBody) => {
                    try {
                        if (postError) {
                            console.log('' + JSON.stringify(postError));
                            console.log('API请求失败，请检查网络重试');
                        } else {
                            cookie = cookiesArr[cookieIndex];
                            let username = decodeURIComponent(cookie.match(/pt_pin=([^; ]+)(?=;?)/) && cookie.match(/pt_pin=([^; ]+)(?=;?)/)[1]);
                            console.log('*' + apiArray[apiIndex].qName + '_【账号' + (cookieIndex + 1) + '】' + username + '*');
                            postBody = JSON.parse(postBody);
                            let message = '';
                            let subCodeWithPipes = '';
                            try {
                                subCodeWithPipes = '|' + postBody.subCode + '|';
                                message = postBody.subCodeMsg || postBody.resultData.msg;
                            } catch (error) {}
                            if (postBody.subCode && (postBody.subCode == 'A1' || postBody.subCode == '0') || message && message.indexOf('成功') > -1) {
                                lqSucArray[apiIndex].push(cookieIndex);
                                yhqAPiHasSuccess[apiArray[apiIndex].qName][username] = getNowDate();
                            }
                            if (AllEendCode.indexOf(subCodeWithPipes) > -1) {
                                if (postBody.subCode == 'D2' && message.substr(message.indexOf('请') + 1, 2) == nextHour) {
                                    console.log(timeFormat() + ':时间未到继续：' + message);
                                } else if (nextHour == 0) {
                                    console.log(timeFormat() + ':继续：' + message);
                                } else {
                                    canTaskFlag[apiIndex] = false;
                                    console.log(timeFormat() + ':' + message);
                                }
                            } else if (PEendCode.indexOf(subCodeWithPipes) > -1) {
                                TgCkArray[apiIndex].push(cookieIndex);
                                console.log(timeFormat() + ':' + message + ',subCode2_' + subCodeWithPipes);
                            } else if (postBody.code && postBody.code == '3') {
                                TgCkArray[apiIndex].push(cookieIndex);
                                console.log(timeFormat() + ':ck过期！');
                                if (!checkHasCz(ckerror, cookieIndex)) {
                                    ckerror.push(cookieIndex);
                                    myNotice('【账号' + (cookieIndex + 1) + '】' + username + '——ck过期!');
                                    console.error('【账号' + (cookieIndex + 1) + '】' + username + '——ck过期!');
                                }
                            } else {
                                console.log(timeFormat() + ':' + JSON.stringify(postBody));
                            }
                        }
                    } catch (error) {
                        TgCkArray[apiIndex].push(cookieIndex);
                        $.logErr(error, postResponse);
                    } finally {
                        resolve(postBody);
                    }
                });
            }
        } else {
            console.log('该券已无或已结束！');
        }
    });
}
function getJDTime() {
    return new Promise(resolve => {
        $.post({
            'url': 'https://api.m.jd.com/client.action?functionId=queryMaterialProducts&client=wh5'
        }, async(error, response, body) => {
            try {
                if (error) {
                    console.log('获取JD时间失败');
                } else {
                    body = JSON.parse(body);
                    if (body.code && body.code == '0') {
                        JDTimes = parseInt(body.currentTime2);
                        if (JDTimeJg == 0 || JDTimeJg != 0 && new Date().getTime() - JDTimes < JDTimeJg) {
                            JDTimeJg = new Date().getTime() - JDTimes;
                        }
                    } else {
                        console.log('获取JD时间失败:' + JSON.stringify(body));
                    }
                }
            } catch (error) {
                $.logErr(error, response);
            } finally {
                resolve(body);
            }
        });
    });
}
function checkYhq(couponInfo, targetHour) {
    if (!couponInfo.endDate) {
        return true;
    }
    if (couponInfo.endDate && couponInfo.qTime && new Date(couponInfo.endDate + ' 23:59:59').getTime() > new Date().getTime()) {
        let timeArray = couponInfo.qTime.split(',');
        if (timeArray.length > 0 && timeArray.includes(targetHour + '')) {
            return true;
        }
    }
    return false;
}
function isRemoveYhqF(couponInfo) {
    let shouldRemove = false;
    if (removeYhq && removeYhq.length > 0) {
        for (var removeIndex in removeYhq) {
            if (couponInfo.qName == removeYhq[removeIndex]) {
                console.log('排除优惠券：' + couponInfo.qName);
                shouldRemove = true;
                break;
            }
        }
    }
    return shouldRemove;
}
async function getApiUrl(apiIndex, cookieIndex) {
    const apiLogResult = await getApiLog(apiArray[apiIndex].qApi);
    const decryptedUrl = await getDecryptUrlTy(apiLogResult);
    return {
        'url': decryptedUrl,
        'headers': {
            'user-agent': user_agent,
            'content-Type': 'application/x-www-form-urlencoded',
            'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
            'accept-encoding': 'gzip, deflate, br',
            'accept-language': 'zh-CN,zh;q=0.9',
            'cache-control': 'max-age=0',
            'cookie': cookiesArr[cookieIndex]
        }
    };
}
async function getApiUrlGet(apiIndex, cookieIndex) {
    if (apiArray[apiIndex].qApi.indexOf('https://s.m.jd.com') > -1 || apiArray[apiIndex].qApi.indexOf('h5_awake_wxapp') > -1) {
        const apiLogResult = await getApiLog(apiArray[apiIndex].qApi);
        const decryptedUrl = await getDecryptUrlTy(apiLogResult);
        return {
            'url': decryptedUrl,
            'headers': {
                'User-Agent': user_agent,
                'Cookie': cookiesArr[0],
                'Accept': '*/*',
                'Accept-Encoding': 'gzip, deflate, br',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Referer': 'https://coupon.m.jd.com/'
            }
        };
    } else if (apiArray[apiIndex].qApi.indexOf('appid=plus_business') > -1) {
        return {
            'url': apiArray[apiIndex].qApi,
            'headers': {
                'User-Agent': user_agent,
                'accept-encoding': 'gzip, deflate, br',
                'accept-language': 'zh-CN,zh;q=0.9',
                'Cookie': cookiesArr[cookieIndex],
                'origin': 'https://plus.m.jd.com'
            }
        };
    } else {
        return {
            'url': apiArray[apiIndex].qApi,
            'headers': {
                'User-Agent': user_agent,
                'accept-encoding': 'gzip, deflate, br',
                'accept-language': 'zh-CN,zh;q=0.9',
                'Cookie': cookiesArr[cookieIndex]
            }
        };
    }
}
function jgNextHourF() {
    let _3e1e1e = timeFormat().substr(0, 0xd) + ':00:00';
    let _326886 = Date['parse'](new Date(_3e1e1e)) + 60 * 60 * 1000;
    return _326886 - new Date().getTime();
}
function myNotice(_16de16) {}
function nextHourF() {
    let _3e1c44 = new Date();
    return _3e1c44.getHours() + 1 >= 18 ? 0 : _3e1c44.getHours() + 1;
}
function DateDiff(_58a163, _452cf8) {
    var _14660f, _3ffd45, _4e88e6, _530806;
    _14660f = _58a163.split('-');
    _3ffd45 = new Date(_14660f[1] + '-' + _14660f[2] + '-' + _14660f[0]);
    _14660f = _452cf8.split('-');
    _4e88e6 = new Date(_14660f[1] + '-' + _14660f[2] + '-' + _14660f[0]);
    _530806 = parseInt(Math.abs(_3ffd45 - _4e88e6) / 1000 / 60 / 60 / 18);
    return _530806;
}
function getNowDate() {
    let _11667f = new Date();
    return _11667f.getFullYear() + '-' + (_11667f.getMonth() + 1 >= 10 ? _11667f.getMonth() + 1 : '0' + (_11667f.getMonth() + 1)) + '-' + (_11667f.getDate() >= 10 ? _11667f.getDate() : '0' + _11667f.getDate());
}
function timeFormat(_476759, _18f799) {
    let _1b1c03;
    if (_476759) {
        _1b1c03 = new Date(_476759);
    } else {
        _1b1c03 = new Date();
    } if (_18f799 == 'date') {
        return _1b1c03.getFullYear() + '-' + (_1b1c03.getMonth() + 1 >= 10 ? _1b1c03.getMonth() + 1 : '0' + (_1b1c03.getMonth() + 1)) + '-' + (_1b1c03.getDate() >= 10 ? _1b1c03.getDate() : '0' + _1b1c03.getDate());
    }
    return _1b1c03.getFullYear() + '-' + (_1b1c03.getMonth() + 1 >= 10 ? _1b1c03.getMonth() + 1 : '0' + (_1b1c03.getMonth() + 1)) + '-' + (_1b1c03.getDate() >= 10 ? _1b1c03.getDate() : '0' + _1b1c03.getDate()) + ' ' + (_1b1c03.getHours() >= 10 ? _1b1c03.getHours() : '0' + _1b1c03.getHours()) + ':' + (_1b1c03.getMinutes() >= 10 ? _1b1c03.getMinutes() : '0' + _1b1c03.getMinutes()) + ':' + (_1b1c03.getSeconds() >= 10 ? _1b1c03.getSeconds() : '0' + _1b1c03.getSeconds()) + ':' + _1b1c03.getMilliseconds();
}
async function getApiLog(_3a204f) {
    let _1ebbfc = smashUtils['getRandom'](8);
    let smashResult = await smashUtils['get_risk_result']({
        'id': 'coupon',
        'data': {
            'random': _1ebbfc
        }
    }, "") || {};
    let _4ae01c = smashResult['log'];
    let _0xfb9364 = encodeURIComponent(',"log":"' + _4ae01c + '","random":"' + _1ebbfc + '"');
    if (_3a204f && _3a204f.indexOf('%7D') > -1) {
        _0xfb9364 = _3a204f.substring(0, _3a204f.indexOf('%7D')) + _0xfb9364 + _3a204f.substring(_3a204f.indexOf('%7D'), _3a204f.length);
    }
    return _0xfb9364;
}
function checkHasCz(_0xbf4a7f, _244387) {
    let _406a4f = false;
    if (_0xbf4a7f) {
        for (var _349d99 in _0xbf4a7f) {
            if (_0xbf4a7f[_349d99] == _244387) {
                _406a4f = true;
                break;
            }
        }
    }
    return _406a4f;
}
function getUrlQueryParams(_4c1021, _1ce2ac) {
    let _1da674 = new RegExp('(^|&)' + _1ce2ac + '=([^&]*)(&|$)', 'i');
    const split = 'split';
    const urlParts = _4c1021[split]('?');
    let _309e14 = urlParts[1].substr(0).match(_1da674);
    if (_309e14 != null) {
        return decodeURIComponent(_309e14[2]);
    };
    return '';
}
function sha256Hash(_31e864) {
    const _39d785 = new TextEncoder();
    const _2355d8 = _39d785['encode'](_31e864);
    const _4f239d = $.CryptoJS['SHA256']($.CryptoJS['enc']['Utf8']['parse'](_31e864));
    const _5bad46 = _4f239d.toString($.CryptoJS['enc']['Hex']);
    return _5bad46;
}
function getDecryptUrlTy(_1462ff) {
    return new Promise((_1c381e, _101473d) => {
        let _20fc97 = sha256Hash(getUrlQueryParams(_1462ff, 'body'));
        let _2e7773 = {
            'appid': 'babelh5',
            'body': _20fc97,
            'client': 'wh5',
            'clientVersion': '1.0.0',
            'functionId': 'newBabelAwardCollection'
        };
        paramsSignLiteMy['sign'](_2e7773).then(_0xe4545 => {
            _1c381e(_1462ff + '&h5st=' + _0xe4545['h5st']);
        }).catch(_52cccd => {
            console.error('签名失败:', _52cccd);
            _1c381e(_1462ff);
        });
    });
}
function getDecryptUrl(_1a1eaf) {
    _1a1eaf = _1a1eaf + '&t=' + Date['now']();
    stk = getUrlQueryParams(_1a1eaf, '_stk');
    if (stk) {
        const _660091 = format('yyyyMMddhhmmssSSS', Date['now']());
        const _5996a4 = $['genKey']($['token'], $['fp'].toString(), _660091.toString(), $['appId'].toString(), $.CryptoJS).toString($.CryptoJS['enc']['Hex']);
        let _2a60af = '';
        stk.split(',')['map']((_1b945f, _260740) => {
            _2a60af += _1b945f + ':' + getUrlQueryParams(_1a1eaf, _1b945f) + (_260740 === stk.split(',').length - 1 ? '' : '&');
        });
        const _35ce14 = $.CryptoJS['HmacSHA256'](_2a60af, _5996a4.toString()).toString($.CryptoJS['enc']['Hex']);
        return _1a1eaf + '&h5st=' + encodeURIComponent(['' .concat(_660091.toString()), '' .concat($['fp'].toString()), '' .concat($['appId'].toString()), '' .concat($['token']), '' .concat(_35ce14), '3.0;'.concat(_660091)].join(';')) + '&__t=' + Date['now']();
    }
}
async function requestAlgo() {
    $['appId'] = '8ba9b';
    $['fp'] = (getRandomIDPro({
        'size': 0xd
    }) + Date['now']()).slice(0, 10);
    const _0xf4cd6b = {
        'url': 'https://cactus.jd.com/request_algo?g_ty=ajax',
        'headers': {
            'Authority': 'cactus.jd.com',
            'Pragma': 'no-cache',
            'Cache-Control': 'no-cache',
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'Origin': 'https://st.jingxi.com',
            'Sec-Fetch-Site': 'cross-site',
            'User-Agent': $['user_agent'],
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'Referer': 'https://st.jingxi.com/',
            'Accept-Language': 'zh-CN,zh;q=0.9,zh-TW;q=0.8,en;q=0.7'
        },
        'body': JSON.stringify({
            'version': '1.0',
            'fp': $['fp'],
            'appId': $['appId'],
            'timestamp': Date['now'](),
            'platform': 'web',
            'expandParams': ''
        })
    };
    return new Promise(async _5058db => {
        $.post(_0xf4cd6b, (_0xe25c05, _6e76dd, _0xf224a0) => {
            try {
                const {
                    ret, msg, data: {
                        result
                    } = {}
                } = JSON.parse(_0xf224a0);
                $['token'] = result['tk'];
                $['genKey'] = new Function('return ' + result['algo'])();
            } catch (_38239b) {
                $.logErr(_38239b, _6e76dd);
            } finally {
                _5058db();
            }
        });
    });
}
function getRandomIDPro() {
    var _158423, _35153a, _281d64 = void 0 === (_55a423 = (_35153a = 0 < arguments.length && void 0 !== arguments[0] ? arguments[0] : {})['size']) ? 10 : _55a423,
        _55a423 = void 0 === (_55a423 = _35153a['dictType']) ? 'number' : _55a423,
        _2bdbe7 = '';
    if ((_35153a = _35153a['customDict']) && 'string' == typeof _35153a) _158423 = _35153a;
    else switch (_55a423) {
        case 'alphabet':
            _158423 = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
            break;
        case 'max':
            _158423 = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ_-';
            break;
        case 'number':
        default:
            _158423 = '0123456789';
    }
    for (; _281d64--;) _2bdbe7 += _158423[Math.random() * _158423.length | 0];
    return _2bdbe7;
}
function format(_4e2821, _1830a2) {
    if (!_4e2821) _4e2821 = 'yyyy-MM-dd';
    var _1660d1;
    if (!_1830a2) {
        _1660d1 = Date['now']();
    } else {
        _1660d1 = new Date(_1830a2);
    }
    var _5b159f, _2c72b9 = new Date(_1660d1),
        _557d25 = _4e2821,
        _22de01 = {
            'M+': _2c72b9.getMonth() + 1,
            'd+': _2c72b9.getDate(),
            'D+': _2c72b9.getDate(),
            'h+': _2c72b9.getHours(),
            'H+': _2c72b9.getHours(),
            'm+': _2c72b9.getMinutes(),
            's+': _2c72b9.getSeconds(),
            'w+': _2c72b9.getDay(),
            'q+': Math.floor((_2c72b9.getMonth() + 3) / 3),
            'S+': _2c72b9.getMilliseconds()
        };
    /(y+)/i ['test'](_557d25) && (_557d25 = _557d25.replace(RegExp['$1'], '' .concat(_2c72b9.getFullYear()).substr(4 - RegExp['$1'].length)));
    Object['keys'](_22de01)['forEach'](_3a57f9 => {
        if (new RegExp('(' .concat(_3a57f9, ')'))['test'](_557d25)) {
            var _4c2a9e, _0xc9b7ea = 'S+' === _3a57f9 ? '000' : '00';
            _557d25 = _557d25.replace(RegExp['$1'], 1 == RegExp['$1'].length ? _22de01[_3a57f9] : '' .concat(_0xc9b7ea).concat(_22de01[_3a57f9]).substr('' .concat(_22de01[_3a57f9]).length));
        }
    });
    return _557d25;
}
function Env(t, e) {
    "undefined" != typeof process && JSON.stringify(process.env).indexOf("GITHUB") > -1 && process.exit(0);
    class s {
        constructor(t) {
            this.env = t
        }
        send(t, e = "GET") {
            t = "string" == typeof t ? {
                url: t
            } : t;
            let s = this.get;
            return "POST" === e && (s = this.post), new Promise((e, i) => {
                s.call(this, t, (t, s, r) => {
                    t ? i(t) : e(s)
                })
            })
        }
        get(t) {
            return this.send.call(this.env, t)
        }
        post(t) {
            return this.send.call(this.env, t, "POST")
        }
    }
    return new class {
        constructor(t, e) {
            this.name = t, this.http = new s(this), this.data = null, this.dataFile = "box.dat", this.logs = [], this.isMute = !1, this.isNeedRewrite = !1, this.logSeparator = "\n", this.startTime = (new Date).getTime(), Object.assign(this, e), this.log("", `🔔${this.name}, 开始!`)
        }
        isNode() {
            return "undefined" != typeof module && !!module.exports
        }
        isQuanX() {
            return "undefined" != typeof $task
        }
        isSurge() {
            return "undefined" != typeof $httpClient && "undefined" == typeof $loon
        }
        isLoon() {
            return "undefined" != typeof $loon
        }
        toObj(t, e = null) {
            try {
                return JSON.parse(t)
            } catch {
                return e
            }
        }
        toStr(t, e = null) {
            try {
                return JSON.stringify(t)
            } catch {
                return e
            }
        }
        getjson(t, e) {
            let s = e;
            const i = this.getdata(t);
            if (i) try {
                s = JSON.parse(this.getdata(t))
            } catch {}
            return s
        }
        setjson(t, e) {
            try {
                return this.setdata(JSON.stringify(t), e)
            } catch {
                return !1
            }
        }
        getScript(t) {
            return new Promise(e => {
                this.get({
                    url: t
                }, (t, s, i) => e(i))
            })
        }
        runScript(t, e) {
            return new Promise(s => {
                let i = this.getdata("@chavy_boxjs_userCfgs.httpapi");
                i = i ? i.replace(/\n/g, "").trim() : i;
                let r = this.getdata("@chavy_boxjs_userCfgs.httpapi_timeout");
                r = r ? 1 * r : 20, r = e && e.timeout ? e.timeout : r;
                const [o, h] = i.split("@"), n = {
                    url: `http://${h}/v1/scripting/evaluate`,
                    body: {
                        script_text: t,
                        mock_type: "cron",
                        timeout: r
                    },
                    headers: {
                        "X-Key": o,
                        Accept: "*/*"
                    }
                };
                this.post(n, (t, e, i) => s(i))
            }).catch(t => this.logErr(t))
        }
        loaddata() {
            if (!this.isNode()) return {}; {
                this.fs = this.fs ? this.fs : require("fs"), this.path = this.path ? this.path : require("path");
                const t = this.path.resolve(this.dataFile),
                    e = this.path.resolve(process.cwd(), this.dataFile),
                    s = this.fs.existsSync(t),
                    i = !s && this.fs.existsSync(e);
                if (!s && !i) return {}; {
                    const i = s ? t : e;
                    try {
                        return JSON.parse(this.fs.readFileSync(i))
                    } catch (t) {
                        return {}
                    }
                }
            }
        }
        writedata() {
            if (this.isNode()) {
                this.fs = this.fs ? this.fs : require("fs"), this.path = this.path ? this.path : require("path");
                const t = this.path.resolve(this.dataFile),
                    e = this.path.resolve(process.cwd(), this.dataFile),
                    s = this.fs.existsSync(t),
                    i = !s && this.fs.existsSync(e),
                    r = JSON.stringify(this.data);
                s ? this.fs.writeFileSync(t, r) : i ? this.fs.writeFileSync(e, r) : this.fs.writeFileSync(t, r)
            }
        }
        lodash_get(t, e, s) {
            const i = e.replace(/\[(\d+)\]/g, ".$1").split(".");
            let r = t;
            for (const t of i)
                if (r = Object(r)[t], void 0 === r) return s;
            return r
        }
        lodash_set(t, e, s) {
            return Object(t) !== t ? t : (Array.isArray(e) || (e = e.toString().match(/[^.[\]]+/g) || []), e.slice(0, -1).reduce((t, s, i) => Object(t[s]) === t[s] ? t[s] : t[s] = Math.abs(e[i + 1]) >> 0 == +e[i + 1] ? [] : {}, t)[e[e.length - 1]] = s, t)
        }
        getdata(t) {
            let e = this.getval(t);
            if (/^@/.test(t)) {
                const [, s, i] = /^@(.*?)\.(.*?)$/.exec(t), r = s ? this.getval(s) : "";
                if (r) try {
                    const t = JSON.parse(r);
                    e = t ? this.lodash_get(t, i, "") : e
                } catch (t) {
                    e = ""
                }
            }
            return e
        }
        setdata(t, e) {
            let s = !1;
            if (/^@/.test(e)) {
                const [, i, r] = /^@(.*?)\.(.*?)$/.exec(e), o = this.getval(i), h = i ? "null" === o ? null : o || "{}" : "{}";
                try {
                    const e = JSON.parse(h);
                    this.lodash_set(e, r, t), s = this.setval(JSON.stringify(e), i)
                } catch (e) {
                    const o = {};
                    this.lodash_set(o, r, t), s = this.setval(JSON.stringify(o), i)
                }
            } else s = this.setval(t, e);
            return s
        }
        getval(t) {
            return this.isSurge() || this.isLoon() ? $persistentStore.read(t) : this.isQuanX() ? $prefs.valueForKey(t) : this.isNode() ? (this.data = this.loaddata(), this.data[t]) : this.data && this.data[t] || null
        }
        setval(t, e) {
            return this.isSurge() || this.isLoon() ? $persistentStore.write(t, e) : this.isQuanX() ? $prefs.setValueForKey(t, e) : this.isNode() ? (this.data = this.loaddata(), this.data[e] = t, this.writedata(), !0) : this.data && this.data[e] || null
        }
        initGotEnv(t) {
            this.got = this.got ? this.got : require("got"), this.cktough = this.cktough ? this.cktough : require("tough-cookie"), this.ckjar = this.ckjar ? this.ckjar : new this.cktough.CookieJar, t && (t.headers = t.headers ? t.headers : {}, void 0 === t.headers.Cookie && void 0 === t.cookieJar && (t.cookieJar = this.ckjar))
        }
        get(t, e = (() => {})) {
            t.headers && (delete t.headers["Content-Type"], delete t.headers["Content-Length"]), this.isSurge() || this.isLoon() ? (this.isSurge() && this.isNeedRewrite && (t.headers = t.headers || {}, Object.assign(t.headers, {
                "X-Surge-Skip-Scripting": !1
            })), $httpClient.get(t, (t, s, i) => {
                !t && s && (s.body = i, s.statusCode = s.status), e(t, s, i)
            })) : this.isQuanX() ? (this.isNeedRewrite && (t.opts = t.opts || {}, Object.assign(t.opts, {
                hints: !1
            })), $task.fetch(t).then(t => {
                const {
                    statusCode: s,
                    statusCode: i,
                    headers: r,
                    body: o
                } = t;
                e(null, {
                    status: s,
                    statusCode: i,
                    headers: r,
                    body: o
                }, o)
            }, t => e(t))) : this.isNode() && (this.initGotEnv(t), this.got(t).on("redirect", (t, e) => {
                try {
                    if (t.headers["set-cookie"]) {
                        const s = t.headers["set-cookie"].map(this.cktough.Cookie.parse).toString();
                        s && this.ckjar.setCookieSync(s, null), e.cookieJar = this.ckjar
                    }
                } catch (t) {
                    this.logErr(t)
                }
            }).then(t => {
                const {
                    statusCode: s,
                    statusCode: i,
                    headers: r,
                    body: o
                } = t;
                e(null, {
                    status: s,
                    statusCode: i,
                    headers: r,
                    body: o
                }, o)
            }, t => {
                const {
                    message: s,
                    response: i
                } = t;
                e(s, i, i && i.body)
            }))
        }
        post(t, e = (() => {})) {
            if (t.body && t.headers && !t.headers["Content-Type"] && (t.headers["Content-Type"] = "application/x-www-form-urlencoded"), t.headers && delete t.headers["Content-Length"], this.isSurge() || this.isLoon()) this.isSurge() && this.isNeedRewrite && (t.headers = t.headers || {}, Object.assign(t.headers, {
                "X-Surge-Skip-Scripting": !1
            })), $httpClient.post(t, (t, s, i) => {
                !t && s && (s.body = i, s.statusCode = s.status), e(t, s, i)
            });
            else if (this.isQuanX()) t.method = "POST", this.isNeedRewrite && (t.opts = t.opts || {}, Object.assign(t.opts, {
                hints: !1
            })), $task.fetch(t).then(t => {
                const {
                    statusCode: s,
                    statusCode: i,
                    headers: r,
                    body: o
                } = t;
                e(null, {
                    status: s,
                    statusCode: i,
                    headers: r,
                    body: o
                }, o)
            }, t => e(t));
            else if (this.isNode()) {
                this.initGotEnv(t);
                const {
                    url: s,
                    ...i
                } = t;
                this.got.post(s, i).then(t => {
                    const {
                        statusCode: s,
                        statusCode: i,
                        headers: r,
                        body: o
                    } = t;
                    e(null, {
                        status: s,
                        statusCode: i,
                        headers: r,
                        body: o
                    }, o)
                }, t => {
                    const {
                        message: s,
                        response: i
                    } = t;
                    e(s, i, i && i.body)
                })
            }
        }
        time(t, e = null) {
            const s = e ? new Date(e) : new Date;
            let i = {
                "M+": s.getMonth() + 1,
                "d+": s.getDate(),
                "H+": s.getHours(),
                "m+": s.getMinutes(),
                "s+": s.getSeconds(),
                "q+": Math.floor((s.getMonth() + 3) / 3),
                S: s.getMilliseconds()
            };
            /(y+)/.test(t) && (t = t.replace(RegExp.$1, (s.getFullYear() + "").substr(4 - RegExp.$1.length)));
            for (let e in i) new RegExp("(" + e + ")").test(t) && (t = t.replace(RegExp.$1, 1 == RegExp.$1.length ? i[e] : ("00" + i[e]).substr(("" + i[e]).length)));
            return t
        }
        msg(e = t, s = "", i = "", r) {
            const o = t => {
                if (!t) return t;
                if ("string" == typeof t) return this.isLoon() ? t : this.isQuanX() ? {
                    "open-url": t
                } : this.isSurge() ? {
                    url: t
                } : void 0;
                if ("object" == typeof t) {
                    if (this.isLoon()) {
                        let e = t.openUrl || t.url || t["open-url"],
                            s = t.mediaUrl || t["media-url"];
                        return {
                            openUrl: e,
                            mediaUrl: s
                        }
                    }
                    if (this.isQuanX()) {
                        let e = t["open-url"] || t.url || t.openUrl,
                            s = t["media-url"] || t.mediaUrl;
                        return {
                            "open-url": e,
                            "media-url": s
                        }
                    }
                    if (this.isSurge()) {
                        let e = t.url || t.openUrl || t["open-url"];
                        return {
                            url: e
                        }
                    }
                }
            };
            if (this.isMute || (this.isSurge() || this.isLoon() ? $notification.post(e, s, i, o(r)) : this.isQuanX() && $notify(e, s, i, o(r))), !this.isMuteLog) {
                let t = ["", "==============📣系统通知📣=============="];
                t.push(e), s && t.push(s), i && t.push(i), console.log(t.join("\n")), this.logs = this.logs.concat(t)
            }
        }
        log(...t) {
            t.length > 0 && (this.logs = [...this.logs, ...t]), console.log(t.join(this.logSeparator))
        }
        logErr(t, e) {
            const s = !this.isSurge() && !this.isQuanX() && !this.isLoon();
            s ? this.log("", `❗️${this.name}, 错误!`, t.stack) : this.log("", `❗️${this.name}, 错误!`, t)
        }
        wait(t) {
            return new Promise(e => setTimeout(e, t))
        }
        done(t = {}) {
            const e = (new Date).getTime(),
                s = (e - this.startTime) / 1e3;
            this.log("", `🔔${this.name}, 结束! 🕛 ${s} 秒`), this.log(), (this.isSurge() || this.isQuanX() || this.isLoon()) && $done(t)
        }
    }(t, e)
}